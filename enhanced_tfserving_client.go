package main

import (
	"bytes"
	"crypto/md5"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"time"
)

// 增强版 TensorFlow Serving 客户端
// 支持请求体保存和一致性检查
type EnhancedTFClient struct {
	baseURL     string
	httpClient  *http.Client
	logDir      string
	enableDiff  bool
}

// 请求记录结构
type RequestRecord struct {
	Timestamp   time.Time   `json:"timestamp"`
	URL         string      `json:"url"`
	Method      string      `json:"method"`
	Headers     http.Header `json:"headers"`
	RequestBody string      `json:"request_body"`
	StatusCode  int         `json:"status_code"`
	Response    string      `json:"response"`
	MD5Hash     string      `json:"md5_hash"`
	ModelName   string      `json:"model_name"`
	Version     string      `json:"version"`
}

// 预测请求和响应结构
type PredictRequest struct {
	Instances [][]float64 `json:"instances"`
}

type PredictResponse struct {
	Predictions [][]float64 `json:"predictions"`
}

type ModelStatus struct {
	ModelVersionStatus []struct {
		Version string `json:"version"`
		State   string `json:"state"`
		Status  struct {
			ErrorCode    string `json:"error_code"`
			ErrorMessage string `json:"error_message"`
		} `json:"status"`
	} `json:"model_version_status"`
}

// 创建增强客户端
func NewEnhancedTFClient(serverAddr, logDir string, enableDiff bool) *EnhancedTFClient {
	// 确保使用 REST API 端口 8501
	if serverAddr == "localhost:8500" {
		serverAddr = "localhost:8501"
	}

	baseURL := fmt.Sprintf("http://%s/v1/models", serverAddr)

	// 创建日志目录
	if logDir != "" {
		os.MkdirAll(logDir, 0755)
	}

	return &EnhancedTFClient{
		baseURL:    baseURL,
		logDir:     logDir,
		enableDiff: enableDiff,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// 保存请求记录
func (c *EnhancedTFClient) saveRequestRecord(record *RequestRecord) error {
	if c.logDir == "" {
		return nil
	}

	// 生成文件名：timestamp_model_hash.json
	filename := fmt.Sprintf("%s_%s_%s.json",
		record.Timestamp.Format("20060102_150405"),
		record.ModelName,
		record.MD5Hash[:8])

	filepath := filepath.Join(c.logDir, filename)

	data, err := json.MarshalIndent(record, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化记录失败: %v", err)
	}

	if err := os.WriteFile(filepath, data, 0644); err != nil {
		return fmt.Errorf("保存记录失败: %v", err)
	}

	log.Printf("📁 请求记录已保存: %s", filepath)
	return nil
}

// 计算请求体MD5哈希
func (c *EnhancedTFClient) calculateMD5(data []byte) string {
	hash := md5.Sum(data)
	return fmt.Sprintf("%x", hash)
}

// 检查模型状态
func (c *EnhancedTFClient) GetModelStatus(modelName string) (*ModelStatus, error) {
	url := fmt.Sprintf("%s/%s", c.baseURL, modelName)

	log.Printf("📋 检查模型状态: %s", url)

	resp, err := c.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}

	var status ModelStatus
	if err := json.Unmarshal(body, &status); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &status, nil
}

// 发送预测请求（增强版）
func (c *EnhancedTFClient) PredictWithLogging(modelName, version string, instances [][]float64) (*PredictResponse, error) {
	// 构造 URL
	var url string
	if version != "" {
		url = fmt.Sprintf("%s/%s/versions/%s:predict", c.baseURL, modelName, version)
	} else {
		url = fmt.Sprintf("%s/%s:predict", c.baseURL, modelName)
	}

	// 准备请求数据
	request := PredictRequest{
		Instances: instances,
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	// 计算请求体哈希
	md5Hash := c.calculateMD5(jsonData)

	log.Printf("🚀 发送预测请求: %s", url)
	log.Printf("📊 请求数据大小: %d bytes", len(jsonData))
	log.Printf("🔐 请求体MD5: %s", md5Hash)

	// 记录开始时间
	startTime := time.Now()

	// 发送 HTTP POST 请求
	resp, err := c.httpClient.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 创建请求记录
	record := &RequestRecord{
		Timestamp:   startTime,
		URL:         url,
		Method:      "POST",
		Headers:     resp.Header,
		RequestBody: string(jsonData),
		StatusCode:  resp.StatusCode,
		Response:    string(responseBody),
		MD5Hash:     md5Hash,
		ModelName:   modelName,
		Version:     version,
	}

	// 保存请求记录
	if err := c.saveRequestRecord(record); err != nil {
		log.Printf("⚠️ 保存请求记录失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应
	var response PredictResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	log.Printf("✅ 预测完成，耗时: %v", time.Since(startTime))
	return &response, nil
}

// 比较两个请求记录的一致性
func (c *EnhancedTFClient) CompareRequests(file1, file2 string) error {
	if !c.enableDiff {
		return nil
	}

	log.Printf("🔍 比较请求一致性: %s vs %s", file1, file2)

	// 读取两个文件
	data1, err := os.ReadFile(file1)
	if err != nil {
		return fmt.Errorf("读取文件1失败: %v", err)
	}

	data2, err := os.ReadFile(file2)
	if err != nil {
		return fmt.Errorf("读取文件2失败: %v", err)
	}

	var record1, record2 RequestRecord
	if err := json.Unmarshal(data1, &record1); err != nil {
		return fmt.Errorf("解析文件1失败: %v", err)
	}

	if err := json.Unmarshal(data2, &record2); err != nil {
		return fmt.Errorf("解析文件2失败: %v", err)
	}

	// 比较关键字段
	log.Println("📋 一致性检查结果:")
	log.Printf("  请求体MD5: %s vs %s", record1.MD5Hash, record2.MD5Hash)
	log.Printf("  模型名称: %s vs %s", record1.ModelName, record2.ModelName)
	log.Printf("  状态码: %d vs %d", record1.StatusCode, record2.StatusCode)

	if record1.MD5Hash == record2.MD5Hash {
		log.Println("✅ 请求体一致")
	} else {
		log.Println("❌ 请求体不一致")
	}

	if record1.StatusCode == record2.StatusCode {
		log.Println("✅ 响应状态一致")
	} else {
		log.Println("❌ 响应状态不一致")
	}

	return nil
}

// 列出日志目录中的请求记录
func (c *EnhancedTFClient) ListRequestRecords() ([]string, error) {
	if c.logDir == "" {
		return nil, fmt.Errorf("日志目录未设置")
	}

	files, err := filepath.Glob(filepath.Join(c.logDir, "*.json"))
	if err != nil {
		return nil, fmt.Errorf("读取日志目录失败: %v", err)
	}

	return files, nil
}

func main() {
	var serverAddr = flag.String("server", "localhost:8501", "TensorFlow Serving 地址")
	var modelName = flag.String("model", "dnn_winr_v1", "模型名称")
	var modelVersion = flag.String("version", "", "模型版本 (空表示最新版本)")
	var logDir = flag.String("log_dir", "./request_logs", "请求日志保存目录")
	var enableDiff = flag.Bool("enable_diff", true, "启用一致性检查")
	var compareFiles = flag.String("compare", "", "比较两个请求文件 (格式: file1,file2)")
	flag.Parse()

	log.Println("=== 增强版 TensorFlow Serving 客户端 ===")
	log.Printf("服务器: %s", *serverAddr)
	log.Printf("模型: %s", *modelName)
	if *modelVersion != "" {
		log.Printf("版本: %s", *modelVersion)
	}
	log.Printf("日志目录: %s", *logDir)

	// 创建增强客户端
	client := NewEnhancedTFClient(*serverAddr, *logDir, *enableDiff)

	// 如果指定了比较文件，执行比较后退出
	if *compareFiles != "" {
		files := strings.Split(*compareFiles, ",")
		if len(files) != 2 {
			log.Fatal("❌ 比较文件格式错误，应为: file1,file2")
		}
		if err := client.CompareRequests(strings.TrimSpace(files[0]), strings.TrimSpace(files[1])); err != nil {
			log.Fatalf("❌ 比较失败: %v", err)
		}
		return
	}
