// Code generated by running "go generate" in golang.org/x/text. DO NOT EDIT.

package charmap

import (
	"golang.org/x/text/encoding"
	"golang.org/x/text/encoding/internal/identifier"
)

// CodePage037 is the IBM Code Page 037 encoding.
var CodePage037 *Charmap = &codePage037

var codePage037 = <PERSON>rma<PERSON>{
	name:          "IBM Code Page 037",
	mib:           identifier.IBM037,
	asciiSuperset: false,
	low:           0x00,
	replacement:   0x3f,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x9c, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x86, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x97, 0x00}}, {2, [3]byte{0xc2, 0x8d, 0x00}},
		{2, [3]byte{0xc2, 0x8e, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x9d, 0x00}}, {2, [3]byte{0xc2, 0x85, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {2, [3]byte{0xc2, 0x87, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x92, 0x00}}, {2, [3]byte{0xc2, 0x8f, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x80, 0x00}}, {2, [3]byte{0xc2, 0x81, 0x00}},
		{2, [3]byte{0xc2, 0x82, 0x00}}, {2, [3]byte{0xc2, 0x83, 0x00}},
		{2, [3]byte{0xc2, 0x84, 0x00}}, {1, [3]byte{0x0a, 0x00, 0x00}},
		{1, [3]byte{0x17, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x88, 0x00}}, {2, [3]byte{0xc2, 0x89, 0x00}},
		{2, [3]byte{0xc2, 0x8a, 0x00}}, {2, [3]byte{0xc2, 0x8b, 0x00}},
		{2, [3]byte{0xc2, 0x8c, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x90, 0x00}}, {2, [3]byte{0xc2, 0x91, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {2, [3]byte{0xc2, 0x93, 0x00}},
		{2, [3]byte{0xc2, 0x94, 0x00}}, {2, [3]byte{0xc2, 0x95, 0x00}},
		{2, [3]byte{0xc2, 0x96, 0x00}}, {1, [3]byte{0x04, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x98, 0x00}}, {2, [3]byte{0xc2, 0x99, 0x00}},
		{2, [3]byte{0xc2, 0x9a, 0x00}}, {2, [3]byte{0xc2, 0x9b, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x9e, 0x00}}, {1, [3]byte{0x1a, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {2, [3]byte{0xc2, 0xa0, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc3, 0xa4, 0x00}},
		{2, [3]byte{0xc3, 0xa0, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa3, 0x00}}, {2, [3]byte{0xc3, 0xa5, 0x00}},
		{2, [3]byte{0xc3, 0xa7, 0x00}}, {2, [3]byte{0xc3, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {1, [3]byte{0x2e, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x28, 0x00, 0x00}},
		{1, [3]byte{0x2b, 0x00, 0x00}}, {1, [3]byte{0x7c, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xac, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{1, [3]byte{0x21, 0x00, 0x00}}, {1, [3]byte{0x24, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x3b, 0x00, 0x00}}, {2, [3]byte{0xc2, 0xac, 0x00}},
		{1, [3]byte{0x2d, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc3, 0x84, 0x00}},
		{2, [3]byte{0xc3, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x83, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x87, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {1, [3]byte{0x2c, 0x00, 0x00}},
		{1, [3]byte{0x25, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0xb8, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc3, 0x8f, 0x00}},
		{2, [3]byte{0xc3, 0x8c, 0x00}}, {1, [3]byte{0x60, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x3d, 0x00, 0x00}}, {1, [3]byte{0x22, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x98, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xab, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xb0, 0x00}}, {2, [3]byte{0xc3, 0xbd, 0x00}},
		{2, [3]byte{0xc3, 0xbe, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {1, [3]byte{0x6a, 0x00, 0x00}},
		{1, [3]byte{0x6b, 0x00, 0x00}}, {1, [3]byte{0x6c, 0x00, 0x00}},
		{1, [3]byte{0x6d, 0x00, 0x00}}, {1, [3]byte{0x6e, 0x00, 0x00}},
		{1, [3]byte{0x6f, 0x00, 0x00}}, {1, [3]byte{0x70, 0x00, 0x00}},
		{1, [3]byte{0x71, 0x00, 0x00}}, {1, [3]byte{0x72, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xba, 0x00}},
		{2, [3]byte{0xc3, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xb8, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc2, 0xa4, 0x00}},
		{2, [3]byte{0xc2, 0xb5, 0x00}}, {1, [3]byte{0x7e, 0x00, 0x00}},
		{1, [3]byte{0x73, 0x00, 0x00}}, {1, [3]byte{0x74, 0x00, 0x00}},
		{1, [3]byte{0x75, 0x00, 0x00}}, {1, [3]byte{0x76, 0x00, 0x00}},
		{1, [3]byte{0x77, 0x00, 0x00}}, {1, [3]byte{0x78, 0x00, 0x00}},
		{1, [3]byte{0x79, 0x00, 0x00}}, {1, [3]byte{0x7a, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xa1, 0x00}}, {2, [3]byte{0xc2, 0xbf, 0x00}},
		{2, [3]byte{0xc3, 0x90, 0x00}}, {2, [3]byte{0xc3, 0x9d, 0x00}},
		{2, [3]byte{0xc3, 0x9e, 0x00}}, {2, [3]byte{0xc2, 0xae, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa5, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc2, 0xa9, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xbc, 0x00}},
		{2, [3]byte{0xc2, 0xbd, 0x00}}, {2, [3]byte{0xc2, 0xbe, 0x00}},
		{1, [3]byte{0x5b, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xaf, 0x00}}, {2, [3]byte{0xc2, 0xa8, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{1, [3]byte{0x7b, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xad, 0x00}}, {2, [3]byte{0xc3, 0xb4, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb2, 0x00}},
		{2, [3]byte{0xc3, 0xb3, 0x00}}, {2, [3]byte{0xc3, 0xb5, 0x00}},
		{1, [3]byte{0x7d, 0x00, 0x00}}, {1, [3]byte{0x4a, 0x00, 0x00}},
		{1, [3]byte{0x4b, 0x00, 0x00}}, {1, [3]byte{0x4c, 0x00, 0x00}},
		{1, [3]byte{0x4d, 0x00, 0x00}}, {1, [3]byte{0x4e, 0x00, 0x00}},
		{1, [3]byte{0x4f, 0x00, 0x00}}, {1, [3]byte{0x50, 0x00, 0x00}},
		{1, [3]byte{0x51, 0x00, 0x00}}, {1, [3]byte{0x52, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xb9, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xbf, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{1, [3]byte{0x53, 0x00, 0x00}}, {1, [3]byte{0x54, 0x00, 0x00}},
		{1, [3]byte{0x55, 0x00, 0x00}}, {1, [3]byte{0x56, 0x00, 0x00}},
		{1, [3]byte{0x57, 0x00, 0x00}}, {1, [3]byte{0x58, 0x00, 0x00}},
		{1, [3]byte{0x59, 0x00, 0x00}}, {1, [3]byte{0x5a, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc3, 0x94, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x92, 0x00}},
		{2, [3]byte{0xc3, 0x93, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xb3, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc2, 0x9f, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x37000004, 0x2d000005, 0x2e000006, 0x2f000007,
		0x16000008, 0x05000009, 0x2500000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x3c000014, 0x3d000015, 0x32000016, 0x26000017,
		0x18000018, 0x19000019, 0x3f00001a, 0x2700001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x40000020, 0x5a000021, 0x7f000022, 0x7b000023, 0x5b000024, 0x6c000025, 0x50000026, 0x7d000027,
		0x4d000028, 0x5d000029, 0x5c00002a, 0x4e00002b, 0x6b00002c, 0x6000002d, 0x4b00002e, 0x6100002f,
		0xf0000030, 0xf1000031, 0xf2000032, 0xf3000033, 0xf4000034, 0xf5000035, 0xf6000036, 0xf7000037,
		0xf8000038, 0xf9000039, 0x7a00003a, 0x5e00003b, 0x4c00003c, 0x7e00003d, 0x6e00003e, 0x6f00003f,
		0x7c000040, 0xc1000041, 0xc2000042, 0xc3000043, 0xc4000044, 0xc5000045, 0xc6000046, 0xc7000047,
		0xc8000048, 0xc9000049, 0xd100004a, 0xd200004b, 0xd300004c, 0xd400004d, 0xd500004e, 0xd600004f,
		0xd7000050, 0xd8000051, 0xd9000052, 0xe2000053, 0xe3000054, 0xe4000055, 0xe5000056, 0xe6000057,
		0xe7000058, 0xe8000059, 0xe900005a, 0xba00005b, 0xe000005c, 0xbb00005d, 0xb000005e, 0x6d00005f,
		0x79000060, 0x81000061, 0x82000062, 0x83000063, 0x84000064, 0x85000065, 0x86000066, 0x87000067,
		0x88000068, 0x89000069, 0x9100006a, 0x9200006b, 0x9300006c, 0x9400006d, 0x9500006e, 0x9600006f,
		0x97000070, 0x98000071, 0x99000072, 0xa2000073, 0xa3000074, 0xa4000075, 0xa5000076, 0xa6000077,
		0xa7000078, 0xa8000079, 0xa900007a, 0xc000007b, 0x4f00007c, 0xd000007d, 0xa100007e, 0x0700007f,
		0x20000080, 0x21000081, 0x22000082, 0x23000083, 0x24000084, 0x15000085, 0x06000086, 0x17000087,
		0x28000088, 0x29000089, 0x2a00008a, 0x2b00008b, 0x2c00008c, 0x0900008d, 0x0a00008e, 0x1b00008f,
		0x30000090, 0x31000091, 0x1a000092, 0x33000093, 0x34000094, 0x35000095, 0x36000096, 0x08000097,
		0x38000098, 0x39000099, 0x3a00009a, 0x3b00009b, 0x0400009c, 0x1400009d, 0x3e00009e, 0xff00009f,
		0x410000a0, 0xaa0000a1, 0x4a0000a2, 0xb10000a3, 0x9f0000a4, 0xb20000a5, 0x6a0000a6, 0xb50000a7,
		0xbd0000a8, 0xb40000a9, 0x9a0000aa, 0x8a0000ab, 0x5f0000ac, 0xca0000ad, 0xaf0000ae, 0xbc0000af,
		0x900000b0, 0x8f0000b1, 0xea0000b2, 0xfa0000b3, 0xbe0000b4, 0xa00000b5, 0xb60000b6, 0xb30000b7,
		0x9d0000b8, 0xda0000b9, 0x9b0000ba, 0x8b0000bb, 0xb70000bc, 0xb80000bd, 0xb90000be, 0xab0000bf,
		0x640000c0, 0x650000c1, 0x620000c2, 0x660000c3, 0x630000c4, 0x670000c5, 0x9e0000c6, 0x680000c7,
		0x740000c8, 0x710000c9, 0x720000ca, 0x730000cb, 0x780000cc, 0x750000cd, 0x760000ce, 0x770000cf,
		0xac0000d0, 0x690000d1, 0xed0000d2, 0xee0000d3, 0xeb0000d4, 0xef0000d5, 0xec0000d6, 0xbf0000d7,
		0x800000d8, 0xfd0000d9, 0xfe0000da, 0xfb0000db, 0xfc0000dc, 0xad0000dd, 0xae0000de, 0x590000df,
		0x440000e0, 0x450000e1, 0x420000e2, 0x460000e3, 0x430000e4, 0x470000e5, 0x9c0000e6, 0x480000e7,
		0x540000e8, 0x510000e9, 0x520000ea, 0x530000eb, 0x580000ec, 0x550000ed, 0x560000ee, 0x570000ef,
		0x8c0000f0, 0x490000f1, 0xcd0000f2, 0xce0000f3, 0xcb0000f4, 0xcf0000f5, 0xcc0000f6, 0xe10000f7,
		0x700000f8, 0xdd0000f9, 0xde0000fa, 0xdb0000fb, 0xdc0000fc, 0x8d0000fd, 0x8e0000fe, 0xdf0000ff,
	},
}

// CodePage437 is the IBM Code Page 437 encoding.
var CodePage437 *Charmap = &codePage437

var codePage437 = Charmap{
	name:          "IBM Code Page 437",
	mib:           identifier.PC8CodePage437,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x87, 0x00}}, {2, [3]byte{0xc3, 0xbc, 0x00}},
		{2, [3]byte{0xc3, 0xa9, 0x00}}, {2, [3]byte{0xc3, 0xa2, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa0, 0x00}},
		{2, [3]byte{0xc3, 0xa5, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xac, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x89, 0x00}}, {2, [3]byte{0xc3, 0xa6, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc3, 0xb4, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb2, 0x00}},
		{2, [3]byte{0xc3, 0xbb, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xbf, 0x00}}, {2, [3]byte{0xc3, 0x96, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc2, 0xa2, 0x00}},
		{2, [3]byte{0xc2, 0xa3, 0x00}}, {2, [3]byte{0xc2, 0xa5, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xa7}}, {2, [3]byte{0xc6, 0x92, 0x00}},
		{2, [3]byte{0xc3, 0xa1, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xb3, 0x00}}, {2, [3]byte{0xc3, 0xba, 0x00}},
		{2, [3]byte{0xc3, 0xb1, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xba, 0x00}},
		{2, [3]byte{0xc2, 0xbf, 0x00}}, {3, [3]byte{0xe2, 0x8c, 0x90}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xa1, 0x00}},
		{2, [3]byte{0xc2, 0xab, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0x91}}, {3, [3]byte{0xe2, 0x96, 0x92}},
		{3, [3]byte{0xe2, 0x96, 0x93}}, {3, [3]byte{0xe2, 0x94, 0x82}},
		{3, [3]byte{0xe2, 0x94, 0xa4}}, {3, [3]byte{0xe2, 0x95, 0xa1}},
		{3, [3]byte{0xe2, 0x95, 0xa2}}, {3, [3]byte{0xe2, 0x95, 0x96}},
		{3, [3]byte{0xe2, 0x95, 0x95}}, {3, [3]byte{0xe2, 0x95, 0xa3}},
		{3, [3]byte{0xe2, 0x95, 0x91}}, {3, [3]byte{0xe2, 0x95, 0x97}},
		{3, [3]byte{0xe2, 0x95, 0x9d}}, {3, [3]byte{0xe2, 0x95, 0x9c}},
		{3, [3]byte{0xe2, 0x95, 0x9b}}, {3, [3]byte{0xe2, 0x94, 0x90}},
		{3, [3]byte{0xe2, 0x94, 0x94}}, {3, [3]byte{0xe2, 0x94, 0xb4}},
		{3, [3]byte{0xe2, 0x94, 0xac}}, {3, [3]byte{0xe2, 0x94, 0x9c}},
		{3, [3]byte{0xe2, 0x94, 0x80}}, {3, [3]byte{0xe2, 0x94, 0xbc}},
		{3, [3]byte{0xe2, 0x95, 0x9e}}, {3, [3]byte{0xe2, 0x95, 0x9f}},
		{3, [3]byte{0xe2, 0x95, 0x9a}}, {3, [3]byte{0xe2, 0x95, 0x94}},
		{3, [3]byte{0xe2, 0x95, 0xa9}}, {3, [3]byte{0xe2, 0x95, 0xa6}},
		{3, [3]byte{0xe2, 0x95, 0xa0}}, {3, [3]byte{0xe2, 0x95, 0x90}},
		{3, [3]byte{0xe2, 0x95, 0xac}}, {3, [3]byte{0xe2, 0x95, 0xa7}},
		{3, [3]byte{0xe2, 0x95, 0xa8}}, {3, [3]byte{0xe2, 0x95, 0xa4}},
		{3, [3]byte{0xe2, 0x95, 0xa5}}, {3, [3]byte{0xe2, 0x95, 0x99}},
		{3, [3]byte{0xe2, 0x95, 0x98}}, {3, [3]byte{0xe2, 0x95, 0x92}},
		{3, [3]byte{0xe2, 0x95, 0x93}}, {3, [3]byte{0xe2, 0x95, 0xab}},
		{3, [3]byte{0xe2, 0x95, 0xaa}}, {3, [3]byte{0xe2, 0x94, 0x98}},
		{3, [3]byte{0xe2, 0x94, 0x8c}}, {3, [3]byte{0xe2, 0x96, 0x88}},
		{3, [3]byte{0xe2, 0x96, 0x84}}, {3, [3]byte{0xe2, 0x96, 0x8c}},
		{3, [3]byte{0xe2, 0x96, 0x90}}, {3, [3]byte{0xe2, 0x96, 0x80}},
		{2, [3]byte{0xce, 0xb1, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xce, 0x93, 0x00}}, {2, [3]byte{0xcf, 0x80, 0x00}},
		{2, [3]byte{0xce, 0xa3, 0x00}}, {2, [3]byte{0xcf, 0x83, 0x00}},
		{2, [3]byte{0xc2, 0xb5, 0x00}}, {2, [3]byte{0xcf, 0x84, 0x00}},
		{2, [3]byte{0xce, 0xa6, 0x00}}, {2, [3]byte{0xce, 0x98, 0x00}},
		{2, [3]byte{0xce, 0xa9, 0x00}}, {2, [3]byte{0xce, 0xb4, 0x00}},
		{3, [3]byte{0xe2, 0x88, 0x9e}}, {2, [3]byte{0xcf, 0x86, 0x00}},
		{2, [3]byte{0xce, 0xb5, 0x00}}, {3, [3]byte{0xe2, 0x88, 0xa9}},
		{3, [3]byte{0xe2, 0x89, 0xa1}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{3, [3]byte{0xe2, 0x89, 0xa5}}, {3, [3]byte{0xe2, 0x89, 0xa4}},
		{3, [3]byte{0xe2, 0x8c, 0xa0}}, {3, [3]byte{0xe2, 0x8c, 0xa1}},
		{2, [3]byte{0xc3, 0xb7, 0x00}}, {3, [3]byte{0xe2, 0x89, 0x88}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {3, [3]byte{0xe2, 0x88, 0x99}},
		{2, [3]byte{0xc2, 0xb7, 0x00}}, {3, [3]byte{0xe2, 0x88, 0x9a}},
		{3, [3]byte{0xe2, 0x81, 0xbf}}, {2, [3]byte{0xc2, 0xb2, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0xa0}}, {2, [3]byte{0xc2, 0xa0, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xff0000a0, 0xad0000a1, 0x9b0000a2, 0x9c0000a3, 0x9d0000a5, 0xa60000aa, 0xae0000ab, 0xaa0000ac,
		0xf80000b0, 0xf10000b1, 0xfd0000b2, 0xe60000b5, 0xfa0000b7, 0xa70000ba, 0xaf0000bb, 0xac0000bc,
		0xab0000bd, 0xa80000bf, 0x8e0000c4, 0x8f0000c5, 0x920000c6, 0x800000c7, 0x900000c9, 0xa50000d1,
		0x990000d6, 0x9a0000dc, 0xe10000df, 0x850000e0, 0xa00000e1, 0x830000e2, 0x840000e4, 0x860000e5,
		0x910000e6, 0x870000e7, 0x8a0000e8, 0x820000e9, 0x880000ea, 0x890000eb, 0x8d0000ec, 0xa10000ed,
		0x8c0000ee, 0x8b0000ef, 0xa40000f1, 0x950000f2, 0xa20000f3, 0x930000f4, 0x940000f6, 0xf60000f7,
		0x970000f9, 0xa30000fa, 0x960000fb, 0x810000fc, 0x980000ff, 0x9f000192, 0xe2000393, 0xe9000398,
		0xe40003a3, 0xe80003a6, 0xea0003a9, 0xe00003b1, 0xeb0003b4, 0xee0003b5, 0xe30003c0, 0xe50003c3,
		0xe70003c4, 0xed0003c6, 0xfc00207f, 0x9e0020a7, 0xf9002219, 0xfb00221a, 0xec00221e, 0xef002229,
		0xf7002248, 0xf0002261, 0xf3002264, 0xf2002265, 0xa9002310, 0xf4002320, 0xf5002321, 0xc4002500,
		0xb3002502, 0xda00250c, 0xbf002510, 0xc0002514, 0xd9002518, 0xc300251c, 0xb4002524, 0xc200252c,
		0xc1002534, 0xc500253c, 0xcd002550, 0xba002551, 0xd5002552, 0xd6002553, 0xc9002554, 0xb8002555,
		0xb7002556, 0xbb002557, 0xd4002558, 0xd3002559, 0xc800255a, 0xbe00255b, 0xbd00255c, 0xbc00255d,
		0xc600255e, 0xc700255f, 0xcc002560, 0xb5002561, 0xb6002562, 0xb9002563, 0xd1002564, 0xd2002565,
		0xcb002566, 0xcf002567, 0xd0002568, 0xca002569, 0xd800256a, 0xd700256b, 0xce00256c, 0xdf002580,
		0xdc002584, 0xdb002588, 0xdd00258c, 0xde002590, 0xb0002591, 0xb1002592, 0xb2002593, 0xfe0025a0,
	},
}

// CodePage850 is the IBM Code Page 850 encoding.
var CodePage850 *Charmap = &codePage850

var codePage850 = Charmap{
	name:          "IBM Code Page 850",
	mib:           identifier.PC850Multilingual,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x87, 0x00}}, {2, [3]byte{0xc3, 0xbc, 0x00}},
		{2, [3]byte{0xc3, 0xa9, 0x00}}, {2, [3]byte{0xc3, 0xa2, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa0, 0x00}},
		{2, [3]byte{0xc3, 0xa5, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xac, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x89, 0x00}}, {2, [3]byte{0xc3, 0xa6, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc3, 0xb4, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb2, 0x00}},
		{2, [3]byte{0xc3, 0xbb, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xbf, 0x00}}, {2, [3]byte{0xc3, 0x96, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0xb8, 0x00}},
		{2, [3]byte{0xc2, 0xa3, 0x00}}, {2, [3]byte{0xc3, 0x98, 0x00}},
		{2, [3]byte{0xc3, 0x97, 0x00}}, {2, [3]byte{0xc6, 0x92, 0x00}},
		{2, [3]byte{0xc3, 0xa1, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xb3, 0x00}}, {2, [3]byte{0xc3, 0xba, 0x00}},
		{2, [3]byte{0xc3, 0xb1, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xba, 0x00}},
		{2, [3]byte{0xc2, 0xbf, 0x00}}, {2, [3]byte{0xc2, 0xae, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xa1, 0x00}},
		{2, [3]byte{0xc2, 0xab, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0x91}}, {3, [3]byte{0xe2, 0x96, 0x92}},
		{3, [3]byte{0xe2, 0x96, 0x93}}, {3, [3]byte{0xe2, 0x94, 0x82}},
		{3, [3]byte{0xe2, 0x94, 0xa4}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc3, 0x80, 0x00}},
		{2, [3]byte{0xc2, 0xa9, 0x00}}, {3, [3]byte{0xe2, 0x95, 0xa3}},
		{3, [3]byte{0xe2, 0x95, 0x91}}, {3, [3]byte{0xe2, 0x95, 0x97}},
		{3, [3]byte{0xe2, 0x95, 0x9d}}, {2, [3]byte{0xc2, 0xa2, 0x00}},
		{2, [3]byte{0xc2, 0xa5, 0x00}}, {3, [3]byte{0xe2, 0x94, 0x90}},
		{3, [3]byte{0xe2, 0x94, 0x94}}, {3, [3]byte{0xe2, 0x94, 0xb4}},
		{3, [3]byte{0xe2, 0x94, 0xac}}, {3, [3]byte{0xe2, 0x94, 0x9c}},
		{3, [3]byte{0xe2, 0x94, 0x80}}, {3, [3]byte{0xe2, 0x94, 0xbc}},
		{2, [3]byte{0xc3, 0xa3, 0x00}}, {2, [3]byte{0xc3, 0x83, 0x00}},
		{3, [3]byte{0xe2, 0x95, 0x9a}}, {3, [3]byte{0xe2, 0x95, 0x94}},
		{3, [3]byte{0xe2, 0x95, 0xa9}}, {3, [3]byte{0xe2, 0x95, 0xa6}},
		{3, [3]byte{0xe2, 0x95, 0xa0}}, {3, [3]byte{0xe2, 0x95, 0x90}},
		{3, [3]byte{0xe2, 0x95, 0xac}}, {2, [3]byte{0xc2, 0xa4, 0x00}},
		{2, [3]byte{0xc3, 0xb0, 0x00}}, {2, [3]byte{0xc3, 0x90, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {2, [3]byte{0xc4, 0xb1, 0x00}},
		{2, [3]byte{0xc3, 0x8d, 0x00}}, {2, [3]byte{0xc3, 0x8e, 0x00}},
		{2, [3]byte{0xc3, 0x8f, 0x00}}, {3, [3]byte{0xe2, 0x94, 0x98}},
		{3, [3]byte{0xe2, 0x94, 0x8c}}, {3, [3]byte{0xe2, 0x96, 0x88}},
		{3, [3]byte{0xe2, 0x96, 0x84}}, {2, [3]byte{0xc2, 0xa6, 0x00}},
		{2, [3]byte{0xc3, 0x8c, 0x00}}, {3, [3]byte{0xe2, 0x96, 0x80}},
		{2, [3]byte{0xc3, 0x93, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc3, 0x92, 0x00}},
		{2, [3]byte{0xc3, 0xb5, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{2, [3]byte{0xc2, 0xb5, 0x00}}, {2, [3]byte{0xc3, 0xbe, 0x00}},
		{2, [3]byte{0xc3, 0x9e, 0x00}}, {2, [3]byte{0xc3, 0x9a, 0x00}},
		{2, [3]byte{0xc3, 0x9b, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0xbd, 0x00}}, {2, [3]byte{0xc3, 0x9d, 0x00}},
		{2, [3]byte{0xc2, 0xaf, 0x00}}, {2, [3]byte{0xc2, 0xb4, 0x00}},
		{2, [3]byte{0xc2, 0xad, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x97}}, {2, [3]byte{0xc2, 0xbe, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xb7, 0x00}}, {2, [3]byte{0xc2, 0xb8, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xa8, 0x00}},
		{2, [3]byte{0xc2, 0xb7, 0x00}}, {2, [3]byte{0xc2, 0xb9, 0x00}},
		{2, [3]byte{0xc2, 0xb3, 0x00}}, {2, [3]byte{0xc2, 0xb2, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0xa0}}, {2, [3]byte{0xc2, 0xa0, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xff0000a0, 0xad0000a1, 0xbd0000a2, 0x9c0000a3, 0xcf0000a4, 0xbe0000a5, 0xdd0000a6, 0xf50000a7,
		0xf90000a8, 0xb80000a9, 0xa60000aa, 0xae0000ab, 0xaa0000ac, 0xf00000ad, 0xa90000ae, 0xee0000af,
		0xf80000b0, 0xf10000b1, 0xfd0000b2, 0xfc0000b3, 0xef0000b4, 0xe60000b5, 0xf40000b6, 0xfa0000b7,
		0xf70000b8, 0xfb0000b9, 0xa70000ba, 0xaf0000bb, 0xac0000bc, 0xab0000bd, 0xf30000be, 0xa80000bf,
		0xb70000c0, 0xb50000c1, 0xb60000c2, 0xc70000c3, 0x8e0000c4, 0x8f0000c5, 0x920000c6, 0x800000c7,
		0xd40000c8, 0x900000c9, 0xd20000ca, 0xd30000cb, 0xde0000cc, 0xd60000cd, 0xd70000ce, 0xd80000cf,
		0xd10000d0, 0xa50000d1, 0xe30000d2, 0xe00000d3, 0xe20000d4, 0xe50000d5, 0x990000d6, 0x9e0000d7,
		0x9d0000d8, 0xeb0000d9, 0xe90000da, 0xea0000db, 0x9a0000dc, 0xed0000dd, 0xe80000de, 0xe10000df,
		0x850000e0, 0xa00000e1, 0x830000e2, 0xc60000e3, 0x840000e4, 0x860000e5, 0x910000e6, 0x870000e7,
		0x8a0000e8, 0x820000e9, 0x880000ea, 0x890000eb, 0x8d0000ec, 0xa10000ed, 0x8c0000ee, 0x8b0000ef,
		0xd00000f0, 0xa40000f1, 0x950000f2, 0xa20000f3, 0x930000f4, 0xe40000f5, 0x940000f6, 0xf60000f7,
		0x9b0000f8, 0x970000f9, 0xa30000fa, 0x960000fb, 0x810000fc, 0xec0000fd, 0xe70000fe, 0x980000ff,
		0xd5000131, 0x9f000192, 0xf2002017, 0xc4002500, 0xb3002502, 0xda00250c, 0xbf002510, 0xc0002514,
		0xd9002518, 0xc300251c, 0xb4002524, 0xc200252c, 0xc1002534, 0xc500253c, 0xcd002550, 0xba002551,
		0xc9002554, 0xbb002557, 0xc800255a, 0xbc00255d, 0xcc002560, 0xb9002563, 0xcb002566, 0xca002569,
		0xce00256c, 0xdf002580, 0xdc002584, 0xdb002588, 0xb0002591, 0xb1002592, 0xb2002593, 0xfe0025a0,
	},
}

// CodePage852 is the IBM Code Page 852 encoding.
var CodePage852 *Charmap = &codePage852

var codePage852 = Charmap{
	name:          "IBM Code Page 852",
	mib:           identifier.PCp852,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x87, 0x00}}, {2, [3]byte{0xc3, 0xbc, 0x00}},
		{2, [3]byte{0xc3, 0xa9, 0x00}}, {2, [3]byte{0xc3, 0xa2, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc5, 0xaf, 0x00}},
		{2, [3]byte{0xc4, 0x87, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc5, 0x82, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc5, 0x90, 0x00}}, {2, [3]byte{0xc5, 0x91, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc5, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc4, 0x86, 0x00}},
		{2, [3]byte{0xc3, 0x89, 0x00}}, {2, [3]byte{0xc4, 0xb9, 0x00}},
		{2, [3]byte{0xc4, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xb4, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc4, 0xbd, 0x00}},
		{2, [3]byte{0xc4, 0xbe, 0x00}}, {2, [3]byte{0xc5, 0x9a, 0x00}},
		{2, [3]byte{0xc5, 0x9b, 0x00}}, {2, [3]byte{0xc3, 0x96, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc5, 0xa4, 0x00}},
		{2, [3]byte{0xc5, 0xa5, 0x00}}, {2, [3]byte{0xc5, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x97, 0x00}}, {2, [3]byte{0xc4, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0xa1, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xb3, 0x00}}, {2, [3]byte{0xc3, 0xba, 0x00}},
		{2, [3]byte{0xc4, 0x84, 0x00}}, {2, [3]byte{0xc4, 0x85, 0x00}},
		{2, [3]byte{0xc5, 0xbd, 0x00}}, {2, [3]byte{0xc5, 0xbe, 0x00}},
		{2, [3]byte{0xc4, 0x98, 0x00}}, {2, [3]byte{0xc4, 0x99, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc5, 0xba, 0x00}},
		{2, [3]byte{0xc4, 0x8c, 0x00}}, {2, [3]byte{0xc5, 0x9f, 0x00}},
		{2, [3]byte{0xc2, 0xab, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0x91}}, {3, [3]byte{0xe2, 0x96, 0x92}},
		{3, [3]byte{0xe2, 0x96, 0x93}}, {3, [3]byte{0xe2, 0x94, 0x82}},
		{3, [3]byte{0xe2, 0x94, 0xa4}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc4, 0x9a, 0x00}},
		{2, [3]byte{0xc5, 0x9e, 0x00}}, {3, [3]byte{0xe2, 0x95, 0xa3}},
		{3, [3]byte{0xe2, 0x95, 0x91}}, {3, [3]byte{0xe2, 0x95, 0x97}},
		{3, [3]byte{0xe2, 0x95, 0x9d}}, {2, [3]byte{0xc5, 0xbb, 0x00}},
		{2, [3]byte{0xc5, 0xbc, 0x00}}, {3, [3]byte{0xe2, 0x94, 0x90}},
		{3, [3]byte{0xe2, 0x94, 0x94}}, {3, [3]byte{0xe2, 0x94, 0xb4}},
		{3, [3]byte{0xe2, 0x94, 0xac}}, {3, [3]byte{0xe2, 0x94, 0x9c}},
		{3, [3]byte{0xe2, 0x94, 0x80}}, {3, [3]byte{0xe2, 0x94, 0xbc}},
		{2, [3]byte{0xc4, 0x82, 0x00}}, {2, [3]byte{0xc4, 0x83, 0x00}},
		{3, [3]byte{0xe2, 0x95, 0x9a}}, {3, [3]byte{0xe2, 0x95, 0x94}},
		{3, [3]byte{0xe2, 0x95, 0xa9}}, {3, [3]byte{0xe2, 0x95, 0xa6}},
		{3, [3]byte{0xe2, 0x95, 0xa0}}, {3, [3]byte{0xe2, 0x95, 0x90}},
		{3, [3]byte{0xe2, 0x95, 0xac}}, {2, [3]byte{0xc2, 0xa4, 0x00}},
		{2, [3]byte{0xc4, 0x91, 0x00}}, {2, [3]byte{0xc4, 0x90, 0x00}},
		{2, [3]byte{0xc4, 0x8e, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc4, 0x8f, 0x00}}, {2, [3]byte{0xc5, 0x87, 0x00}},
		{2, [3]byte{0xc3, 0x8d, 0x00}}, {2, [3]byte{0xc3, 0x8e, 0x00}},
		{2, [3]byte{0xc4, 0x9b, 0x00}}, {3, [3]byte{0xe2, 0x94, 0x98}},
		{3, [3]byte{0xe2, 0x94, 0x8c}}, {3, [3]byte{0xe2, 0x96, 0x88}},
		{3, [3]byte{0xe2, 0x96, 0x84}}, {2, [3]byte{0xc5, 0xa2, 0x00}},
		{2, [3]byte{0xc5, 0xae, 0x00}}, {3, [3]byte{0xe2, 0x96, 0x80}},
		{2, [3]byte{0xc3, 0x93, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc5, 0x83, 0x00}},
		{2, [3]byte{0xc5, 0x84, 0x00}}, {2, [3]byte{0xc5, 0x88, 0x00}},
		{2, [3]byte{0xc5, 0xa0, 0x00}}, {2, [3]byte{0xc5, 0xa1, 0x00}},
		{2, [3]byte{0xc5, 0x94, 0x00}}, {2, [3]byte{0xc3, 0x9a, 0x00}},
		{2, [3]byte{0xc5, 0x95, 0x00}}, {2, [3]byte{0xc5, 0xb0, 0x00}},
		{2, [3]byte{0xc3, 0xbd, 0x00}}, {2, [3]byte{0xc3, 0x9d, 0x00}},
		{2, [3]byte{0xc5, 0xa3, 0x00}}, {2, [3]byte{0xc2, 0xb4, 0x00}},
		{2, [3]byte{0xc2, 0xad, 0x00}}, {2, [3]byte{0xcb, 0x9d, 0x00}},
		{2, [3]byte{0xcb, 0x9b, 0x00}}, {2, [3]byte{0xcb, 0x87, 0x00}},
		{2, [3]byte{0xcb, 0x98, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xb7, 0x00}}, {2, [3]byte{0xc2, 0xb8, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xa8, 0x00}},
		{2, [3]byte{0xcb, 0x99, 0x00}}, {2, [3]byte{0xc5, 0xb1, 0x00}},
		{2, [3]byte{0xc5, 0x98, 0x00}}, {2, [3]byte{0xc5, 0x99, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0xa0}}, {2, [3]byte{0xc2, 0xa0, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xff0000a0, 0xcf0000a4, 0xf50000a7, 0xf90000a8, 0xae0000ab, 0xaa0000ac, 0xf00000ad, 0xf80000b0,
		0xef0000b4, 0xf70000b8, 0xaf0000bb, 0xb50000c1, 0xb60000c2, 0x8e0000c4, 0x800000c7, 0x900000c9,
		0xd30000cb, 0xd60000cd, 0xd70000ce, 0xe00000d3, 0xe20000d4, 0x990000d6, 0x9e0000d7, 0xe90000da,
		0x9a0000dc, 0xed0000dd, 0xe10000df, 0xa00000e1, 0x830000e2, 0x840000e4, 0x870000e7, 0x820000e9,
		0x890000eb, 0xa10000ed, 0x8c0000ee, 0xa20000f3, 0x930000f4, 0x940000f6, 0xf60000f7, 0xa30000fa,
		0x810000fc, 0xec0000fd, 0xc6000102, 0xc7000103, 0xa4000104, 0xa5000105, 0x8f000106, 0x86000107,
		0xac00010c, 0x9f00010d, 0xd200010e, 0xd400010f, 0xd1000110, 0xd0000111, 0xa8000118, 0xa9000119,
		0xb700011a, 0xd800011b, 0x91000139, 0x9200013a, 0x9500013d, 0x9600013e, 0x9d000141, 0x88000142,
		0xe3000143, 0xe4000144, 0xd5000147, 0xe5000148, 0x8a000150, 0x8b000151, 0xe8000154, 0xea000155,
		0xfc000158, 0xfd000159, 0x9700015a, 0x9800015b, 0xb800015e, 0xad00015f, 0xe6000160, 0xe7000161,
		0xdd000162, 0xee000163, 0x9b000164, 0x9c000165, 0xde00016e, 0x8500016f, 0xeb000170, 0xfb000171,
		0x8d000179, 0xab00017a, 0xbd00017b, 0xbe00017c, 0xa600017d, 0xa700017e, 0xf30002c7, 0xf40002d8,
		0xfa0002d9, 0xf20002db, 0xf10002dd, 0xc4002500, 0xb3002502, 0xda00250c, 0xbf002510, 0xc0002514,
		0xd9002518, 0xc300251c, 0xb4002524, 0xc200252c, 0xc1002534, 0xc500253c, 0xcd002550, 0xba002551,
		0xc9002554, 0xbb002557, 0xc800255a, 0xbc00255d, 0xcc002560, 0xb9002563, 0xcb002566, 0xca002569,
		0xce00256c, 0xdf002580, 0xdc002584, 0xdb002588, 0xb0002591, 0xb1002592, 0xb2002593, 0xfe0025a0,
	},
}

// CodePage855 is the IBM Code Page 855 encoding.
var CodePage855 *Charmap = &codePage855

var codePage855 = Charmap{
	name:          "IBM Code Page 855",
	mib:           identifier.IBM855,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xd1, 0x92, 0x00}}, {2, [3]byte{0xd0, 0x82, 0x00}},
		{2, [3]byte{0xd1, 0x93, 0x00}}, {2, [3]byte{0xd0, 0x83, 0x00}},
		{2, [3]byte{0xd1, 0x91, 0x00}}, {2, [3]byte{0xd0, 0x81, 0x00}},
		{2, [3]byte{0xd1, 0x94, 0x00}}, {2, [3]byte{0xd0, 0x84, 0x00}},
		{2, [3]byte{0xd1, 0x95, 0x00}}, {2, [3]byte{0xd0, 0x85, 0x00}},
		{2, [3]byte{0xd1, 0x96, 0x00}}, {2, [3]byte{0xd0, 0x86, 0x00}},
		{2, [3]byte{0xd1, 0x97, 0x00}}, {2, [3]byte{0xd0, 0x87, 0x00}},
		{2, [3]byte{0xd1, 0x98, 0x00}}, {2, [3]byte{0xd0, 0x88, 0x00}},
		{2, [3]byte{0xd1, 0x99, 0x00}}, {2, [3]byte{0xd0, 0x89, 0x00}},
		{2, [3]byte{0xd1, 0x9a, 0x00}}, {2, [3]byte{0xd0, 0x8a, 0x00}},
		{2, [3]byte{0xd1, 0x9b, 0x00}}, {2, [3]byte{0xd0, 0x8b, 0x00}},
		{2, [3]byte{0xd1, 0x9c, 0x00}}, {2, [3]byte{0xd0, 0x8c, 0x00}},
		{2, [3]byte{0xd1, 0x9e, 0x00}}, {2, [3]byte{0xd0, 0x8e, 0x00}},
		{2, [3]byte{0xd1, 0x9f, 0x00}}, {2, [3]byte{0xd0, 0x8f, 0x00}},
		{2, [3]byte{0xd1, 0x8e, 0x00}}, {2, [3]byte{0xd0, 0xae, 0x00}},
		{2, [3]byte{0xd1, 0x8a, 0x00}}, {2, [3]byte{0xd0, 0xaa, 0x00}},
		{2, [3]byte{0xd0, 0xb0, 0x00}}, {2, [3]byte{0xd0, 0x90, 0x00}},
		{2, [3]byte{0xd0, 0xb1, 0x00}}, {2, [3]byte{0xd0, 0x91, 0x00}},
		{2, [3]byte{0xd1, 0x86, 0x00}}, {2, [3]byte{0xd0, 0xa6, 0x00}},
		{2, [3]byte{0xd0, 0xb4, 0x00}}, {2, [3]byte{0xd0, 0x94, 0x00}},
		{2, [3]byte{0xd0, 0xb5, 0x00}}, {2, [3]byte{0xd0, 0x95, 0x00}},
		{2, [3]byte{0xd1, 0x84, 0x00}}, {2, [3]byte{0xd0, 0xa4, 0x00}},
		{2, [3]byte{0xd0, 0xb3, 0x00}}, {2, [3]byte{0xd0, 0x93, 0x00}},
		{2, [3]byte{0xc2, 0xab, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0x91}}, {3, [3]byte{0xe2, 0x96, 0x92}},
		{3, [3]byte{0xe2, 0x96, 0x93}}, {3, [3]byte{0xe2, 0x94, 0x82}},
		{3, [3]byte{0xe2, 0x94, 0xa4}}, {2, [3]byte{0xd1, 0x85, 0x00}},
		{2, [3]byte{0xd0, 0xa5, 0x00}}, {2, [3]byte{0xd0, 0xb8, 0x00}},
		{2, [3]byte{0xd0, 0x98, 0x00}}, {3, [3]byte{0xe2, 0x95, 0xa3}},
		{3, [3]byte{0xe2, 0x95, 0x91}}, {3, [3]byte{0xe2, 0x95, 0x97}},
		{3, [3]byte{0xe2, 0x95, 0x9d}}, {2, [3]byte{0xd0, 0xb9, 0x00}},
		{2, [3]byte{0xd0, 0x99, 0x00}}, {3, [3]byte{0xe2, 0x94, 0x90}},
		{3, [3]byte{0xe2, 0x94, 0x94}}, {3, [3]byte{0xe2, 0x94, 0xb4}},
		{3, [3]byte{0xe2, 0x94, 0xac}}, {3, [3]byte{0xe2, 0x94, 0x9c}},
		{3, [3]byte{0xe2, 0x94, 0x80}}, {3, [3]byte{0xe2, 0x94, 0xbc}},
		{2, [3]byte{0xd0, 0xba, 0x00}}, {2, [3]byte{0xd0, 0x9a, 0x00}},
		{3, [3]byte{0xe2, 0x95, 0x9a}}, {3, [3]byte{0xe2, 0x95, 0x94}},
		{3, [3]byte{0xe2, 0x95, 0xa9}}, {3, [3]byte{0xe2, 0x95, 0xa6}},
		{3, [3]byte{0xe2, 0x95, 0xa0}}, {3, [3]byte{0xe2, 0x95, 0x90}},
		{3, [3]byte{0xe2, 0x95, 0xac}}, {2, [3]byte{0xc2, 0xa4, 0x00}},
		{2, [3]byte{0xd0, 0xbb, 0x00}}, {2, [3]byte{0xd0, 0x9b, 0x00}},
		{2, [3]byte{0xd0, 0xbc, 0x00}}, {2, [3]byte{0xd0, 0x9c, 0x00}},
		{2, [3]byte{0xd0, 0xbd, 0x00}}, {2, [3]byte{0xd0, 0x9d, 0x00}},
		{2, [3]byte{0xd0, 0xbe, 0x00}}, {2, [3]byte{0xd0, 0x9e, 0x00}},
		{2, [3]byte{0xd0, 0xbf, 0x00}}, {3, [3]byte{0xe2, 0x94, 0x98}},
		{3, [3]byte{0xe2, 0x94, 0x8c}}, {3, [3]byte{0xe2, 0x96, 0x88}},
		{3, [3]byte{0xe2, 0x96, 0x84}}, {2, [3]byte{0xd0, 0x9f, 0x00}},
		{2, [3]byte{0xd1, 0x8f, 0x00}}, {3, [3]byte{0xe2, 0x96, 0x80}},
		{2, [3]byte{0xd0, 0xaf, 0x00}}, {2, [3]byte{0xd1, 0x80, 0x00}},
		{2, [3]byte{0xd0, 0xa0, 0x00}}, {2, [3]byte{0xd1, 0x81, 0x00}},
		{2, [3]byte{0xd0, 0xa1, 0x00}}, {2, [3]byte{0xd1, 0x82, 0x00}},
		{2, [3]byte{0xd0, 0xa2, 0x00}}, {2, [3]byte{0xd1, 0x83, 0x00}},
		{2, [3]byte{0xd0, 0xa3, 0x00}}, {2, [3]byte{0xd0, 0xb6, 0x00}},
		{2, [3]byte{0xd0, 0x96, 0x00}}, {2, [3]byte{0xd0, 0xb2, 0x00}},
		{2, [3]byte{0xd0, 0x92, 0x00}}, {2, [3]byte{0xd1, 0x8c, 0x00}},
		{2, [3]byte{0xd0, 0xac, 0x00}}, {3, [3]byte{0xe2, 0x84, 0x96}},
		{2, [3]byte{0xc2, 0xad, 0x00}}, {2, [3]byte{0xd1, 0x8b, 0x00}},
		{2, [3]byte{0xd0, 0xab, 0x00}}, {2, [3]byte{0xd0, 0xb7, 0x00}},
		{2, [3]byte{0xd0, 0x97, 0x00}}, {2, [3]byte{0xd1, 0x88, 0x00}},
		{2, [3]byte{0xd0, 0xa8, 0x00}}, {2, [3]byte{0xd1, 0x8d, 0x00}},
		{2, [3]byte{0xd0, 0xad, 0x00}}, {2, [3]byte{0xd1, 0x89, 0x00}},
		{2, [3]byte{0xd0, 0xa9, 0x00}}, {2, [3]byte{0xd1, 0x87, 0x00}},
		{2, [3]byte{0xd0, 0xa7, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0xa0}}, {2, [3]byte{0xc2, 0xa0, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xff0000a0, 0xcf0000a4, 0xfd0000a7, 0xae0000ab, 0xf00000ad, 0xaf0000bb, 0x85000401, 0x81000402,
		0x83000403, 0x87000404, 0x89000405, 0x8b000406, 0x8d000407, 0x8f000408, 0x91000409, 0x9300040a,
		0x9500040b, 0x9700040c, 0x9900040e, 0x9b00040f, 0xa1000410, 0xa3000411, 0xec000412, 0xad000413,
		0xa7000414, 0xa9000415, 0xea000416, 0xf4000417, 0xb8000418, 0xbe000419, 0xc700041a, 0xd100041b,
		0xd300041c, 0xd500041d, 0xd700041e, 0xdd00041f, 0xe2000420, 0xe4000421, 0xe6000422, 0xe8000423,
		0xab000424, 0xb6000425, 0xa5000426, 0xfc000427, 0xf6000428, 0xfa000429, 0x9f00042a, 0xf200042b,
		0xee00042c, 0xf800042d, 0x9d00042e, 0xe000042f, 0xa0000430, 0xa2000431, 0xeb000432, 0xac000433,
		0xa6000434, 0xa8000435, 0xe9000436, 0xf3000437, 0xb7000438, 0xbd000439, 0xc600043a, 0xd000043b,
		0xd200043c, 0xd400043d, 0xd600043e, 0xd800043f, 0xe1000440, 0xe3000441, 0xe5000442, 0xe7000443,
		0xaa000444, 0xb5000445, 0xa4000446, 0xfb000447, 0xf5000448, 0xf9000449, 0x9e00044a, 0xf100044b,
		0xed00044c, 0xf700044d, 0x9c00044e, 0xde00044f, 0x84000451, 0x80000452, 0x82000453, 0x86000454,
		0x88000455, 0x8a000456, 0x8c000457, 0x8e000458, 0x90000459, 0x9200045a, 0x9400045b, 0x9600045c,
		0x9800045e, 0x9a00045f, 0xef002116, 0xc4002500, 0xb3002502, 0xda00250c, 0xbf002510, 0xc0002514,
		0xd9002518, 0xc300251c, 0xb4002524, 0xc200252c, 0xc1002534, 0xc500253c, 0xcd002550, 0xba002551,
		0xc9002554, 0xbb002557, 0xc800255a, 0xbc00255d, 0xcc002560, 0xb9002563, 0xcb002566, 0xca002569,
		0xce00256c, 0xdf002580, 0xdc002584, 0xdb002588, 0xb0002591, 0xb1002592, 0xb2002593, 0xfe0025a0,
	},
}

// CodePage858 is the Windows Code Page 858 encoding.
var CodePage858 *Charmap = &codePage858

var codePage858 = Charmap{
	name:          "Windows Code Page 858",
	mib:           identifier.IBM00858,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x87, 0x00}}, {2, [3]byte{0xc3, 0xbc, 0x00}},
		{2, [3]byte{0xc3, 0xa9, 0x00}}, {2, [3]byte{0xc3, 0xa2, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa0, 0x00}},
		{2, [3]byte{0xc3, 0xa5, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xac, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x89, 0x00}}, {2, [3]byte{0xc3, 0xa6, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc3, 0xb4, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb2, 0x00}},
		{2, [3]byte{0xc3, 0xbb, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xbf, 0x00}}, {2, [3]byte{0xc3, 0x96, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0xb8, 0x00}},
		{2, [3]byte{0xc2, 0xa3, 0x00}}, {2, [3]byte{0xc3, 0x98, 0x00}},
		{2, [3]byte{0xc3, 0x97, 0x00}}, {2, [3]byte{0xc6, 0x92, 0x00}},
		{2, [3]byte{0xc3, 0xa1, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xb3, 0x00}}, {2, [3]byte{0xc3, 0xba, 0x00}},
		{2, [3]byte{0xc3, 0xb1, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xba, 0x00}},
		{2, [3]byte{0xc2, 0xbf, 0x00}}, {2, [3]byte{0xc2, 0xae, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xa1, 0x00}},
		{2, [3]byte{0xc2, 0xab, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0x91}}, {3, [3]byte{0xe2, 0x96, 0x92}},
		{3, [3]byte{0xe2, 0x96, 0x93}}, {3, [3]byte{0xe2, 0x94, 0x82}},
		{3, [3]byte{0xe2, 0x94, 0xa4}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc3, 0x80, 0x00}},
		{2, [3]byte{0xc2, 0xa9, 0x00}}, {3, [3]byte{0xe2, 0x95, 0xa3}},
		{3, [3]byte{0xe2, 0x95, 0x91}}, {3, [3]byte{0xe2, 0x95, 0x97}},
		{3, [3]byte{0xe2, 0x95, 0x9d}}, {2, [3]byte{0xc2, 0xa2, 0x00}},
		{2, [3]byte{0xc2, 0xa5, 0x00}}, {3, [3]byte{0xe2, 0x94, 0x90}},
		{3, [3]byte{0xe2, 0x94, 0x94}}, {3, [3]byte{0xe2, 0x94, 0xb4}},
		{3, [3]byte{0xe2, 0x94, 0xac}}, {3, [3]byte{0xe2, 0x94, 0x9c}},
		{3, [3]byte{0xe2, 0x94, 0x80}}, {3, [3]byte{0xe2, 0x94, 0xbc}},
		{2, [3]byte{0xc3, 0xa3, 0x00}}, {2, [3]byte{0xc3, 0x83, 0x00}},
		{3, [3]byte{0xe2, 0x95, 0x9a}}, {3, [3]byte{0xe2, 0x95, 0x94}},
		{3, [3]byte{0xe2, 0x95, 0xa9}}, {3, [3]byte{0xe2, 0x95, 0xa6}},
		{3, [3]byte{0xe2, 0x95, 0xa0}}, {3, [3]byte{0xe2, 0x95, 0x90}},
		{3, [3]byte{0xe2, 0x95, 0xac}}, {2, [3]byte{0xc2, 0xa4, 0x00}},
		{2, [3]byte{0xc3, 0xb0, 0x00}}, {2, [3]byte{0xc3, 0x90, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {3, [3]byte{0xe2, 0x82, 0xac}},
		{2, [3]byte{0xc3, 0x8d, 0x00}}, {2, [3]byte{0xc3, 0x8e, 0x00}},
		{2, [3]byte{0xc3, 0x8f, 0x00}}, {3, [3]byte{0xe2, 0x94, 0x98}},
		{3, [3]byte{0xe2, 0x94, 0x8c}}, {3, [3]byte{0xe2, 0x96, 0x88}},
		{3, [3]byte{0xe2, 0x96, 0x84}}, {2, [3]byte{0xc2, 0xa6, 0x00}},
		{2, [3]byte{0xc3, 0x8c, 0x00}}, {3, [3]byte{0xe2, 0x96, 0x80}},
		{2, [3]byte{0xc3, 0x93, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc3, 0x92, 0x00}},
		{2, [3]byte{0xc3, 0xb5, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{2, [3]byte{0xc2, 0xb5, 0x00}}, {2, [3]byte{0xc3, 0xbe, 0x00}},
		{2, [3]byte{0xc3, 0x9e, 0x00}}, {2, [3]byte{0xc3, 0x9a, 0x00}},
		{2, [3]byte{0xc3, 0x9b, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0xbd, 0x00}}, {2, [3]byte{0xc3, 0x9d, 0x00}},
		{2, [3]byte{0xc2, 0xaf, 0x00}}, {2, [3]byte{0xc2, 0xb4, 0x00}},
		{2, [3]byte{0xc2, 0xad, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x97}}, {2, [3]byte{0xc2, 0xbe, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xb7, 0x00}}, {2, [3]byte{0xc2, 0xb8, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xa8, 0x00}},
		{2, [3]byte{0xc2, 0xb7, 0x00}}, {2, [3]byte{0xc2, 0xb9, 0x00}},
		{2, [3]byte{0xc2, 0xb3, 0x00}}, {2, [3]byte{0xc2, 0xb2, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0xa0}}, {2, [3]byte{0xc2, 0xa0, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xff0000a0, 0xad0000a1, 0xbd0000a2, 0x9c0000a3, 0xcf0000a4, 0xbe0000a5, 0xdd0000a6, 0xf50000a7,
		0xf90000a8, 0xb80000a9, 0xa60000aa, 0xae0000ab, 0xaa0000ac, 0xf00000ad, 0xa90000ae, 0xee0000af,
		0xf80000b0, 0xf10000b1, 0xfd0000b2, 0xfc0000b3, 0xef0000b4, 0xe60000b5, 0xf40000b6, 0xfa0000b7,
		0xf70000b8, 0xfb0000b9, 0xa70000ba, 0xaf0000bb, 0xac0000bc, 0xab0000bd, 0xf30000be, 0xa80000bf,
		0xb70000c0, 0xb50000c1, 0xb60000c2, 0xc70000c3, 0x8e0000c4, 0x8f0000c5, 0x920000c6, 0x800000c7,
		0xd40000c8, 0x900000c9, 0xd20000ca, 0xd30000cb, 0xde0000cc, 0xd60000cd, 0xd70000ce, 0xd80000cf,
		0xd10000d0, 0xa50000d1, 0xe30000d2, 0xe00000d3, 0xe20000d4, 0xe50000d5, 0x990000d6, 0x9e0000d7,
		0x9d0000d8, 0xeb0000d9, 0xe90000da, 0xea0000db, 0x9a0000dc, 0xed0000dd, 0xe80000de, 0xe10000df,
		0x850000e0, 0xa00000e1, 0x830000e2, 0xc60000e3, 0x840000e4, 0x860000e5, 0x910000e6, 0x870000e7,
		0x8a0000e8, 0x820000e9, 0x880000ea, 0x890000eb, 0x8d0000ec, 0xa10000ed, 0x8c0000ee, 0x8b0000ef,
		0xd00000f0, 0xa40000f1, 0x950000f2, 0xa20000f3, 0x930000f4, 0xe40000f5, 0x940000f6, 0xf60000f7,
		0x9b0000f8, 0x970000f9, 0xa30000fa, 0x960000fb, 0x810000fc, 0xec0000fd, 0xe70000fe, 0x980000ff,
		0x9f000192, 0xf2002017, 0xd50020ac, 0xc4002500, 0xb3002502, 0xda00250c, 0xbf002510, 0xc0002514,
		0xd9002518, 0xc300251c, 0xb4002524, 0xc200252c, 0xc1002534, 0xc500253c, 0xcd002550, 0xba002551,
		0xc9002554, 0xbb002557, 0xc800255a, 0xbc00255d, 0xcc002560, 0xb9002563, 0xcb002566, 0xca002569,
		0xce00256c, 0xdf002580, 0xdc002584, 0xdb002588, 0xb0002591, 0xb1002592, 0xb2002593, 0xfe0025a0,
	},
}

// CodePage860 is the IBM Code Page 860 encoding.
var CodePage860 *Charmap = &codePage860

var codePage860 = Charmap{
	name:          "IBM Code Page 860",
	mib:           identifier.IBM860,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x87, 0x00}}, {2, [3]byte{0xc3, 0xbc, 0x00}},
		{2, [3]byte{0xc3, 0xa9, 0x00}}, {2, [3]byte{0xc3, 0xa2, 0x00}},
		{2, [3]byte{0xc3, 0xa3, 0x00}}, {2, [3]byte{0xc3, 0xa0, 0x00}},
		{2, [3]byte{0xc3, 0x81, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0x8a, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc3, 0xac, 0x00}},
		{2, [3]byte{0xc3, 0x83, 0x00}}, {2, [3]byte{0xc3, 0x82, 0x00}},
		{2, [3]byte{0xc3, 0x89, 0x00}}, {2, [3]byte{0xc3, 0x80, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {2, [3]byte{0xc3, 0xb4, 0x00}},
		{2, [3]byte{0xc3, 0xb5, 0x00}}, {2, [3]byte{0xc3, 0xb2, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc2, 0xa2, 0x00}},
		{2, [3]byte{0xc2, 0xa3, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xa7}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc3, 0xa1, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xb3, 0x00}}, {2, [3]byte{0xc3, 0xba, 0x00}},
		{2, [3]byte{0xc3, 0xb1, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xba, 0x00}},
		{2, [3]byte{0xc2, 0xbf, 0x00}}, {2, [3]byte{0xc3, 0x92, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xa1, 0x00}},
		{2, [3]byte{0xc2, 0xab, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0x91}}, {3, [3]byte{0xe2, 0x96, 0x92}},
		{3, [3]byte{0xe2, 0x96, 0x93}}, {3, [3]byte{0xe2, 0x94, 0x82}},
		{3, [3]byte{0xe2, 0x94, 0xa4}}, {3, [3]byte{0xe2, 0x95, 0xa1}},
		{3, [3]byte{0xe2, 0x95, 0xa2}}, {3, [3]byte{0xe2, 0x95, 0x96}},
		{3, [3]byte{0xe2, 0x95, 0x95}}, {3, [3]byte{0xe2, 0x95, 0xa3}},
		{3, [3]byte{0xe2, 0x95, 0x91}}, {3, [3]byte{0xe2, 0x95, 0x97}},
		{3, [3]byte{0xe2, 0x95, 0x9d}}, {3, [3]byte{0xe2, 0x95, 0x9c}},
		{3, [3]byte{0xe2, 0x95, 0x9b}}, {3, [3]byte{0xe2, 0x94, 0x90}},
		{3, [3]byte{0xe2, 0x94, 0x94}}, {3, [3]byte{0xe2, 0x94, 0xb4}},
		{3, [3]byte{0xe2, 0x94, 0xac}}, {3, [3]byte{0xe2, 0x94, 0x9c}},
		{3, [3]byte{0xe2, 0x94, 0x80}}, {3, [3]byte{0xe2, 0x94, 0xbc}},
		{3, [3]byte{0xe2, 0x95, 0x9e}}, {3, [3]byte{0xe2, 0x95, 0x9f}},
		{3, [3]byte{0xe2, 0x95, 0x9a}}, {3, [3]byte{0xe2, 0x95, 0x94}},
		{3, [3]byte{0xe2, 0x95, 0xa9}}, {3, [3]byte{0xe2, 0x95, 0xa6}},
		{3, [3]byte{0xe2, 0x95, 0xa0}}, {3, [3]byte{0xe2, 0x95, 0x90}},
		{3, [3]byte{0xe2, 0x95, 0xac}}, {3, [3]byte{0xe2, 0x95, 0xa7}},
		{3, [3]byte{0xe2, 0x95, 0xa8}}, {3, [3]byte{0xe2, 0x95, 0xa4}},
		{3, [3]byte{0xe2, 0x95, 0xa5}}, {3, [3]byte{0xe2, 0x95, 0x99}},
		{3, [3]byte{0xe2, 0x95, 0x98}}, {3, [3]byte{0xe2, 0x95, 0x92}},
		{3, [3]byte{0xe2, 0x95, 0x93}}, {3, [3]byte{0xe2, 0x95, 0xab}},
		{3, [3]byte{0xe2, 0x95, 0xaa}}, {3, [3]byte{0xe2, 0x94, 0x98}},
		{3, [3]byte{0xe2, 0x94, 0x8c}}, {3, [3]byte{0xe2, 0x96, 0x88}},
		{3, [3]byte{0xe2, 0x96, 0x84}}, {3, [3]byte{0xe2, 0x96, 0x8c}},
		{3, [3]byte{0xe2, 0x96, 0x90}}, {3, [3]byte{0xe2, 0x96, 0x80}},
		{2, [3]byte{0xce, 0xb1, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xce, 0x93, 0x00}}, {2, [3]byte{0xcf, 0x80, 0x00}},
		{2, [3]byte{0xce, 0xa3, 0x00}}, {2, [3]byte{0xcf, 0x83, 0x00}},
		{2, [3]byte{0xc2, 0xb5, 0x00}}, {2, [3]byte{0xcf, 0x84, 0x00}},
		{2, [3]byte{0xce, 0xa6, 0x00}}, {2, [3]byte{0xce, 0x98, 0x00}},
		{2, [3]byte{0xce, 0xa9, 0x00}}, {2, [3]byte{0xce, 0xb4, 0x00}},
		{3, [3]byte{0xe2, 0x88, 0x9e}}, {2, [3]byte{0xcf, 0x86, 0x00}},
		{2, [3]byte{0xce, 0xb5, 0x00}}, {3, [3]byte{0xe2, 0x88, 0xa9}},
		{3, [3]byte{0xe2, 0x89, 0xa1}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{3, [3]byte{0xe2, 0x89, 0xa5}}, {3, [3]byte{0xe2, 0x89, 0xa4}},
		{3, [3]byte{0xe2, 0x8c, 0xa0}}, {3, [3]byte{0xe2, 0x8c, 0xa1}},
		{2, [3]byte{0xc3, 0xb7, 0x00}}, {3, [3]byte{0xe2, 0x89, 0x88}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {3, [3]byte{0xe2, 0x88, 0x99}},
		{2, [3]byte{0xc2, 0xb7, 0x00}}, {3, [3]byte{0xe2, 0x88, 0x9a}},
		{3, [3]byte{0xe2, 0x81, 0xbf}}, {2, [3]byte{0xc2, 0xb2, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0xa0}}, {2, [3]byte{0xc2, 0xa0, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xff0000a0, 0xad0000a1, 0x9b0000a2, 0x9c0000a3, 0xa60000aa, 0xae0000ab, 0xaa0000ac, 0xf80000b0,
		0xf10000b1, 0xfd0000b2, 0xe60000b5, 0xfa0000b7, 0xa70000ba, 0xaf0000bb, 0xac0000bc, 0xab0000bd,
		0xa80000bf, 0x910000c0, 0x860000c1, 0x8f0000c2, 0x8e0000c3, 0x800000c7, 0x920000c8, 0x900000c9,
		0x890000ca, 0x980000cc, 0x8b0000cd, 0xa50000d1, 0xa90000d2, 0x9f0000d3, 0x8c0000d4, 0x990000d5,
		0x9d0000d9, 0x960000da, 0x9a0000dc, 0xe10000df, 0x850000e0, 0xa00000e1, 0x830000e2, 0x840000e3,
		0x870000e7, 0x8a0000e8, 0x820000e9, 0x880000ea, 0x8d0000ec, 0xa10000ed, 0xa40000f1, 0x950000f2,
		0xa20000f3, 0x930000f4, 0x940000f5, 0xf60000f7, 0x970000f9, 0xa30000fa, 0x810000fc, 0xe2000393,
		0xe9000398, 0xe40003a3, 0xe80003a6, 0xea0003a9, 0xe00003b1, 0xeb0003b4, 0xee0003b5, 0xe30003c0,
		0xe50003c3, 0xe70003c4, 0xed0003c6, 0xfc00207f, 0x9e0020a7, 0xf9002219, 0xfb00221a, 0xec00221e,
		0xef002229, 0xf7002248, 0xf0002261, 0xf3002264, 0xf2002265, 0xf4002320, 0xf5002321, 0xc4002500,
		0xb3002502, 0xda00250c, 0xbf002510, 0xc0002514, 0xd9002518, 0xc300251c, 0xb4002524, 0xc200252c,
		0xc1002534, 0xc500253c, 0xcd002550, 0xba002551, 0xd5002552, 0xd6002553, 0xc9002554, 0xb8002555,
		0xb7002556, 0xbb002557, 0xd4002558, 0xd3002559, 0xc800255a, 0xbe00255b, 0xbd00255c, 0xbc00255d,
		0xc600255e, 0xc700255f, 0xcc002560, 0xb5002561, 0xb6002562, 0xb9002563, 0xd1002564, 0xd2002565,
		0xcb002566, 0xcf002567, 0xd0002568, 0xca002569, 0xd800256a, 0xd700256b, 0xce00256c, 0xdf002580,
		0xdc002584, 0xdb002588, 0xdd00258c, 0xde002590, 0xb0002591, 0xb1002592, 0xb2002593, 0xfe0025a0,
	},
}

// CodePage862 is the IBM Code Page 862 encoding.
var CodePage862 *Charmap = &codePage862

var codePage862 = Charmap{
	name:          "IBM Code Page 862",
	mib:           identifier.PC862LatinHebrew,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xd7, 0x90, 0x00}}, {2, [3]byte{0xd7, 0x91, 0x00}},
		{2, [3]byte{0xd7, 0x92, 0x00}}, {2, [3]byte{0xd7, 0x93, 0x00}},
		{2, [3]byte{0xd7, 0x94, 0x00}}, {2, [3]byte{0xd7, 0x95, 0x00}},
		{2, [3]byte{0xd7, 0x96, 0x00}}, {2, [3]byte{0xd7, 0x97, 0x00}},
		{2, [3]byte{0xd7, 0x98, 0x00}}, {2, [3]byte{0xd7, 0x99, 0x00}},
		{2, [3]byte{0xd7, 0x9a, 0x00}}, {2, [3]byte{0xd7, 0x9b, 0x00}},
		{2, [3]byte{0xd7, 0x9c, 0x00}}, {2, [3]byte{0xd7, 0x9d, 0x00}},
		{2, [3]byte{0xd7, 0x9e, 0x00}}, {2, [3]byte{0xd7, 0x9f, 0x00}},
		{2, [3]byte{0xd7, 0xa0, 0x00}}, {2, [3]byte{0xd7, 0xa1, 0x00}},
		{2, [3]byte{0xd7, 0xa2, 0x00}}, {2, [3]byte{0xd7, 0xa3, 0x00}},
		{2, [3]byte{0xd7, 0xa4, 0x00}}, {2, [3]byte{0xd7, 0xa5, 0x00}},
		{2, [3]byte{0xd7, 0xa6, 0x00}}, {2, [3]byte{0xd7, 0xa7, 0x00}},
		{2, [3]byte{0xd7, 0xa8, 0x00}}, {2, [3]byte{0xd7, 0xa9, 0x00}},
		{2, [3]byte{0xd7, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xa2, 0x00}},
		{2, [3]byte{0xc2, 0xa3, 0x00}}, {2, [3]byte{0xc2, 0xa5, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xa7}}, {2, [3]byte{0xc6, 0x92, 0x00}},
		{2, [3]byte{0xc3, 0xa1, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xb3, 0x00}}, {2, [3]byte{0xc3, 0xba, 0x00}},
		{2, [3]byte{0xc3, 0xb1, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xba, 0x00}},
		{2, [3]byte{0xc2, 0xbf, 0x00}}, {3, [3]byte{0xe2, 0x8c, 0x90}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xa1, 0x00}},
		{2, [3]byte{0xc2, 0xab, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0x91}}, {3, [3]byte{0xe2, 0x96, 0x92}},
		{3, [3]byte{0xe2, 0x96, 0x93}}, {3, [3]byte{0xe2, 0x94, 0x82}},
		{3, [3]byte{0xe2, 0x94, 0xa4}}, {3, [3]byte{0xe2, 0x95, 0xa1}},
		{3, [3]byte{0xe2, 0x95, 0xa2}}, {3, [3]byte{0xe2, 0x95, 0x96}},
		{3, [3]byte{0xe2, 0x95, 0x95}}, {3, [3]byte{0xe2, 0x95, 0xa3}},
		{3, [3]byte{0xe2, 0x95, 0x91}}, {3, [3]byte{0xe2, 0x95, 0x97}},
		{3, [3]byte{0xe2, 0x95, 0x9d}}, {3, [3]byte{0xe2, 0x95, 0x9c}},
		{3, [3]byte{0xe2, 0x95, 0x9b}}, {3, [3]byte{0xe2, 0x94, 0x90}},
		{3, [3]byte{0xe2, 0x94, 0x94}}, {3, [3]byte{0xe2, 0x94, 0xb4}},
		{3, [3]byte{0xe2, 0x94, 0xac}}, {3, [3]byte{0xe2, 0x94, 0x9c}},
		{3, [3]byte{0xe2, 0x94, 0x80}}, {3, [3]byte{0xe2, 0x94, 0xbc}},
		{3, [3]byte{0xe2, 0x95, 0x9e}}, {3, [3]byte{0xe2, 0x95, 0x9f}},
		{3, [3]byte{0xe2, 0x95, 0x9a}}, {3, [3]byte{0xe2, 0x95, 0x94}},
		{3, [3]byte{0xe2, 0x95, 0xa9}}, {3, [3]byte{0xe2, 0x95, 0xa6}},
		{3, [3]byte{0xe2, 0x95, 0xa0}}, {3, [3]byte{0xe2, 0x95, 0x90}},
		{3, [3]byte{0xe2, 0x95, 0xac}}, {3, [3]byte{0xe2, 0x95, 0xa7}},
		{3, [3]byte{0xe2, 0x95, 0xa8}}, {3, [3]byte{0xe2, 0x95, 0xa4}},
		{3, [3]byte{0xe2, 0x95, 0xa5}}, {3, [3]byte{0xe2, 0x95, 0x99}},
		{3, [3]byte{0xe2, 0x95, 0x98}}, {3, [3]byte{0xe2, 0x95, 0x92}},
		{3, [3]byte{0xe2, 0x95, 0x93}}, {3, [3]byte{0xe2, 0x95, 0xab}},
		{3, [3]byte{0xe2, 0x95, 0xaa}}, {3, [3]byte{0xe2, 0x94, 0x98}},
		{3, [3]byte{0xe2, 0x94, 0x8c}}, {3, [3]byte{0xe2, 0x96, 0x88}},
		{3, [3]byte{0xe2, 0x96, 0x84}}, {3, [3]byte{0xe2, 0x96, 0x8c}},
		{3, [3]byte{0xe2, 0x96, 0x90}}, {3, [3]byte{0xe2, 0x96, 0x80}},
		{2, [3]byte{0xce, 0xb1, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xce, 0x93, 0x00}}, {2, [3]byte{0xcf, 0x80, 0x00}},
		{2, [3]byte{0xce, 0xa3, 0x00}}, {2, [3]byte{0xcf, 0x83, 0x00}},
		{2, [3]byte{0xc2, 0xb5, 0x00}}, {2, [3]byte{0xcf, 0x84, 0x00}},
		{2, [3]byte{0xce, 0xa6, 0x00}}, {2, [3]byte{0xce, 0x98, 0x00}},
		{2, [3]byte{0xce, 0xa9, 0x00}}, {2, [3]byte{0xce, 0xb4, 0x00}},
		{3, [3]byte{0xe2, 0x88, 0x9e}}, {2, [3]byte{0xcf, 0x86, 0x00}},
		{2, [3]byte{0xce, 0xb5, 0x00}}, {3, [3]byte{0xe2, 0x88, 0xa9}},
		{3, [3]byte{0xe2, 0x89, 0xa1}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{3, [3]byte{0xe2, 0x89, 0xa5}}, {3, [3]byte{0xe2, 0x89, 0xa4}},
		{3, [3]byte{0xe2, 0x8c, 0xa0}}, {3, [3]byte{0xe2, 0x8c, 0xa1}},
		{2, [3]byte{0xc3, 0xb7, 0x00}}, {3, [3]byte{0xe2, 0x89, 0x88}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {3, [3]byte{0xe2, 0x88, 0x99}},
		{2, [3]byte{0xc2, 0xb7, 0x00}}, {3, [3]byte{0xe2, 0x88, 0x9a}},
		{3, [3]byte{0xe2, 0x81, 0xbf}}, {2, [3]byte{0xc2, 0xb2, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0xa0}}, {2, [3]byte{0xc2, 0xa0, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xff0000a0, 0xad0000a1, 0x9b0000a2, 0x9c0000a3, 0x9d0000a5, 0xa60000aa, 0xae0000ab, 0xaa0000ac,
		0xf80000b0, 0xf10000b1, 0xfd0000b2, 0xe60000b5, 0xfa0000b7, 0xa70000ba, 0xaf0000bb, 0xac0000bc,
		0xab0000bd, 0xa80000bf, 0xa50000d1, 0xe10000df, 0xa00000e1, 0xa10000ed, 0xa40000f1, 0xa20000f3,
		0xf60000f7, 0xa30000fa, 0x9f000192, 0xe2000393, 0xe9000398, 0xe40003a3, 0xe80003a6, 0xea0003a9,
		0xe00003b1, 0xeb0003b4, 0xee0003b5, 0xe30003c0, 0xe50003c3, 0xe70003c4, 0xed0003c6, 0x800005d0,
		0x810005d1, 0x820005d2, 0x830005d3, 0x840005d4, 0x850005d5, 0x860005d6, 0x870005d7, 0x880005d8,
		0x890005d9, 0x8a0005da, 0x8b0005db, 0x8c0005dc, 0x8d0005dd, 0x8e0005de, 0x8f0005df, 0x900005e0,
		0x910005e1, 0x920005e2, 0x930005e3, 0x940005e4, 0x950005e5, 0x960005e6, 0x970005e7, 0x980005e8,
		0x990005e9, 0x9a0005ea, 0xfc00207f, 0x9e0020a7, 0xf9002219, 0xfb00221a, 0xec00221e, 0xef002229,
		0xf7002248, 0xf0002261, 0xf3002264, 0xf2002265, 0xa9002310, 0xf4002320, 0xf5002321, 0xc4002500,
		0xb3002502, 0xda00250c, 0xbf002510, 0xc0002514, 0xd9002518, 0xc300251c, 0xb4002524, 0xc200252c,
		0xc1002534, 0xc500253c, 0xcd002550, 0xba002551, 0xd5002552, 0xd6002553, 0xc9002554, 0xb8002555,
		0xb7002556, 0xbb002557, 0xd4002558, 0xd3002559, 0xc800255a, 0xbe00255b, 0xbd00255c, 0xbc00255d,
		0xc600255e, 0xc700255f, 0xcc002560, 0xb5002561, 0xb6002562, 0xb9002563, 0xd1002564, 0xd2002565,
		0xcb002566, 0xcf002567, 0xd0002568, 0xca002569, 0xd800256a, 0xd700256b, 0xce00256c, 0xdf002580,
		0xdc002584, 0xdb002588, 0xdd00258c, 0xde002590, 0xb0002591, 0xb1002592, 0xb2002593, 0xfe0025a0,
	},
}

// CodePage863 is the IBM Code Page 863 encoding.
var CodePage863 *Charmap = &codePage863

var codePage863 = Charmap{
	name:          "IBM Code Page 863",
	mib:           identifier.IBM863,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x87, 0x00}}, {2, [3]byte{0xc3, 0xbc, 0x00}},
		{2, [3]byte{0xc3, 0xa9, 0x00}}, {2, [3]byte{0xc3, 0xa2, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc3, 0xa0, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {3, [3]byte{0xe2, 0x80, 0x97}},
		{2, [3]byte{0xc3, 0x80, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0x89, 0x00}}, {2, [3]byte{0xc3, 0x88, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0xb4, 0x00}},
		{2, [3]byte{0xc3, 0x8b, 0x00}}, {2, [3]byte{0xc3, 0x8f, 0x00}},
		{2, [3]byte{0xc3, 0xbb, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0x94, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc2, 0xa2, 0x00}},
		{2, [3]byte{0xc2, 0xa3, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0x9b, 0x00}}, {2, [3]byte{0xc6, 0x92, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xb4, 0x00}},
		{2, [3]byte{0xc3, 0xb3, 0x00}}, {2, [3]byte{0xc3, 0xba, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc2, 0xb8, 0x00}},
		{2, [3]byte{0xc2, 0xb3, 0x00}}, {2, [3]byte{0xc2, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {3, [3]byte{0xe2, 0x8c, 0x90}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xbe, 0x00}},
		{2, [3]byte{0xc2, 0xab, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0x91}}, {3, [3]byte{0xe2, 0x96, 0x92}},
		{3, [3]byte{0xe2, 0x96, 0x93}}, {3, [3]byte{0xe2, 0x94, 0x82}},
		{3, [3]byte{0xe2, 0x94, 0xa4}}, {3, [3]byte{0xe2, 0x95, 0xa1}},
		{3, [3]byte{0xe2, 0x95, 0xa2}}, {3, [3]byte{0xe2, 0x95, 0x96}},
		{3, [3]byte{0xe2, 0x95, 0x95}}, {3, [3]byte{0xe2, 0x95, 0xa3}},
		{3, [3]byte{0xe2, 0x95, 0x91}}, {3, [3]byte{0xe2, 0x95, 0x97}},
		{3, [3]byte{0xe2, 0x95, 0x9d}}, {3, [3]byte{0xe2, 0x95, 0x9c}},
		{3, [3]byte{0xe2, 0x95, 0x9b}}, {3, [3]byte{0xe2, 0x94, 0x90}},
		{3, [3]byte{0xe2, 0x94, 0x94}}, {3, [3]byte{0xe2, 0x94, 0xb4}},
		{3, [3]byte{0xe2, 0x94, 0xac}}, {3, [3]byte{0xe2, 0x94, 0x9c}},
		{3, [3]byte{0xe2, 0x94, 0x80}}, {3, [3]byte{0xe2, 0x94, 0xbc}},
		{3, [3]byte{0xe2, 0x95, 0x9e}}, {3, [3]byte{0xe2, 0x95, 0x9f}},
		{3, [3]byte{0xe2, 0x95, 0x9a}}, {3, [3]byte{0xe2, 0x95, 0x94}},
		{3, [3]byte{0xe2, 0x95, 0xa9}}, {3, [3]byte{0xe2, 0x95, 0xa6}},
		{3, [3]byte{0xe2, 0x95, 0xa0}}, {3, [3]byte{0xe2, 0x95, 0x90}},
		{3, [3]byte{0xe2, 0x95, 0xac}}, {3, [3]byte{0xe2, 0x95, 0xa7}},
		{3, [3]byte{0xe2, 0x95, 0xa8}}, {3, [3]byte{0xe2, 0x95, 0xa4}},
		{3, [3]byte{0xe2, 0x95, 0xa5}}, {3, [3]byte{0xe2, 0x95, 0x99}},
		{3, [3]byte{0xe2, 0x95, 0x98}}, {3, [3]byte{0xe2, 0x95, 0x92}},
		{3, [3]byte{0xe2, 0x95, 0x93}}, {3, [3]byte{0xe2, 0x95, 0xab}},
		{3, [3]byte{0xe2, 0x95, 0xaa}}, {3, [3]byte{0xe2, 0x94, 0x98}},
		{3, [3]byte{0xe2, 0x94, 0x8c}}, {3, [3]byte{0xe2, 0x96, 0x88}},
		{3, [3]byte{0xe2, 0x96, 0x84}}, {3, [3]byte{0xe2, 0x96, 0x8c}},
		{3, [3]byte{0xe2, 0x96, 0x90}}, {3, [3]byte{0xe2, 0x96, 0x80}},
		{2, [3]byte{0xce, 0xb1, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xce, 0x93, 0x00}}, {2, [3]byte{0xcf, 0x80, 0x00}},
		{2, [3]byte{0xce, 0xa3, 0x00}}, {2, [3]byte{0xcf, 0x83, 0x00}},
		{2, [3]byte{0xc2, 0xb5, 0x00}}, {2, [3]byte{0xcf, 0x84, 0x00}},
		{2, [3]byte{0xce, 0xa6, 0x00}}, {2, [3]byte{0xce, 0x98, 0x00}},
		{2, [3]byte{0xce, 0xa9, 0x00}}, {2, [3]byte{0xce, 0xb4, 0x00}},
		{3, [3]byte{0xe2, 0x88, 0x9e}}, {2, [3]byte{0xcf, 0x86, 0x00}},
		{2, [3]byte{0xce, 0xb5, 0x00}}, {3, [3]byte{0xe2, 0x88, 0xa9}},
		{3, [3]byte{0xe2, 0x89, 0xa1}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{3, [3]byte{0xe2, 0x89, 0xa5}}, {3, [3]byte{0xe2, 0x89, 0xa4}},
		{3, [3]byte{0xe2, 0x8c, 0xa0}}, {3, [3]byte{0xe2, 0x8c, 0xa1}},
		{2, [3]byte{0xc3, 0xb7, 0x00}}, {3, [3]byte{0xe2, 0x89, 0x88}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {3, [3]byte{0xe2, 0x88, 0x99}},
		{2, [3]byte{0xc2, 0xb7, 0x00}}, {3, [3]byte{0xe2, 0x88, 0x9a}},
		{3, [3]byte{0xe2, 0x81, 0xbf}}, {2, [3]byte{0xc2, 0xb2, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0xa0}}, {2, [3]byte{0xc2, 0xa0, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xff0000a0, 0x9b0000a2, 0x9c0000a3, 0x980000a4, 0xa00000a6, 0x8f0000a7, 0xa40000a8, 0xae0000ab,
		0xaa0000ac, 0xa70000af, 0xf80000b0, 0xf10000b1, 0xfd0000b2, 0xa60000b3, 0xa10000b4, 0xe60000b5,
		0x860000b6, 0xfa0000b7, 0xa50000b8, 0xaf0000bb, 0xac0000bc, 0xab0000bd, 0xad0000be, 0x8e0000c0,
		0x840000c2, 0x800000c7, 0x910000c8, 0x900000c9, 0x920000ca, 0x940000cb, 0xa80000ce, 0x950000cf,
		0x990000d4, 0x9d0000d9, 0x9e0000db, 0x9a0000dc, 0xe10000df, 0x850000e0, 0x830000e2, 0x870000e7,
		0x8a0000e8, 0x820000e9, 0x880000ea, 0x890000eb, 0x8c0000ee, 0x8b0000ef, 0xa20000f3, 0x930000f4,
		0xf60000f7, 0x970000f9, 0xa30000fa, 0x960000fb, 0x810000fc, 0x9f000192, 0xe2000393, 0xe9000398,
		0xe40003a3, 0xe80003a6, 0xea0003a9, 0xe00003b1, 0xeb0003b4, 0xee0003b5, 0xe30003c0, 0xe50003c3,
		0xe70003c4, 0xed0003c6, 0x8d002017, 0xfc00207f, 0xf9002219, 0xfb00221a, 0xec00221e, 0xef002229,
		0xf7002248, 0xf0002261, 0xf3002264, 0xf2002265, 0xa9002310, 0xf4002320, 0xf5002321, 0xc4002500,
		0xb3002502, 0xda00250c, 0xbf002510, 0xc0002514, 0xd9002518, 0xc300251c, 0xb4002524, 0xc200252c,
		0xc1002534, 0xc500253c, 0xcd002550, 0xba002551, 0xd5002552, 0xd6002553, 0xc9002554, 0xb8002555,
		0xb7002556, 0xbb002557, 0xd4002558, 0xd3002559, 0xc800255a, 0xbe00255b, 0xbd00255c, 0xbc00255d,
		0xc600255e, 0xc700255f, 0xcc002560, 0xb5002561, 0xb6002562, 0xb9002563, 0xd1002564, 0xd2002565,
		0xcb002566, 0xcf002567, 0xd0002568, 0xca002569, 0xd800256a, 0xd700256b, 0xce00256c, 0xdf002580,
		0xdc002584, 0xdb002588, 0xdd00258c, 0xde002590, 0xb0002591, 0xb1002592, 0xb2002593, 0xfe0025a0,
	},
}

// CodePage865 is the IBM Code Page 865 encoding.
var CodePage865 *Charmap = &codePage865

var codePage865 = Charmap{
	name:          "IBM Code Page 865",
	mib:           identifier.IBM865,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x87, 0x00}}, {2, [3]byte{0xc3, 0xbc, 0x00}},
		{2, [3]byte{0xc3, 0xa9, 0x00}}, {2, [3]byte{0xc3, 0xa2, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa0, 0x00}},
		{2, [3]byte{0xc3, 0xa5, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xac, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x89, 0x00}}, {2, [3]byte{0xc3, 0xa6, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc3, 0xb4, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb2, 0x00}},
		{2, [3]byte{0xc3, 0xbb, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xbf, 0x00}}, {2, [3]byte{0xc3, 0x96, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0xb8, 0x00}},
		{2, [3]byte{0xc2, 0xa3, 0x00}}, {2, [3]byte{0xc3, 0x98, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xa7}}, {2, [3]byte{0xc6, 0x92, 0x00}},
		{2, [3]byte{0xc3, 0xa1, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xb3, 0x00}}, {2, [3]byte{0xc3, 0xba, 0x00}},
		{2, [3]byte{0xc3, 0xb1, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xba, 0x00}},
		{2, [3]byte{0xc2, 0xbf, 0x00}}, {3, [3]byte{0xe2, 0x8c, 0x90}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xa1, 0x00}},
		{2, [3]byte{0xc2, 0xab, 0x00}}, {2, [3]byte{0xc2, 0xa4, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0x91}}, {3, [3]byte{0xe2, 0x96, 0x92}},
		{3, [3]byte{0xe2, 0x96, 0x93}}, {3, [3]byte{0xe2, 0x94, 0x82}},
		{3, [3]byte{0xe2, 0x94, 0xa4}}, {3, [3]byte{0xe2, 0x95, 0xa1}},
		{3, [3]byte{0xe2, 0x95, 0xa2}}, {3, [3]byte{0xe2, 0x95, 0x96}},
		{3, [3]byte{0xe2, 0x95, 0x95}}, {3, [3]byte{0xe2, 0x95, 0xa3}},
		{3, [3]byte{0xe2, 0x95, 0x91}}, {3, [3]byte{0xe2, 0x95, 0x97}},
		{3, [3]byte{0xe2, 0x95, 0x9d}}, {3, [3]byte{0xe2, 0x95, 0x9c}},
		{3, [3]byte{0xe2, 0x95, 0x9b}}, {3, [3]byte{0xe2, 0x94, 0x90}},
		{3, [3]byte{0xe2, 0x94, 0x94}}, {3, [3]byte{0xe2, 0x94, 0xb4}},
		{3, [3]byte{0xe2, 0x94, 0xac}}, {3, [3]byte{0xe2, 0x94, 0x9c}},
		{3, [3]byte{0xe2, 0x94, 0x80}}, {3, [3]byte{0xe2, 0x94, 0xbc}},
		{3, [3]byte{0xe2, 0x95, 0x9e}}, {3, [3]byte{0xe2, 0x95, 0x9f}},
		{3, [3]byte{0xe2, 0x95, 0x9a}}, {3, [3]byte{0xe2, 0x95, 0x94}},
		{3, [3]byte{0xe2, 0x95, 0xa9}}, {3, [3]byte{0xe2, 0x95, 0xa6}},
		{3, [3]byte{0xe2, 0x95, 0xa0}}, {3, [3]byte{0xe2, 0x95, 0x90}},
		{3, [3]byte{0xe2, 0x95, 0xac}}, {3, [3]byte{0xe2, 0x95, 0xa7}},
		{3, [3]byte{0xe2, 0x95, 0xa8}}, {3, [3]byte{0xe2, 0x95, 0xa4}},
		{3, [3]byte{0xe2, 0x95, 0xa5}}, {3, [3]byte{0xe2, 0x95, 0x99}},
		{3, [3]byte{0xe2, 0x95, 0x98}}, {3, [3]byte{0xe2, 0x95, 0x92}},
		{3, [3]byte{0xe2, 0x95, 0x93}}, {3, [3]byte{0xe2, 0x95, 0xab}},
		{3, [3]byte{0xe2, 0x95, 0xaa}}, {3, [3]byte{0xe2, 0x94, 0x98}},
		{3, [3]byte{0xe2, 0x94, 0x8c}}, {3, [3]byte{0xe2, 0x96, 0x88}},
		{3, [3]byte{0xe2, 0x96, 0x84}}, {3, [3]byte{0xe2, 0x96, 0x8c}},
		{3, [3]byte{0xe2, 0x96, 0x90}}, {3, [3]byte{0xe2, 0x96, 0x80}},
		{2, [3]byte{0xce, 0xb1, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xce, 0x93, 0x00}}, {2, [3]byte{0xcf, 0x80, 0x00}},
		{2, [3]byte{0xce, 0xa3, 0x00}}, {2, [3]byte{0xcf, 0x83, 0x00}},
		{2, [3]byte{0xc2, 0xb5, 0x00}}, {2, [3]byte{0xcf, 0x84, 0x00}},
		{2, [3]byte{0xce, 0xa6, 0x00}}, {2, [3]byte{0xce, 0x98, 0x00}},
		{2, [3]byte{0xce, 0xa9, 0x00}}, {2, [3]byte{0xce, 0xb4, 0x00}},
		{3, [3]byte{0xe2, 0x88, 0x9e}}, {2, [3]byte{0xcf, 0x86, 0x00}},
		{2, [3]byte{0xce, 0xb5, 0x00}}, {3, [3]byte{0xe2, 0x88, 0xa9}},
		{3, [3]byte{0xe2, 0x89, 0xa1}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{3, [3]byte{0xe2, 0x89, 0xa5}}, {3, [3]byte{0xe2, 0x89, 0xa4}},
		{3, [3]byte{0xe2, 0x8c, 0xa0}}, {3, [3]byte{0xe2, 0x8c, 0xa1}},
		{2, [3]byte{0xc3, 0xb7, 0x00}}, {3, [3]byte{0xe2, 0x89, 0x88}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {3, [3]byte{0xe2, 0x88, 0x99}},
		{2, [3]byte{0xc2, 0xb7, 0x00}}, {3, [3]byte{0xe2, 0x88, 0x9a}},
		{3, [3]byte{0xe2, 0x81, 0xbf}}, {2, [3]byte{0xc2, 0xb2, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0xa0}}, {2, [3]byte{0xc2, 0xa0, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xff0000a0, 0xad0000a1, 0x9c0000a3, 0xaf0000a4, 0xa60000aa, 0xae0000ab, 0xaa0000ac, 0xf80000b0,
		0xf10000b1, 0xfd0000b2, 0xe60000b5, 0xfa0000b7, 0xa70000ba, 0xac0000bc, 0xab0000bd, 0xa80000bf,
		0x8e0000c4, 0x8f0000c5, 0x920000c6, 0x800000c7, 0x900000c9, 0xa50000d1, 0x990000d6, 0x9d0000d8,
		0x9a0000dc, 0xe10000df, 0x850000e0, 0xa00000e1, 0x830000e2, 0x840000e4, 0x860000e5, 0x910000e6,
		0x870000e7, 0x8a0000e8, 0x820000e9, 0x880000ea, 0x890000eb, 0x8d0000ec, 0xa10000ed, 0x8c0000ee,
		0x8b0000ef, 0xa40000f1, 0x950000f2, 0xa20000f3, 0x930000f4, 0x940000f6, 0xf60000f7, 0x9b0000f8,
		0x970000f9, 0xa30000fa, 0x960000fb, 0x810000fc, 0x980000ff, 0x9f000192, 0xe2000393, 0xe9000398,
		0xe40003a3, 0xe80003a6, 0xea0003a9, 0xe00003b1, 0xeb0003b4, 0xee0003b5, 0xe30003c0, 0xe50003c3,
		0xe70003c4, 0xed0003c6, 0xfc00207f, 0x9e0020a7, 0xf9002219, 0xfb00221a, 0xec00221e, 0xef002229,
		0xf7002248, 0xf0002261, 0xf3002264, 0xf2002265, 0xa9002310, 0xf4002320, 0xf5002321, 0xc4002500,
		0xb3002502, 0xda00250c, 0xbf002510, 0xc0002514, 0xd9002518, 0xc300251c, 0xb4002524, 0xc200252c,
		0xc1002534, 0xc500253c, 0xcd002550, 0xba002551, 0xd5002552, 0xd6002553, 0xc9002554, 0xb8002555,
		0xb7002556, 0xbb002557, 0xd4002558, 0xd3002559, 0xc800255a, 0xbe00255b, 0xbd00255c, 0xbc00255d,
		0xc600255e, 0xc700255f, 0xcc002560, 0xb5002561, 0xb6002562, 0xb9002563, 0xd1002564, 0xd2002565,
		0xcb002566, 0xcf002567, 0xd0002568, 0xca002569, 0xd800256a, 0xd700256b, 0xce00256c, 0xdf002580,
		0xdc002584, 0xdb002588, 0xdd00258c, 0xde002590, 0xb0002591, 0xb1002592, 0xb2002593, 0xfe0025a0,
	},
}

// CodePage866 is the IBM Code Page 866 encoding.
var CodePage866 *Charmap = &codePage866

var codePage866 = Charmap{
	name:          "IBM Code Page 866",
	mib:           identifier.IBM866,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xd0, 0x90, 0x00}}, {2, [3]byte{0xd0, 0x91, 0x00}},
		{2, [3]byte{0xd0, 0x92, 0x00}}, {2, [3]byte{0xd0, 0x93, 0x00}},
		{2, [3]byte{0xd0, 0x94, 0x00}}, {2, [3]byte{0xd0, 0x95, 0x00}},
		{2, [3]byte{0xd0, 0x96, 0x00}}, {2, [3]byte{0xd0, 0x97, 0x00}},
		{2, [3]byte{0xd0, 0x98, 0x00}}, {2, [3]byte{0xd0, 0x99, 0x00}},
		{2, [3]byte{0xd0, 0x9a, 0x00}}, {2, [3]byte{0xd0, 0x9b, 0x00}},
		{2, [3]byte{0xd0, 0x9c, 0x00}}, {2, [3]byte{0xd0, 0x9d, 0x00}},
		{2, [3]byte{0xd0, 0x9e, 0x00}}, {2, [3]byte{0xd0, 0x9f, 0x00}},
		{2, [3]byte{0xd0, 0xa0, 0x00}}, {2, [3]byte{0xd0, 0xa1, 0x00}},
		{2, [3]byte{0xd0, 0xa2, 0x00}}, {2, [3]byte{0xd0, 0xa3, 0x00}},
		{2, [3]byte{0xd0, 0xa4, 0x00}}, {2, [3]byte{0xd0, 0xa5, 0x00}},
		{2, [3]byte{0xd0, 0xa6, 0x00}}, {2, [3]byte{0xd0, 0xa7, 0x00}},
		{2, [3]byte{0xd0, 0xa8, 0x00}}, {2, [3]byte{0xd0, 0xa9, 0x00}},
		{2, [3]byte{0xd0, 0xaa, 0x00}}, {2, [3]byte{0xd0, 0xab, 0x00}},
		{2, [3]byte{0xd0, 0xac, 0x00}}, {2, [3]byte{0xd0, 0xad, 0x00}},
		{2, [3]byte{0xd0, 0xae, 0x00}}, {2, [3]byte{0xd0, 0xaf, 0x00}},
		{2, [3]byte{0xd0, 0xb0, 0x00}}, {2, [3]byte{0xd0, 0xb1, 0x00}},
		{2, [3]byte{0xd0, 0xb2, 0x00}}, {2, [3]byte{0xd0, 0xb3, 0x00}},
		{2, [3]byte{0xd0, 0xb4, 0x00}}, {2, [3]byte{0xd0, 0xb5, 0x00}},
		{2, [3]byte{0xd0, 0xb6, 0x00}}, {2, [3]byte{0xd0, 0xb7, 0x00}},
		{2, [3]byte{0xd0, 0xb8, 0x00}}, {2, [3]byte{0xd0, 0xb9, 0x00}},
		{2, [3]byte{0xd0, 0xba, 0x00}}, {2, [3]byte{0xd0, 0xbb, 0x00}},
		{2, [3]byte{0xd0, 0xbc, 0x00}}, {2, [3]byte{0xd0, 0xbd, 0x00}},
		{2, [3]byte{0xd0, 0xbe, 0x00}}, {2, [3]byte{0xd0, 0xbf, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0x91}}, {3, [3]byte{0xe2, 0x96, 0x92}},
		{3, [3]byte{0xe2, 0x96, 0x93}}, {3, [3]byte{0xe2, 0x94, 0x82}},
		{3, [3]byte{0xe2, 0x94, 0xa4}}, {3, [3]byte{0xe2, 0x95, 0xa1}},
		{3, [3]byte{0xe2, 0x95, 0xa2}}, {3, [3]byte{0xe2, 0x95, 0x96}},
		{3, [3]byte{0xe2, 0x95, 0x95}}, {3, [3]byte{0xe2, 0x95, 0xa3}},
		{3, [3]byte{0xe2, 0x95, 0x91}}, {3, [3]byte{0xe2, 0x95, 0x97}},
		{3, [3]byte{0xe2, 0x95, 0x9d}}, {3, [3]byte{0xe2, 0x95, 0x9c}},
		{3, [3]byte{0xe2, 0x95, 0x9b}}, {3, [3]byte{0xe2, 0x94, 0x90}},
		{3, [3]byte{0xe2, 0x94, 0x94}}, {3, [3]byte{0xe2, 0x94, 0xb4}},
		{3, [3]byte{0xe2, 0x94, 0xac}}, {3, [3]byte{0xe2, 0x94, 0x9c}},
		{3, [3]byte{0xe2, 0x94, 0x80}}, {3, [3]byte{0xe2, 0x94, 0xbc}},
		{3, [3]byte{0xe2, 0x95, 0x9e}}, {3, [3]byte{0xe2, 0x95, 0x9f}},
		{3, [3]byte{0xe2, 0x95, 0x9a}}, {3, [3]byte{0xe2, 0x95, 0x94}},
		{3, [3]byte{0xe2, 0x95, 0xa9}}, {3, [3]byte{0xe2, 0x95, 0xa6}},
		{3, [3]byte{0xe2, 0x95, 0xa0}}, {3, [3]byte{0xe2, 0x95, 0x90}},
		{3, [3]byte{0xe2, 0x95, 0xac}}, {3, [3]byte{0xe2, 0x95, 0xa7}},
		{3, [3]byte{0xe2, 0x95, 0xa8}}, {3, [3]byte{0xe2, 0x95, 0xa4}},
		{3, [3]byte{0xe2, 0x95, 0xa5}}, {3, [3]byte{0xe2, 0x95, 0x99}},
		{3, [3]byte{0xe2, 0x95, 0x98}}, {3, [3]byte{0xe2, 0x95, 0x92}},
		{3, [3]byte{0xe2, 0x95, 0x93}}, {3, [3]byte{0xe2, 0x95, 0xab}},
		{3, [3]byte{0xe2, 0x95, 0xaa}}, {3, [3]byte{0xe2, 0x94, 0x98}},
		{3, [3]byte{0xe2, 0x94, 0x8c}}, {3, [3]byte{0xe2, 0x96, 0x88}},
		{3, [3]byte{0xe2, 0x96, 0x84}}, {3, [3]byte{0xe2, 0x96, 0x8c}},
		{3, [3]byte{0xe2, 0x96, 0x90}}, {3, [3]byte{0xe2, 0x96, 0x80}},
		{2, [3]byte{0xd1, 0x80, 0x00}}, {2, [3]byte{0xd1, 0x81, 0x00}},
		{2, [3]byte{0xd1, 0x82, 0x00}}, {2, [3]byte{0xd1, 0x83, 0x00}},
		{2, [3]byte{0xd1, 0x84, 0x00}}, {2, [3]byte{0xd1, 0x85, 0x00}},
		{2, [3]byte{0xd1, 0x86, 0x00}}, {2, [3]byte{0xd1, 0x87, 0x00}},
		{2, [3]byte{0xd1, 0x88, 0x00}}, {2, [3]byte{0xd1, 0x89, 0x00}},
		{2, [3]byte{0xd1, 0x8a, 0x00}}, {2, [3]byte{0xd1, 0x8b, 0x00}},
		{2, [3]byte{0xd1, 0x8c, 0x00}}, {2, [3]byte{0xd1, 0x8d, 0x00}},
		{2, [3]byte{0xd1, 0x8e, 0x00}}, {2, [3]byte{0xd1, 0x8f, 0x00}},
		{2, [3]byte{0xd0, 0x81, 0x00}}, {2, [3]byte{0xd1, 0x91, 0x00}},
		{2, [3]byte{0xd0, 0x84, 0x00}}, {2, [3]byte{0xd1, 0x94, 0x00}},
		{2, [3]byte{0xd0, 0x87, 0x00}}, {2, [3]byte{0xd1, 0x97, 0x00}},
		{2, [3]byte{0xd0, 0x8e, 0x00}}, {2, [3]byte{0xd1, 0x9e, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {3, [3]byte{0xe2, 0x88, 0x99}},
		{2, [3]byte{0xc2, 0xb7, 0x00}}, {3, [3]byte{0xe2, 0x88, 0x9a}},
		{3, [3]byte{0xe2, 0x84, 0x96}}, {2, [3]byte{0xc2, 0xa4, 0x00}},
		{3, [3]byte{0xe2, 0x96, 0xa0}}, {2, [3]byte{0xc2, 0xa0, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xff0000a0, 0xfd0000a4, 0xf80000b0, 0xfa0000b7, 0xf0000401, 0xf2000404, 0xf4000407, 0xf600040e,
		0x80000410, 0x81000411, 0x82000412, 0x83000413, 0x84000414, 0x85000415, 0x86000416, 0x87000417,
		0x88000418, 0x89000419, 0x8a00041a, 0x8b00041b, 0x8c00041c, 0x8d00041d, 0x8e00041e, 0x8f00041f,
		0x90000420, 0x91000421, 0x92000422, 0x93000423, 0x94000424, 0x95000425, 0x96000426, 0x97000427,
		0x98000428, 0x99000429, 0x9a00042a, 0x9b00042b, 0x9c00042c, 0x9d00042d, 0x9e00042e, 0x9f00042f,
		0xa0000430, 0xa1000431, 0xa2000432, 0xa3000433, 0xa4000434, 0xa5000435, 0xa6000436, 0xa7000437,
		0xa8000438, 0xa9000439, 0xaa00043a, 0xab00043b, 0xac00043c, 0xad00043d, 0xae00043e, 0xaf00043f,
		0xe0000440, 0xe1000441, 0xe2000442, 0xe3000443, 0xe4000444, 0xe5000445, 0xe6000446, 0xe7000447,
		0xe8000448, 0xe9000449, 0xea00044a, 0xeb00044b, 0xec00044c, 0xed00044d, 0xee00044e, 0xef00044f,
		0xf1000451, 0xf3000454, 0xf5000457, 0xf700045e, 0xfc002116, 0xf9002219, 0xfb00221a, 0xc4002500,
		0xb3002502, 0xda00250c, 0xbf002510, 0xc0002514, 0xd9002518, 0xc300251c, 0xb4002524, 0xc200252c,
		0xc1002534, 0xc500253c, 0xcd002550, 0xba002551, 0xd5002552, 0xd6002553, 0xc9002554, 0xb8002555,
		0xb7002556, 0xbb002557, 0xd4002558, 0xd3002559, 0xc800255a, 0xbe00255b, 0xbd00255c, 0xbc00255d,
		0xc600255e, 0xc700255f, 0xcc002560, 0xb5002561, 0xb6002562, 0xb9002563, 0xd1002564, 0xd2002565,
		0xcb002566, 0xcf002567, 0xd0002568, 0xca002569, 0xd800256a, 0xd700256b, 0xce00256c, 0xdf002580,
		0xdc002584, 0xdb002588, 0xdd00258c, 0xde002590, 0xb0002591, 0xb1002592, 0xb2002593, 0xfe0025a0,
	},
}

// CodePage1047 is the IBM Code Page 1047 encoding.
var CodePage1047 *Charmap = &codePage1047

var codePage1047 = Charmap{
	name:          "IBM Code Page 1047",
	mib:           identifier.IBM1047,
	asciiSuperset: false,
	low:           0x00,
	replacement:   0x3f,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x9c, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x86, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x97, 0x00}}, {2, [3]byte{0xc2, 0x8d, 0x00}},
		{2, [3]byte{0xc2, 0x8e, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x9d, 0x00}}, {2, [3]byte{0xc2, 0x85, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {2, [3]byte{0xc2, 0x87, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x92, 0x00}}, {2, [3]byte{0xc2, 0x8f, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x80, 0x00}}, {2, [3]byte{0xc2, 0x81, 0x00}},
		{2, [3]byte{0xc2, 0x82, 0x00}}, {2, [3]byte{0xc2, 0x83, 0x00}},
		{2, [3]byte{0xc2, 0x84, 0x00}}, {1, [3]byte{0x0a, 0x00, 0x00}},
		{1, [3]byte{0x17, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x88, 0x00}}, {2, [3]byte{0xc2, 0x89, 0x00}},
		{2, [3]byte{0xc2, 0x8a, 0x00}}, {2, [3]byte{0xc2, 0x8b, 0x00}},
		{2, [3]byte{0xc2, 0x8c, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x90, 0x00}}, {2, [3]byte{0xc2, 0x91, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {2, [3]byte{0xc2, 0x93, 0x00}},
		{2, [3]byte{0xc2, 0x94, 0x00}}, {2, [3]byte{0xc2, 0x95, 0x00}},
		{2, [3]byte{0xc2, 0x96, 0x00}}, {1, [3]byte{0x04, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x98, 0x00}}, {2, [3]byte{0xc2, 0x99, 0x00}},
		{2, [3]byte{0xc2, 0x9a, 0x00}}, {2, [3]byte{0xc2, 0x9b, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x9e, 0x00}}, {1, [3]byte{0x1a, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {2, [3]byte{0xc2, 0xa0, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc3, 0xa4, 0x00}},
		{2, [3]byte{0xc3, 0xa0, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa3, 0x00}}, {2, [3]byte{0xc3, 0xa5, 0x00}},
		{2, [3]byte{0xc3, 0xa7, 0x00}}, {2, [3]byte{0xc3, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {1, [3]byte{0x2e, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x28, 0x00, 0x00}},
		{1, [3]byte{0x2b, 0x00, 0x00}}, {1, [3]byte{0x7c, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xac, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{1, [3]byte{0x21, 0x00, 0x00}}, {1, [3]byte{0x24, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x3b, 0x00, 0x00}}, {1, [3]byte{0x5e, 0x00, 0x00}},
		{1, [3]byte{0x2d, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc3, 0x84, 0x00}},
		{2, [3]byte{0xc3, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x83, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x87, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {1, [3]byte{0x2c, 0x00, 0x00}},
		{1, [3]byte{0x25, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0xb8, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc3, 0x8f, 0x00}},
		{2, [3]byte{0xc3, 0x8c, 0x00}}, {1, [3]byte{0x60, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x3d, 0x00, 0x00}}, {1, [3]byte{0x22, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x98, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xab, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xb0, 0x00}}, {2, [3]byte{0xc3, 0xbd, 0x00}},
		{2, [3]byte{0xc3, 0xbe, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {1, [3]byte{0x6a, 0x00, 0x00}},
		{1, [3]byte{0x6b, 0x00, 0x00}}, {1, [3]byte{0x6c, 0x00, 0x00}},
		{1, [3]byte{0x6d, 0x00, 0x00}}, {1, [3]byte{0x6e, 0x00, 0x00}},
		{1, [3]byte{0x6f, 0x00, 0x00}}, {1, [3]byte{0x70, 0x00, 0x00}},
		{1, [3]byte{0x71, 0x00, 0x00}}, {1, [3]byte{0x72, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xba, 0x00}},
		{2, [3]byte{0xc3, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xb8, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc2, 0xa4, 0x00}},
		{2, [3]byte{0xc2, 0xb5, 0x00}}, {1, [3]byte{0x7e, 0x00, 0x00}},
		{1, [3]byte{0x73, 0x00, 0x00}}, {1, [3]byte{0x74, 0x00, 0x00}},
		{1, [3]byte{0x75, 0x00, 0x00}}, {1, [3]byte{0x76, 0x00, 0x00}},
		{1, [3]byte{0x77, 0x00, 0x00}}, {1, [3]byte{0x78, 0x00, 0x00}},
		{1, [3]byte{0x79, 0x00, 0x00}}, {1, [3]byte{0x7a, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xa1, 0x00}}, {2, [3]byte{0xc2, 0xbf, 0x00}},
		{2, [3]byte{0xc3, 0x90, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x9e, 0x00}}, {2, [3]byte{0xc2, 0xae, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa5, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc2, 0xa9, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xbc, 0x00}},
		{2, [3]byte{0xc2, 0xbd, 0x00}}, {2, [3]byte{0xc2, 0xbe, 0x00}},
		{2, [3]byte{0xc3, 0x9d, 0x00}}, {2, [3]byte{0xc2, 0xa8, 0x00}},
		{2, [3]byte{0xc2, 0xaf, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{1, [3]byte{0x7b, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xad, 0x00}}, {2, [3]byte{0xc3, 0xb4, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb2, 0x00}},
		{2, [3]byte{0xc3, 0xb3, 0x00}}, {2, [3]byte{0xc3, 0xb5, 0x00}},
		{1, [3]byte{0x7d, 0x00, 0x00}}, {1, [3]byte{0x4a, 0x00, 0x00}},
		{1, [3]byte{0x4b, 0x00, 0x00}}, {1, [3]byte{0x4c, 0x00, 0x00}},
		{1, [3]byte{0x4d, 0x00, 0x00}}, {1, [3]byte{0x4e, 0x00, 0x00}},
		{1, [3]byte{0x4f, 0x00, 0x00}}, {1, [3]byte{0x50, 0x00, 0x00}},
		{1, [3]byte{0x51, 0x00, 0x00}}, {1, [3]byte{0x52, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xb9, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xbf, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{1, [3]byte{0x53, 0x00, 0x00}}, {1, [3]byte{0x54, 0x00, 0x00}},
		{1, [3]byte{0x55, 0x00, 0x00}}, {1, [3]byte{0x56, 0x00, 0x00}},
		{1, [3]byte{0x57, 0x00, 0x00}}, {1, [3]byte{0x58, 0x00, 0x00}},
		{1, [3]byte{0x59, 0x00, 0x00}}, {1, [3]byte{0x5a, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc3, 0x94, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x92, 0x00}},
		{2, [3]byte{0xc3, 0x93, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xb3, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc2, 0x9f, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x37000004, 0x2d000005, 0x2e000006, 0x2f000007,
		0x16000008, 0x05000009, 0x2500000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x3c000014, 0x3d000015, 0x32000016, 0x26000017,
		0x18000018, 0x19000019, 0x3f00001a, 0x2700001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x40000020, 0x5a000021, 0x7f000022, 0x7b000023, 0x5b000024, 0x6c000025, 0x50000026, 0x7d000027,
		0x4d000028, 0x5d000029, 0x5c00002a, 0x4e00002b, 0x6b00002c, 0x6000002d, 0x4b00002e, 0x6100002f,
		0xf0000030, 0xf1000031, 0xf2000032, 0xf3000033, 0xf4000034, 0xf5000035, 0xf6000036, 0xf7000037,
		0xf8000038, 0xf9000039, 0x7a00003a, 0x5e00003b, 0x4c00003c, 0x7e00003d, 0x6e00003e, 0x6f00003f,
		0x7c000040, 0xc1000041, 0xc2000042, 0xc3000043, 0xc4000044, 0xc5000045, 0xc6000046, 0xc7000047,
		0xc8000048, 0xc9000049, 0xd100004a, 0xd200004b, 0xd300004c, 0xd400004d, 0xd500004e, 0xd600004f,
		0xd7000050, 0xd8000051, 0xd9000052, 0xe2000053, 0xe3000054, 0xe4000055, 0xe5000056, 0xe6000057,
		0xe7000058, 0xe8000059, 0xe900005a, 0xad00005b, 0xe000005c, 0xbd00005d, 0x5f00005e, 0x6d00005f,
		0x79000060, 0x81000061, 0x82000062, 0x83000063, 0x84000064, 0x85000065, 0x86000066, 0x87000067,
		0x88000068, 0x89000069, 0x9100006a, 0x9200006b, 0x9300006c, 0x9400006d, 0x9500006e, 0x9600006f,
		0x97000070, 0x98000071, 0x99000072, 0xa2000073, 0xa3000074, 0xa4000075, 0xa5000076, 0xa6000077,
		0xa7000078, 0xa8000079, 0xa900007a, 0xc000007b, 0x4f00007c, 0xd000007d, 0xa100007e, 0x0700007f,
		0x20000080, 0x21000081, 0x22000082, 0x23000083, 0x24000084, 0x15000085, 0x06000086, 0x17000087,
		0x28000088, 0x29000089, 0x2a00008a, 0x2b00008b, 0x2c00008c, 0x0900008d, 0x0a00008e, 0x1b00008f,
		0x30000090, 0x31000091, 0x1a000092, 0x33000093, 0x34000094, 0x35000095, 0x36000096, 0x08000097,
		0x38000098, 0x39000099, 0x3a00009a, 0x3b00009b, 0x0400009c, 0x1400009d, 0x3e00009e, 0xff00009f,
		0x410000a0, 0xaa0000a1, 0x4a0000a2, 0xb10000a3, 0x9f0000a4, 0xb20000a5, 0x6a0000a6, 0xb50000a7,
		0xbb0000a8, 0xb40000a9, 0x9a0000aa, 0x8a0000ab, 0xb00000ac, 0xca0000ad, 0xaf0000ae, 0xbc0000af,
		0x900000b0, 0x8f0000b1, 0xea0000b2, 0xfa0000b3, 0xbe0000b4, 0xa00000b5, 0xb60000b6, 0xb30000b7,
		0x9d0000b8, 0xda0000b9, 0x9b0000ba, 0x8b0000bb, 0xb70000bc, 0xb80000bd, 0xb90000be, 0xab0000bf,
		0x640000c0, 0x650000c1, 0x620000c2, 0x660000c3, 0x630000c4, 0x670000c5, 0x9e0000c6, 0x680000c7,
		0x740000c8, 0x710000c9, 0x720000ca, 0x730000cb, 0x780000cc, 0x750000cd, 0x760000ce, 0x770000cf,
		0xac0000d0, 0x690000d1, 0xed0000d2, 0xee0000d3, 0xeb0000d4, 0xef0000d5, 0xec0000d6, 0xbf0000d7,
		0x800000d8, 0xfd0000d9, 0xfe0000da, 0xfb0000db, 0xfc0000dc, 0xba0000dd, 0xae0000de, 0x590000df,
		0x440000e0, 0x450000e1, 0x420000e2, 0x460000e3, 0x430000e4, 0x470000e5, 0x9c0000e6, 0x480000e7,
		0x540000e8, 0x510000e9, 0x520000ea, 0x530000eb, 0x580000ec, 0x550000ed, 0x560000ee, 0x570000ef,
		0x8c0000f0, 0x490000f1, 0xcd0000f2, 0xce0000f3, 0xcb0000f4, 0xcf0000f5, 0xcc0000f6, 0xe10000f7,
		0x700000f8, 0xdd0000f9, 0xde0000fa, 0xdb0000fb, 0xdc0000fc, 0x8d0000fd, 0x8e0000fe, 0xdf0000ff,
	},
}

// CodePage1140 is the IBM Code Page 1140 encoding.
var CodePage1140 *Charmap = &codePage1140

var codePage1140 = Charmap{
	name:          "IBM Code Page 1140",
	mib:           identifier.IBM01140,
	asciiSuperset: false,
	low:           0x00,
	replacement:   0x3f,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x9c, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x86, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x97, 0x00}}, {2, [3]byte{0xc2, 0x8d, 0x00}},
		{2, [3]byte{0xc2, 0x8e, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x9d, 0x00}}, {2, [3]byte{0xc2, 0x85, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {2, [3]byte{0xc2, 0x87, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x92, 0x00}}, {2, [3]byte{0xc2, 0x8f, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x80, 0x00}}, {2, [3]byte{0xc2, 0x81, 0x00}},
		{2, [3]byte{0xc2, 0x82, 0x00}}, {2, [3]byte{0xc2, 0x83, 0x00}},
		{2, [3]byte{0xc2, 0x84, 0x00}}, {1, [3]byte{0x0a, 0x00, 0x00}},
		{1, [3]byte{0x17, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x88, 0x00}}, {2, [3]byte{0xc2, 0x89, 0x00}},
		{2, [3]byte{0xc2, 0x8a, 0x00}}, {2, [3]byte{0xc2, 0x8b, 0x00}},
		{2, [3]byte{0xc2, 0x8c, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x90, 0x00}}, {2, [3]byte{0xc2, 0x91, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {2, [3]byte{0xc2, 0x93, 0x00}},
		{2, [3]byte{0xc2, 0x94, 0x00}}, {2, [3]byte{0xc2, 0x95, 0x00}},
		{2, [3]byte{0xc2, 0x96, 0x00}}, {1, [3]byte{0x04, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x98, 0x00}}, {2, [3]byte{0xc2, 0x99, 0x00}},
		{2, [3]byte{0xc2, 0x9a, 0x00}}, {2, [3]byte{0xc2, 0x9b, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x9e, 0x00}}, {1, [3]byte{0x1a, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {2, [3]byte{0xc2, 0xa0, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc3, 0xa4, 0x00}},
		{2, [3]byte{0xc3, 0xa0, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa3, 0x00}}, {2, [3]byte{0xc3, 0xa5, 0x00}},
		{2, [3]byte{0xc3, 0xa7, 0x00}}, {2, [3]byte{0xc3, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {1, [3]byte{0x2e, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x28, 0x00, 0x00}},
		{1, [3]byte{0x2b, 0x00, 0x00}}, {1, [3]byte{0x7c, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xac, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{1, [3]byte{0x21, 0x00, 0x00}}, {1, [3]byte{0x24, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x3b, 0x00, 0x00}}, {2, [3]byte{0xc2, 0xac, 0x00}},
		{1, [3]byte{0x2d, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc3, 0x84, 0x00}},
		{2, [3]byte{0xc3, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x83, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x87, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {1, [3]byte{0x2c, 0x00, 0x00}},
		{1, [3]byte{0x25, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0xb8, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc3, 0x8f, 0x00}},
		{2, [3]byte{0xc3, 0x8c, 0x00}}, {1, [3]byte{0x60, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x3d, 0x00, 0x00}}, {1, [3]byte{0x22, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x98, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xab, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xb0, 0x00}}, {2, [3]byte{0xc3, 0xbd, 0x00}},
		{2, [3]byte{0xc3, 0xbe, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {1, [3]byte{0x6a, 0x00, 0x00}},
		{1, [3]byte{0x6b, 0x00, 0x00}}, {1, [3]byte{0x6c, 0x00, 0x00}},
		{1, [3]byte{0x6d, 0x00, 0x00}}, {1, [3]byte{0x6e, 0x00, 0x00}},
		{1, [3]byte{0x6f, 0x00, 0x00}}, {1, [3]byte{0x70, 0x00, 0x00}},
		{1, [3]byte{0x71, 0x00, 0x00}}, {1, [3]byte{0x72, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xba, 0x00}},
		{2, [3]byte{0xc3, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xb8, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {3, [3]byte{0xe2, 0x82, 0xac}},
		{2, [3]byte{0xc2, 0xb5, 0x00}}, {1, [3]byte{0x7e, 0x00, 0x00}},
		{1, [3]byte{0x73, 0x00, 0x00}}, {1, [3]byte{0x74, 0x00, 0x00}},
		{1, [3]byte{0x75, 0x00, 0x00}}, {1, [3]byte{0x76, 0x00, 0x00}},
		{1, [3]byte{0x77, 0x00, 0x00}}, {1, [3]byte{0x78, 0x00, 0x00}},
		{1, [3]byte{0x79, 0x00, 0x00}}, {1, [3]byte{0x7a, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xa1, 0x00}}, {2, [3]byte{0xc2, 0xbf, 0x00}},
		{2, [3]byte{0xc3, 0x90, 0x00}}, {2, [3]byte{0xc3, 0x9d, 0x00}},
		{2, [3]byte{0xc3, 0x9e, 0x00}}, {2, [3]byte{0xc2, 0xae, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa5, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc2, 0xa9, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xbc, 0x00}},
		{2, [3]byte{0xc2, 0xbd, 0x00}}, {2, [3]byte{0xc2, 0xbe, 0x00}},
		{1, [3]byte{0x5b, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xaf, 0x00}}, {2, [3]byte{0xc2, 0xa8, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{1, [3]byte{0x7b, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xad, 0x00}}, {2, [3]byte{0xc3, 0xb4, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb2, 0x00}},
		{2, [3]byte{0xc3, 0xb3, 0x00}}, {2, [3]byte{0xc3, 0xb5, 0x00}},
		{1, [3]byte{0x7d, 0x00, 0x00}}, {1, [3]byte{0x4a, 0x00, 0x00}},
		{1, [3]byte{0x4b, 0x00, 0x00}}, {1, [3]byte{0x4c, 0x00, 0x00}},
		{1, [3]byte{0x4d, 0x00, 0x00}}, {1, [3]byte{0x4e, 0x00, 0x00}},
		{1, [3]byte{0x4f, 0x00, 0x00}}, {1, [3]byte{0x50, 0x00, 0x00}},
		{1, [3]byte{0x51, 0x00, 0x00}}, {1, [3]byte{0x52, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xb9, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xbf, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{1, [3]byte{0x53, 0x00, 0x00}}, {1, [3]byte{0x54, 0x00, 0x00}},
		{1, [3]byte{0x55, 0x00, 0x00}}, {1, [3]byte{0x56, 0x00, 0x00}},
		{1, [3]byte{0x57, 0x00, 0x00}}, {1, [3]byte{0x58, 0x00, 0x00}},
		{1, [3]byte{0x59, 0x00, 0x00}}, {1, [3]byte{0x5a, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc3, 0x94, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x92, 0x00}},
		{2, [3]byte{0xc3, 0x93, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0xb3, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc2, 0x9f, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x37000004, 0x2d000005, 0x2e000006, 0x2f000007,
		0x16000008, 0x05000009, 0x2500000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x3c000014, 0x3d000015, 0x32000016, 0x26000017,
		0x18000018, 0x19000019, 0x3f00001a, 0x2700001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x40000020, 0x5a000021, 0x7f000022, 0x7b000023, 0x5b000024, 0x6c000025, 0x50000026, 0x7d000027,
		0x4d000028, 0x5d000029, 0x5c00002a, 0x4e00002b, 0x6b00002c, 0x6000002d, 0x4b00002e, 0x6100002f,
		0xf0000030, 0xf1000031, 0xf2000032, 0xf3000033, 0xf4000034, 0xf5000035, 0xf6000036, 0xf7000037,
		0xf8000038, 0xf9000039, 0x7a00003a, 0x5e00003b, 0x4c00003c, 0x7e00003d, 0x6e00003e, 0x6f00003f,
		0x7c000040, 0xc1000041, 0xc2000042, 0xc3000043, 0xc4000044, 0xc5000045, 0xc6000046, 0xc7000047,
		0xc8000048, 0xc9000049, 0xd100004a, 0xd200004b, 0xd300004c, 0xd400004d, 0xd500004e, 0xd600004f,
		0xd7000050, 0xd8000051, 0xd9000052, 0xe2000053, 0xe3000054, 0xe4000055, 0xe5000056, 0xe6000057,
		0xe7000058, 0xe8000059, 0xe900005a, 0xba00005b, 0xe000005c, 0xbb00005d, 0xb000005e, 0x6d00005f,
		0x79000060, 0x81000061, 0x82000062, 0x83000063, 0x84000064, 0x85000065, 0x86000066, 0x87000067,
		0x88000068, 0x89000069, 0x9100006a, 0x9200006b, 0x9300006c, 0x9400006d, 0x9500006e, 0x9600006f,
		0x97000070, 0x98000071, 0x99000072, 0xa2000073, 0xa3000074, 0xa4000075, 0xa5000076, 0xa6000077,
		0xa7000078, 0xa8000079, 0xa900007a, 0xc000007b, 0x4f00007c, 0xd000007d, 0xa100007e, 0x0700007f,
		0x20000080, 0x21000081, 0x22000082, 0x23000083, 0x24000084, 0x15000085, 0x06000086, 0x17000087,
		0x28000088, 0x29000089, 0x2a00008a, 0x2b00008b, 0x2c00008c, 0x0900008d, 0x0a00008e, 0x1b00008f,
		0x30000090, 0x31000091, 0x1a000092, 0x33000093, 0x34000094, 0x35000095, 0x36000096, 0x08000097,
		0x38000098, 0x39000099, 0x3a00009a, 0x3b00009b, 0x0400009c, 0x1400009d, 0x3e00009e, 0xff00009f,
		0x410000a0, 0xaa0000a1, 0x4a0000a2, 0xb10000a3, 0xb20000a5, 0x6a0000a6, 0xb50000a7, 0xbd0000a8,
		0xb40000a9, 0x9a0000aa, 0x8a0000ab, 0x5f0000ac, 0xca0000ad, 0xaf0000ae, 0xbc0000af, 0x900000b0,
		0x8f0000b1, 0xea0000b2, 0xfa0000b3, 0xbe0000b4, 0xa00000b5, 0xb60000b6, 0xb30000b7, 0x9d0000b8,
		0xda0000b9, 0x9b0000ba, 0x8b0000bb, 0xb70000bc, 0xb80000bd, 0xb90000be, 0xab0000bf, 0x640000c0,
		0x650000c1, 0x620000c2, 0x660000c3, 0x630000c4, 0x670000c5, 0x9e0000c6, 0x680000c7, 0x740000c8,
		0x710000c9, 0x720000ca, 0x730000cb, 0x780000cc, 0x750000cd, 0x760000ce, 0x770000cf, 0xac0000d0,
		0x690000d1, 0xed0000d2, 0xee0000d3, 0xeb0000d4, 0xef0000d5, 0xec0000d6, 0xbf0000d7, 0x800000d8,
		0xfd0000d9, 0xfe0000da, 0xfb0000db, 0xfc0000dc, 0xad0000dd, 0xae0000de, 0x590000df, 0x440000e0,
		0x450000e1, 0x420000e2, 0x460000e3, 0x430000e4, 0x470000e5, 0x9c0000e6, 0x480000e7, 0x540000e8,
		0x510000e9, 0x520000ea, 0x530000eb, 0x580000ec, 0x550000ed, 0x560000ee, 0x570000ef, 0x8c0000f0,
		0x490000f1, 0xcd0000f2, 0xce0000f3, 0xcb0000f4, 0xcf0000f5, 0xcc0000f6, 0xe10000f7, 0x700000f8,
		0xdd0000f9, 0xde0000fa, 0xdb0000fb, 0xdc0000fc, 0x8d0000fd, 0x8e0000fe, 0xdf0000ff, 0x9f0020ac,
	},
}

// ISO8859_1 is the ISO 8859-1 encoding.
var ISO8859_1 *Charmap = &iso8859_1

var iso8859_1 = Charmap{
	name:          "ISO 8859-1",
	mib:           identifier.ISOLatin1,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x80, 0x00}}, {2, [3]byte{0xc2, 0x81, 0x00}},
		{2, [3]byte{0xc2, 0x82, 0x00}}, {2, [3]byte{0xc2, 0x83, 0x00}},
		{2, [3]byte{0xc2, 0x84, 0x00}}, {2, [3]byte{0xc2, 0x85, 0x00}},
		{2, [3]byte{0xc2, 0x86, 0x00}}, {2, [3]byte{0xc2, 0x87, 0x00}},
		{2, [3]byte{0xc2, 0x88, 0x00}}, {2, [3]byte{0xc2, 0x89, 0x00}},
		{2, [3]byte{0xc2, 0x8a, 0x00}}, {2, [3]byte{0xc2, 0x8b, 0x00}},
		{2, [3]byte{0xc2, 0x8c, 0x00}}, {2, [3]byte{0xc2, 0x8d, 0x00}},
		{2, [3]byte{0xc2, 0x8e, 0x00}}, {2, [3]byte{0xc2, 0x8f, 0x00}},
		{2, [3]byte{0xc2, 0x90, 0x00}}, {2, [3]byte{0xc2, 0x91, 0x00}},
		{2, [3]byte{0xc2, 0x92, 0x00}}, {2, [3]byte{0xc2, 0x93, 0x00}},
		{2, [3]byte{0xc2, 0x94, 0x00}}, {2, [3]byte{0xc2, 0x95, 0x00}},
		{2, [3]byte{0xc2, 0x96, 0x00}}, {2, [3]byte{0xc2, 0x97, 0x00}},
		{2, [3]byte{0xc2, 0x98, 0x00}}, {2, [3]byte{0xc2, 0x99, 0x00}},
		{2, [3]byte{0xc2, 0x9a, 0x00}}, {2, [3]byte{0xc2, 0x9b, 0x00}},
		{2, [3]byte{0xc2, 0x9c, 0x00}}, {2, [3]byte{0xc2, 0x9d, 0x00}},
		{2, [3]byte{0xc2, 0x9e, 0x00}}, {2, [3]byte{0xc2, 0x9f, 0x00}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xc2, 0xa1, 0x00}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {2, [3]byte{0xc2, 0xa5, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc2, 0xaf, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc2, 0xb3, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc2, 0xb8, 0x00}}, {2, [3]byte{0xc2, 0xb9, 0x00}},
		{2, [3]byte{0xc2, 0xba, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbe, 0x00}}, {2, [3]byte{0xc2, 0xbf, 0x00}},
		{2, [3]byte{0xc3, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc3, 0x83, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc3, 0x87, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc3, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc3, 0x8f, 0x00}},
		{2, [3]byte{0xc3, 0x90, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc3, 0x92, 0x00}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{2, [3]byte{0xc3, 0x98, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0x9d, 0x00}},
		{2, [3]byte{0xc3, 0x9e, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc3, 0xa0, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc3, 0xa3, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa5, 0x00}},
		{2, [3]byte{0xc3, 0xa6, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xac, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xb0, 0x00}}, {2, [3]byte{0xc3, 0xb1, 0x00}},
		{2, [3]byte{0xc3, 0xb2, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xb4, 0x00}}, {2, [3]byte{0xc3, 0xb5, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{2, [3]byte{0xc3, 0xb8, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc3, 0xbd, 0x00}},
		{2, [3]byte{0xc3, 0xbe, 0x00}}, {2, [3]byte{0xc3, 0xbf, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0x80000080, 0x81000081, 0x82000082, 0x83000083, 0x84000084, 0x85000085, 0x86000086, 0x87000087,
		0x88000088, 0x89000089, 0x8a00008a, 0x8b00008b, 0x8c00008c, 0x8d00008d, 0x8e00008e, 0x8f00008f,
		0x90000090, 0x91000091, 0x92000092, 0x93000093, 0x94000094, 0x95000095, 0x96000096, 0x97000097,
		0x98000098, 0x99000099, 0x9a00009a, 0x9b00009b, 0x9c00009c, 0x9d00009d, 0x9e00009e, 0x9f00009f,
		0xa00000a0, 0xa10000a1, 0xa20000a2, 0xa30000a3, 0xa40000a4, 0xa50000a5, 0xa60000a6, 0xa70000a7,
		0xa80000a8, 0xa90000a9, 0xaa0000aa, 0xab0000ab, 0xac0000ac, 0xad0000ad, 0xae0000ae, 0xaf0000af,
		0xb00000b0, 0xb10000b1, 0xb20000b2, 0xb30000b3, 0xb40000b4, 0xb50000b5, 0xb60000b6, 0xb70000b7,
		0xb80000b8, 0xb90000b9, 0xba0000ba, 0xbb0000bb, 0xbc0000bc, 0xbd0000bd, 0xbe0000be, 0xbf0000bf,
		0xc00000c0, 0xc10000c1, 0xc20000c2, 0xc30000c3, 0xc40000c4, 0xc50000c5, 0xc60000c6, 0xc70000c7,
		0xc80000c8, 0xc90000c9, 0xca0000ca, 0xcb0000cb, 0xcc0000cc, 0xcd0000cd, 0xce0000ce, 0xcf0000cf,
		0xd00000d0, 0xd10000d1, 0xd20000d2, 0xd30000d3, 0xd40000d4, 0xd50000d5, 0xd60000d6, 0xd70000d7,
		0xd80000d8, 0xd90000d9, 0xda0000da, 0xdb0000db, 0xdc0000dc, 0xdd0000dd, 0xde0000de, 0xdf0000df,
		0xe00000e0, 0xe10000e1, 0xe20000e2, 0xe30000e3, 0xe40000e4, 0xe50000e5, 0xe60000e6, 0xe70000e7,
		0xe80000e8, 0xe90000e9, 0xea0000ea, 0xeb0000eb, 0xec0000ec, 0xed0000ed, 0xee0000ee, 0xef0000ef,
		0xf00000f0, 0xf10000f1, 0xf20000f2, 0xf30000f3, 0xf40000f4, 0xf50000f5, 0xf60000f6, 0xf70000f7,
		0xf80000f8, 0xf90000f9, 0xfa0000fa, 0xfb0000fb, 0xfc0000fc, 0xfd0000fd, 0xfe0000fe, 0xff0000ff,
	},
}

// ISO8859_2 is the ISO 8859-2 encoding.
var ISO8859_2 *Charmap = &iso8859_2

var iso8859_2 = Charmap{
	name:          "ISO 8859-2",
	mib:           identifier.ISOLatin2,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xc4, 0x84, 0x00}},
		{2, [3]byte{0xcb, 0x98, 0x00}}, {2, [3]byte{0xc5, 0x81, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {2, [3]byte{0xc4, 0xbd, 0x00}},
		{2, [3]byte{0xc5, 0x9a, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc5, 0xa0, 0x00}},
		{2, [3]byte{0xc5, 0x9e, 0x00}}, {2, [3]byte{0xc5, 0xa4, 0x00}},
		{2, [3]byte{0xc5, 0xb9, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc5, 0xbd, 0x00}}, {2, [3]byte{0xc5, 0xbb, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc4, 0x85, 0x00}},
		{2, [3]byte{0xcb, 0x9b, 0x00}}, {2, [3]byte{0xc5, 0x82, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc4, 0xbe, 0x00}},
		{2, [3]byte{0xc5, 0x9b, 0x00}}, {2, [3]byte{0xcb, 0x87, 0x00}},
		{2, [3]byte{0xc2, 0xb8, 0x00}}, {2, [3]byte{0xc5, 0xa1, 0x00}},
		{2, [3]byte{0xc5, 0x9f, 0x00}}, {2, [3]byte{0xc5, 0xa5, 0x00}},
		{2, [3]byte{0xc5, 0xba, 0x00}}, {2, [3]byte{0xcb, 0x9d, 0x00}},
		{2, [3]byte{0xc5, 0xbe, 0x00}}, {2, [3]byte{0xc5, 0xbc, 0x00}},
		{2, [3]byte{0xc5, 0x94, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc4, 0x82, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc4, 0xb9, 0x00}},
		{2, [3]byte{0xc4, 0x86, 0x00}}, {2, [3]byte{0xc3, 0x87, 0x00}},
		{2, [3]byte{0xc4, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc4, 0x98, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc4, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc4, 0x8e, 0x00}},
		{2, [3]byte{0xc4, 0x90, 0x00}}, {2, [3]byte{0xc5, 0x83, 0x00}},
		{2, [3]byte{0xc5, 0x87, 0x00}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc5, 0x90, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{2, [3]byte{0xc5, 0x98, 0x00}}, {2, [3]byte{0xc5, 0xae, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc5, 0xb0, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0x9d, 0x00}},
		{2, [3]byte{0xc5, 0xa2, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc5, 0x95, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc4, 0x83, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc4, 0xba, 0x00}},
		{2, [3]byte{0xc4, 0x87, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc4, 0x8d, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc4, 0x99, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc4, 0x9b, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc4, 0x8f, 0x00}},
		{2, [3]byte{0xc4, 0x91, 0x00}}, {2, [3]byte{0xc5, 0x84, 0x00}},
		{2, [3]byte{0xc5, 0x88, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xb4, 0x00}}, {2, [3]byte{0xc5, 0x91, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{2, [3]byte{0xc5, 0x99, 0x00}}, {2, [3]byte{0xc5, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc5, 0xb1, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc3, 0xbd, 0x00}},
		{2, [3]byte{0xc5, 0xa3, 0x00}}, {2, [3]byte{0xcb, 0x99, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa40000a4, 0xa70000a7, 0xa80000a8, 0xad0000ad, 0xb00000b0, 0xb40000b4, 0xb80000b8,
		0xc10000c1, 0xc20000c2, 0xc40000c4, 0xc70000c7, 0xc90000c9, 0xcb0000cb, 0xcd0000cd, 0xce0000ce,
		0xd30000d3, 0xd40000d4, 0xd60000d6, 0xd70000d7, 0xda0000da, 0xdc0000dc, 0xdd0000dd, 0xdf0000df,
		0xe10000e1, 0xe20000e2, 0xe40000e4, 0xe70000e7, 0xe90000e9, 0xeb0000eb, 0xed0000ed, 0xee0000ee,
		0xf30000f3, 0xf40000f4, 0xf60000f6, 0xf70000f7, 0xfa0000fa, 0xfc0000fc, 0xfd0000fd, 0xc3000102,
		0xe3000103, 0xa1000104, 0xb1000105, 0xc6000106, 0xe6000107, 0xc800010c, 0xe800010d, 0xcf00010e,
		0xef00010f, 0xd0000110, 0xf0000111, 0xca000118, 0xea000119, 0xcc00011a, 0xec00011b, 0xc5000139,
		0xe500013a, 0xa500013d, 0xb500013e, 0xa3000141, 0xb3000142, 0xd1000143, 0xf1000144, 0xd2000147,
		0xf2000148, 0xd5000150, 0xf5000151, 0xc0000154, 0xe0000155, 0xd8000158, 0xf8000159, 0xa600015a,
		0xb600015b, 0xaa00015e, 0xba00015f, 0xa9000160, 0xb9000161, 0xde000162, 0xfe000163, 0xab000164,
		0xbb000165, 0xd900016e, 0xf900016f, 0xdb000170, 0xfb000171, 0xac000179, 0xbc00017a, 0xaf00017b,
		0xbf00017c, 0xae00017d, 0xbe00017e, 0xb70002c7, 0xa20002d8, 0xff0002d9, 0xb20002db, 0xbd0002dd,
		0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd,
		0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd,
		0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd,
		0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd, 0xbd0002dd,
	},
}

// ISO8859_3 is the ISO 8859-3 encoding.
var ISO8859_3 *Charmap = &iso8859_3

var iso8859_3 = Charmap{
	name:          "ISO 8859-3",
	mib:           identifier.ISOLatin3,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xc4, 0xa6, 0x00}},
		{2, [3]byte{0xcb, 0x98, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc4, 0xa4, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc4, 0xb0, 0x00}},
		{2, [3]byte{0xc5, 0x9e, 0x00}}, {2, [3]byte{0xc4, 0x9e, 0x00}},
		{2, [3]byte{0xc4, 0xb4, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {2, [3]byte{0xc5, 0xbb, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc4, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc2, 0xb3, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc4, 0xa5, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc2, 0xb8, 0x00}}, {2, [3]byte{0xc4, 0xb1, 0x00}},
		{2, [3]byte{0xc5, 0x9f, 0x00}}, {2, [3]byte{0xc4, 0x9f, 0x00}},
		{2, [3]byte{0xc4, 0xb5, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {2, [3]byte{0xc5, 0xbc, 0x00}},
		{2, [3]byte{0xc3, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc4, 0x8a, 0x00}},
		{2, [3]byte{0xc4, 0x88, 0x00}}, {2, [3]byte{0xc3, 0x87, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc3, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc3, 0x8f, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc3, 0x92, 0x00}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc4, 0xa0, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{2, [3]byte{0xc4, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc5, 0xac, 0x00}},
		{2, [3]byte{0xc5, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc3, 0xa0, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc4, 0x8b, 0x00}},
		{2, [3]byte{0xc4, 0x89, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xac, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {2, [3]byte{0xc3, 0xb1, 0x00}},
		{2, [3]byte{0xc3, 0xb2, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xb4, 0x00}}, {2, [3]byte{0xc4, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{2, [3]byte{0xc4, 0x9d, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc5, 0xad, 0x00}},
		{2, [3]byte{0xc5, 0x9d, 0x00}}, {2, [3]byte{0xcb, 0x99, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa30000a3, 0xa40000a4, 0xa70000a7, 0xa80000a8, 0xad0000ad, 0xb00000b0, 0xb20000b2,
		0xb30000b3, 0xb40000b4, 0xb50000b5, 0xb70000b7, 0xb80000b8, 0xbd0000bd, 0xc00000c0, 0xc10000c1,
		0xc20000c2, 0xc40000c4, 0xc70000c7, 0xc80000c8, 0xc90000c9, 0xca0000ca, 0xcb0000cb, 0xcc0000cc,
		0xcd0000cd, 0xce0000ce, 0xcf0000cf, 0xd10000d1, 0xd20000d2, 0xd30000d3, 0xd40000d4, 0xd60000d6,
		0xd70000d7, 0xd90000d9, 0xda0000da, 0xdb0000db, 0xdc0000dc, 0xdf0000df, 0xe00000e0, 0xe10000e1,
		0xe20000e2, 0xe40000e4, 0xe70000e7, 0xe80000e8, 0xe90000e9, 0xea0000ea, 0xeb0000eb, 0xec0000ec,
		0xed0000ed, 0xee0000ee, 0xef0000ef, 0xf10000f1, 0xf20000f2, 0xf30000f3, 0xf40000f4, 0xf60000f6,
		0xf70000f7, 0xf90000f9, 0xfa0000fa, 0xfb0000fb, 0xfc0000fc, 0xc6000108, 0xe6000109, 0xc500010a,
		0xe500010b, 0xd800011c, 0xf800011d, 0xab00011e, 0xbb00011f, 0xd5000120, 0xf5000121, 0xa6000124,
		0xb6000125, 0xa1000126, 0xb1000127, 0xa9000130, 0xb9000131, 0xac000134, 0xbc000135, 0xde00015c,
		0xfe00015d, 0xaa00015e, 0xba00015f, 0xdd00016c, 0xfd00016d, 0xaf00017b, 0xbf00017c, 0xa20002d8,
		0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9,
		0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9,
		0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9,
		0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9,
		0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9, 0xff0002d9,
	},
}

// ISO8859_4 is the ISO 8859-4 encoding.
var ISO8859_4 *Charmap = &iso8859_4

var iso8859_4 = Charmap{
	name:          "ISO 8859-4",
	mib:           identifier.ISOLatin4,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xc4, 0x84, 0x00}},
		{2, [3]byte{0xc4, 0xb8, 0x00}}, {2, [3]byte{0xc5, 0x96, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {2, [3]byte{0xc4, 0xa8, 0x00}},
		{2, [3]byte{0xc4, 0xbb, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc5, 0xa0, 0x00}},
		{2, [3]byte{0xc4, 0x92, 0x00}}, {2, [3]byte{0xc4, 0xa2, 0x00}},
		{2, [3]byte{0xc5, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc5, 0xbd, 0x00}}, {2, [3]byte{0xc2, 0xaf, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc4, 0x85, 0x00}},
		{2, [3]byte{0xcb, 0x9b, 0x00}}, {2, [3]byte{0xc5, 0x97, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc4, 0xa9, 0x00}},
		{2, [3]byte{0xc4, 0xbc, 0x00}}, {2, [3]byte{0xcb, 0x87, 0x00}},
		{2, [3]byte{0xc2, 0xb8, 0x00}}, {2, [3]byte{0xc5, 0xa1, 0x00}},
		{2, [3]byte{0xc4, 0x93, 0x00}}, {2, [3]byte{0xc4, 0xa3, 0x00}},
		{2, [3]byte{0xc5, 0xa7, 0x00}}, {2, [3]byte{0xc5, 0x8a, 0x00}},
		{2, [3]byte{0xc5, 0xbe, 0x00}}, {2, [3]byte{0xc5, 0x8b, 0x00}},
		{2, [3]byte{0xc4, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc3, 0x83, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc4, 0xae, 0x00}},
		{2, [3]byte{0xc4, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc4, 0x98, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc4, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc4, 0xaa, 0x00}},
		{2, [3]byte{0xc4, 0x90, 0x00}}, {2, [3]byte{0xc5, 0x85, 0x00}},
		{2, [3]byte{0xc5, 0x8c, 0x00}}, {2, [3]byte{0xc4, 0xb6, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{2, [3]byte{0xc3, 0x98, 0x00}}, {2, [3]byte{0xc5, 0xb2, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc5, 0xa8, 0x00}},
		{2, [3]byte{0xc5, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc4, 0x81, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc3, 0xa3, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa5, 0x00}},
		{2, [3]byte{0xc3, 0xa6, 0x00}}, {2, [3]byte{0xc4, 0xaf, 0x00}},
		{2, [3]byte{0xc4, 0x8d, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc4, 0x99, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc4, 0x97, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc4, 0xab, 0x00}},
		{2, [3]byte{0xc4, 0x91, 0x00}}, {2, [3]byte{0xc5, 0x86, 0x00}},
		{2, [3]byte{0xc5, 0x8d, 0x00}}, {2, [3]byte{0xc4, 0xb7, 0x00}},
		{2, [3]byte{0xc3, 0xb4, 0x00}}, {2, [3]byte{0xc3, 0xb5, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{2, [3]byte{0xc3, 0xb8, 0x00}}, {2, [3]byte{0xc5, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc5, 0xa9, 0x00}},
		{2, [3]byte{0xc5, 0xab, 0x00}}, {2, [3]byte{0xcb, 0x99, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa40000a4, 0xa70000a7, 0xa80000a8, 0xad0000ad, 0xaf0000af, 0xb00000b0, 0xb40000b4,
		0xb80000b8, 0xc10000c1, 0xc20000c2, 0xc30000c3, 0xc40000c4, 0xc50000c5, 0xc60000c6, 0xc90000c9,
		0xcb0000cb, 0xcd0000cd, 0xce0000ce, 0xd40000d4, 0xd50000d5, 0xd60000d6, 0xd70000d7, 0xd80000d8,
		0xda0000da, 0xdb0000db, 0xdc0000dc, 0xdf0000df, 0xe10000e1, 0xe20000e2, 0xe30000e3, 0xe40000e4,
		0xe50000e5, 0xe60000e6, 0xe90000e9, 0xeb0000eb, 0xed0000ed, 0xee0000ee, 0xf40000f4, 0xf50000f5,
		0xf60000f6, 0xf70000f7, 0xf80000f8, 0xfa0000fa, 0xfb0000fb, 0xfc0000fc, 0xc0000100, 0xe0000101,
		0xa1000104, 0xb1000105, 0xc800010c, 0xe800010d, 0xd0000110, 0xf0000111, 0xaa000112, 0xba000113,
		0xcc000116, 0xec000117, 0xca000118, 0xea000119, 0xab000122, 0xbb000123, 0xa5000128, 0xb5000129,
		0xcf00012a, 0xef00012b, 0xc700012e, 0xe700012f, 0xd3000136, 0xf3000137, 0xa2000138, 0xa600013b,
		0xb600013c, 0xd1000145, 0xf1000146, 0xbd00014a, 0xbf00014b, 0xd200014c, 0xf200014d, 0xa3000156,
		0xb3000157, 0xa9000160, 0xb9000161, 0xac000166, 0xbc000167, 0xdd000168, 0xfd000169, 0xde00016a,
		0xfe00016b, 0xd9000172, 0xf9000173, 0xae00017d, 0xbe00017e, 0xb70002c7, 0xff0002d9, 0xb20002db,
		0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db,
		0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db,
		0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db,
		0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db, 0xb20002db,
	},
}

// ISO8859_5 is the ISO 8859-5 encoding.
var ISO8859_5 *Charmap = &iso8859_5

var iso8859_5 = Charmap{
	name:          "ISO 8859-5",
	mib:           identifier.ISOLatinCyrillic,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xd0, 0x81, 0x00}},
		{2, [3]byte{0xd0, 0x82, 0x00}}, {2, [3]byte{0xd0, 0x83, 0x00}},
		{2, [3]byte{0xd0, 0x84, 0x00}}, {2, [3]byte{0xd0, 0x85, 0x00}},
		{2, [3]byte{0xd0, 0x86, 0x00}}, {2, [3]byte{0xd0, 0x87, 0x00}},
		{2, [3]byte{0xd0, 0x88, 0x00}}, {2, [3]byte{0xd0, 0x89, 0x00}},
		{2, [3]byte{0xd0, 0x8a, 0x00}}, {2, [3]byte{0xd0, 0x8b, 0x00}},
		{2, [3]byte{0xd0, 0x8c, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xd0, 0x8e, 0x00}}, {2, [3]byte{0xd0, 0x8f, 0x00}},
		{2, [3]byte{0xd0, 0x90, 0x00}}, {2, [3]byte{0xd0, 0x91, 0x00}},
		{2, [3]byte{0xd0, 0x92, 0x00}}, {2, [3]byte{0xd0, 0x93, 0x00}},
		{2, [3]byte{0xd0, 0x94, 0x00}}, {2, [3]byte{0xd0, 0x95, 0x00}},
		{2, [3]byte{0xd0, 0x96, 0x00}}, {2, [3]byte{0xd0, 0x97, 0x00}},
		{2, [3]byte{0xd0, 0x98, 0x00}}, {2, [3]byte{0xd0, 0x99, 0x00}},
		{2, [3]byte{0xd0, 0x9a, 0x00}}, {2, [3]byte{0xd0, 0x9b, 0x00}},
		{2, [3]byte{0xd0, 0x9c, 0x00}}, {2, [3]byte{0xd0, 0x9d, 0x00}},
		{2, [3]byte{0xd0, 0x9e, 0x00}}, {2, [3]byte{0xd0, 0x9f, 0x00}},
		{2, [3]byte{0xd0, 0xa0, 0x00}}, {2, [3]byte{0xd0, 0xa1, 0x00}},
		{2, [3]byte{0xd0, 0xa2, 0x00}}, {2, [3]byte{0xd0, 0xa3, 0x00}},
		{2, [3]byte{0xd0, 0xa4, 0x00}}, {2, [3]byte{0xd0, 0xa5, 0x00}},
		{2, [3]byte{0xd0, 0xa6, 0x00}}, {2, [3]byte{0xd0, 0xa7, 0x00}},
		{2, [3]byte{0xd0, 0xa8, 0x00}}, {2, [3]byte{0xd0, 0xa9, 0x00}},
		{2, [3]byte{0xd0, 0xaa, 0x00}}, {2, [3]byte{0xd0, 0xab, 0x00}},
		{2, [3]byte{0xd0, 0xac, 0x00}}, {2, [3]byte{0xd0, 0xad, 0x00}},
		{2, [3]byte{0xd0, 0xae, 0x00}}, {2, [3]byte{0xd0, 0xaf, 0x00}},
		{2, [3]byte{0xd0, 0xb0, 0x00}}, {2, [3]byte{0xd0, 0xb1, 0x00}},
		{2, [3]byte{0xd0, 0xb2, 0x00}}, {2, [3]byte{0xd0, 0xb3, 0x00}},
		{2, [3]byte{0xd0, 0xb4, 0x00}}, {2, [3]byte{0xd0, 0xb5, 0x00}},
		{2, [3]byte{0xd0, 0xb6, 0x00}}, {2, [3]byte{0xd0, 0xb7, 0x00}},
		{2, [3]byte{0xd0, 0xb8, 0x00}}, {2, [3]byte{0xd0, 0xb9, 0x00}},
		{2, [3]byte{0xd0, 0xba, 0x00}}, {2, [3]byte{0xd0, 0xbb, 0x00}},
		{2, [3]byte{0xd0, 0xbc, 0x00}}, {2, [3]byte{0xd0, 0xbd, 0x00}},
		{2, [3]byte{0xd0, 0xbe, 0x00}}, {2, [3]byte{0xd0, 0xbf, 0x00}},
		{2, [3]byte{0xd1, 0x80, 0x00}}, {2, [3]byte{0xd1, 0x81, 0x00}},
		{2, [3]byte{0xd1, 0x82, 0x00}}, {2, [3]byte{0xd1, 0x83, 0x00}},
		{2, [3]byte{0xd1, 0x84, 0x00}}, {2, [3]byte{0xd1, 0x85, 0x00}},
		{2, [3]byte{0xd1, 0x86, 0x00}}, {2, [3]byte{0xd1, 0x87, 0x00}},
		{2, [3]byte{0xd1, 0x88, 0x00}}, {2, [3]byte{0xd1, 0x89, 0x00}},
		{2, [3]byte{0xd1, 0x8a, 0x00}}, {2, [3]byte{0xd1, 0x8b, 0x00}},
		{2, [3]byte{0xd1, 0x8c, 0x00}}, {2, [3]byte{0xd1, 0x8d, 0x00}},
		{2, [3]byte{0xd1, 0x8e, 0x00}}, {2, [3]byte{0xd1, 0x8f, 0x00}},
		{3, [3]byte{0xe2, 0x84, 0x96}}, {2, [3]byte{0xd1, 0x91, 0x00}},
		{2, [3]byte{0xd1, 0x92, 0x00}}, {2, [3]byte{0xd1, 0x93, 0x00}},
		{2, [3]byte{0xd1, 0x94, 0x00}}, {2, [3]byte{0xd1, 0x95, 0x00}},
		{2, [3]byte{0xd1, 0x96, 0x00}}, {2, [3]byte{0xd1, 0x97, 0x00}},
		{2, [3]byte{0xd1, 0x98, 0x00}}, {2, [3]byte{0xd1, 0x99, 0x00}},
		{2, [3]byte{0xd1, 0x9a, 0x00}}, {2, [3]byte{0xd1, 0x9b, 0x00}},
		{2, [3]byte{0xd1, 0x9c, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xd1, 0x9e, 0x00}}, {2, [3]byte{0xd1, 0x9f, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xfd0000a7, 0xad0000ad, 0xa1000401, 0xa2000402, 0xa3000403, 0xa4000404, 0xa5000405,
		0xa6000406, 0xa7000407, 0xa8000408, 0xa9000409, 0xaa00040a, 0xab00040b, 0xac00040c, 0xae00040e,
		0xaf00040f, 0xb0000410, 0xb1000411, 0xb2000412, 0xb3000413, 0xb4000414, 0xb5000415, 0xb6000416,
		0xb7000417, 0xb8000418, 0xb9000419, 0xba00041a, 0xbb00041b, 0xbc00041c, 0xbd00041d, 0xbe00041e,
		0xbf00041f, 0xc0000420, 0xc1000421, 0xc2000422, 0xc3000423, 0xc4000424, 0xc5000425, 0xc6000426,
		0xc7000427, 0xc8000428, 0xc9000429, 0xca00042a, 0xcb00042b, 0xcc00042c, 0xcd00042d, 0xce00042e,
		0xcf00042f, 0xd0000430, 0xd1000431, 0xd2000432, 0xd3000433, 0xd4000434, 0xd5000435, 0xd6000436,
		0xd7000437, 0xd8000438, 0xd9000439, 0xda00043a, 0xdb00043b, 0xdc00043c, 0xdd00043d, 0xde00043e,
		0xdf00043f, 0xe0000440, 0xe1000441, 0xe2000442, 0xe3000443, 0xe4000444, 0xe5000445, 0xe6000446,
		0xe7000447, 0xe8000448, 0xe9000449, 0xea00044a, 0xeb00044b, 0xec00044c, 0xed00044d, 0xee00044e,
		0xef00044f, 0xf1000451, 0xf2000452, 0xf3000453, 0xf4000454, 0xf5000455, 0xf6000456, 0xf7000457,
		0xf8000458, 0xf9000459, 0xfa00045a, 0xfb00045b, 0xfc00045c, 0xfe00045e, 0xff00045f, 0xf0002116,
		0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116,
		0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116,
		0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116,
		0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116, 0xf0002116,
	},
}

// ISO8859_6 is the ISO 8859-6 encoding.
var ISO8859_6 *Charmap = &iso8859_6

var iso8859_6 = Charmap{
	name:          "ISO 8859-6",
	mib:           identifier.ISOLatinArabic,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xd8, 0x8c, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {2, [3]byte{0xd8, 0x9b, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {2, [3]byte{0xd8, 0x9f, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {2, [3]byte{0xd8, 0xa1, 0x00}},
		{2, [3]byte{0xd8, 0xa2, 0x00}}, {2, [3]byte{0xd8, 0xa3, 0x00}},
		{2, [3]byte{0xd8, 0xa4, 0x00}}, {2, [3]byte{0xd8, 0xa5, 0x00}},
		{2, [3]byte{0xd8, 0xa6, 0x00}}, {2, [3]byte{0xd8, 0xa7, 0x00}},
		{2, [3]byte{0xd8, 0xa8, 0x00}}, {2, [3]byte{0xd8, 0xa9, 0x00}},
		{2, [3]byte{0xd8, 0xaa, 0x00}}, {2, [3]byte{0xd8, 0xab, 0x00}},
		{2, [3]byte{0xd8, 0xac, 0x00}}, {2, [3]byte{0xd8, 0xad, 0x00}},
		{2, [3]byte{0xd8, 0xae, 0x00}}, {2, [3]byte{0xd8, 0xaf, 0x00}},
		{2, [3]byte{0xd8, 0xb0, 0x00}}, {2, [3]byte{0xd8, 0xb1, 0x00}},
		{2, [3]byte{0xd8, 0xb2, 0x00}}, {2, [3]byte{0xd8, 0xb3, 0x00}},
		{2, [3]byte{0xd8, 0xb4, 0x00}}, {2, [3]byte{0xd8, 0xb5, 0x00}},
		{2, [3]byte{0xd8, 0xb6, 0x00}}, {2, [3]byte{0xd8, 0xb7, 0x00}},
		{2, [3]byte{0xd8, 0xb8, 0x00}}, {2, [3]byte{0xd8, 0xb9, 0x00}},
		{2, [3]byte{0xd8, 0xba, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xd9, 0x80, 0x00}}, {2, [3]byte{0xd9, 0x81, 0x00}},
		{2, [3]byte{0xd9, 0x82, 0x00}}, {2, [3]byte{0xd9, 0x83, 0x00}},
		{2, [3]byte{0xd9, 0x84, 0x00}}, {2, [3]byte{0xd9, 0x85, 0x00}},
		{2, [3]byte{0xd9, 0x86, 0x00}}, {2, [3]byte{0xd9, 0x87, 0x00}},
		{2, [3]byte{0xd9, 0x88, 0x00}}, {2, [3]byte{0xd9, 0x89, 0x00}},
		{2, [3]byte{0xd9, 0x8a, 0x00}}, {2, [3]byte{0xd9, 0x8b, 0x00}},
		{2, [3]byte{0xd9, 0x8c, 0x00}}, {2, [3]byte{0xd9, 0x8d, 0x00}},
		{2, [3]byte{0xd9, 0x8e, 0x00}}, {2, [3]byte{0xd9, 0x8f, 0x00}},
		{2, [3]byte{0xd9, 0x90, 0x00}}, {2, [3]byte{0xd9, 0x91, 0x00}},
		{2, [3]byte{0xd9, 0x92, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa40000a4, 0xad0000ad, 0xac00060c, 0xbb00061b, 0xbf00061f, 0xc1000621, 0xc2000622,
		0xc3000623, 0xc4000624, 0xc5000625, 0xc6000626, 0xc7000627, 0xc8000628, 0xc9000629, 0xca00062a,
		0xcb00062b, 0xcc00062c, 0xcd00062d, 0xce00062e, 0xcf00062f, 0xd0000630, 0xd1000631, 0xd2000632,
		0xd3000633, 0xd4000634, 0xd5000635, 0xd6000636, 0xd7000637, 0xd8000638, 0xd9000639, 0xda00063a,
		0xe0000640, 0xe1000641, 0xe2000642, 0xe3000643, 0xe4000644, 0xe5000645, 0xe6000646, 0xe7000647,
		0xe8000648, 0xe9000649, 0xea00064a, 0xeb00064b, 0xec00064c, 0xed00064d, 0xee00064e, 0xef00064f,
		0xf0000650, 0xf1000651, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652,
		0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652,
		0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652,
		0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652,
		0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652,
		0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652,
		0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652,
		0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652,
		0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652,
		0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652, 0xf2000652,
	},
}

// ISO8859_7 is the ISO 8859-7 encoding.
var ISO8859_7 *Charmap = &iso8859_7

var iso8859_7 = Charmap{
	name:          "ISO 8859-7",
	mib:           identifier.ISOLatinGreek,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {3, [3]byte{0xe2, 0x80, 0x98}},
		{3, [3]byte{0xe2, 0x80, 0x99}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xac}}, {3, [3]byte{0xe2, 0x82, 0xaf}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xcd, 0xba, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0x95}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc2, 0xb3, 0x00}},
		{2, [3]byte{0xce, 0x84, 0x00}}, {2, [3]byte{0xce, 0x85, 0x00}},
		{2, [3]byte{0xce, 0x86, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xce, 0x88, 0x00}}, {2, [3]byte{0xce, 0x89, 0x00}},
		{2, [3]byte{0xce, 0x8a, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xce, 0x8c, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xce, 0x8e, 0x00}}, {2, [3]byte{0xce, 0x8f, 0x00}},
		{2, [3]byte{0xce, 0x90, 0x00}}, {2, [3]byte{0xce, 0x91, 0x00}},
		{2, [3]byte{0xce, 0x92, 0x00}}, {2, [3]byte{0xce, 0x93, 0x00}},
		{2, [3]byte{0xce, 0x94, 0x00}}, {2, [3]byte{0xce, 0x95, 0x00}},
		{2, [3]byte{0xce, 0x96, 0x00}}, {2, [3]byte{0xce, 0x97, 0x00}},
		{2, [3]byte{0xce, 0x98, 0x00}}, {2, [3]byte{0xce, 0x99, 0x00}},
		{2, [3]byte{0xce, 0x9a, 0x00}}, {2, [3]byte{0xce, 0x9b, 0x00}},
		{2, [3]byte{0xce, 0x9c, 0x00}}, {2, [3]byte{0xce, 0x9d, 0x00}},
		{2, [3]byte{0xce, 0x9e, 0x00}}, {2, [3]byte{0xce, 0x9f, 0x00}},
		{2, [3]byte{0xce, 0xa0, 0x00}}, {2, [3]byte{0xce, 0xa1, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {2, [3]byte{0xce, 0xa3, 0x00}},
		{2, [3]byte{0xce, 0xa4, 0x00}}, {2, [3]byte{0xce, 0xa5, 0x00}},
		{2, [3]byte{0xce, 0xa6, 0x00}}, {2, [3]byte{0xce, 0xa7, 0x00}},
		{2, [3]byte{0xce, 0xa8, 0x00}}, {2, [3]byte{0xce, 0xa9, 0x00}},
		{2, [3]byte{0xce, 0xaa, 0x00}}, {2, [3]byte{0xce, 0xab, 0x00}},
		{2, [3]byte{0xce, 0xac, 0x00}}, {2, [3]byte{0xce, 0xad, 0x00}},
		{2, [3]byte{0xce, 0xae, 0x00}}, {2, [3]byte{0xce, 0xaf, 0x00}},
		{2, [3]byte{0xce, 0xb0, 0x00}}, {2, [3]byte{0xce, 0xb1, 0x00}},
		{2, [3]byte{0xce, 0xb2, 0x00}}, {2, [3]byte{0xce, 0xb3, 0x00}},
		{2, [3]byte{0xce, 0xb4, 0x00}}, {2, [3]byte{0xce, 0xb5, 0x00}},
		{2, [3]byte{0xce, 0xb6, 0x00}}, {2, [3]byte{0xce, 0xb7, 0x00}},
		{2, [3]byte{0xce, 0xb8, 0x00}}, {2, [3]byte{0xce, 0xb9, 0x00}},
		{2, [3]byte{0xce, 0xba, 0x00}}, {2, [3]byte{0xce, 0xbb, 0x00}},
		{2, [3]byte{0xce, 0xbc, 0x00}}, {2, [3]byte{0xce, 0xbd, 0x00}},
		{2, [3]byte{0xce, 0xbe, 0x00}}, {2, [3]byte{0xce, 0xbf, 0x00}},
		{2, [3]byte{0xcf, 0x80, 0x00}}, {2, [3]byte{0xcf, 0x81, 0x00}},
		{2, [3]byte{0xcf, 0x82, 0x00}}, {2, [3]byte{0xcf, 0x83, 0x00}},
		{2, [3]byte{0xcf, 0x84, 0x00}}, {2, [3]byte{0xcf, 0x85, 0x00}},
		{2, [3]byte{0xcf, 0x86, 0x00}}, {2, [3]byte{0xcf, 0x87, 0x00}},
		{2, [3]byte{0xcf, 0x88, 0x00}}, {2, [3]byte{0xcf, 0x89, 0x00}},
		{2, [3]byte{0xcf, 0x8a, 0x00}}, {2, [3]byte{0xcf, 0x8b, 0x00}},
		{2, [3]byte{0xcf, 0x8c, 0x00}}, {2, [3]byte{0xcf, 0x8d, 0x00}},
		{2, [3]byte{0xcf, 0x8e, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa30000a3, 0xa60000a6, 0xa70000a7, 0xa80000a8, 0xa90000a9, 0xab0000ab, 0xac0000ac,
		0xad0000ad, 0xb00000b0, 0xb10000b1, 0xb20000b2, 0xb30000b3, 0xb70000b7, 0xbb0000bb, 0xbd0000bd,
		0xaa00037a, 0xb4000384, 0xb5000385, 0xb6000386, 0xb8000388, 0xb9000389, 0xba00038a, 0xbc00038c,
		0xbe00038e, 0xbf00038f, 0xc0000390, 0xc1000391, 0xc2000392, 0xc3000393, 0xc4000394, 0xc5000395,
		0xc6000396, 0xc7000397, 0xc8000398, 0xc9000399, 0xca00039a, 0xcb00039b, 0xcc00039c, 0xcd00039d,
		0xce00039e, 0xcf00039f, 0xd00003a0, 0xd10003a1, 0xd30003a3, 0xd40003a4, 0xd50003a5, 0xd60003a6,
		0xd70003a7, 0xd80003a8, 0xd90003a9, 0xda0003aa, 0xdb0003ab, 0xdc0003ac, 0xdd0003ad, 0xde0003ae,
		0xdf0003af, 0xe00003b0, 0xe10003b1, 0xe20003b2, 0xe30003b3, 0xe40003b4, 0xe50003b5, 0xe60003b6,
		0xe70003b7, 0xe80003b8, 0xe90003b9, 0xea0003ba, 0xeb0003bb, 0xec0003bc, 0xed0003bd, 0xee0003be,
		0xef0003bf, 0xf00003c0, 0xf10003c1, 0xf20003c2, 0xf30003c3, 0xf40003c4, 0xf50003c5, 0xf60003c6,
		0xf70003c7, 0xf80003c8, 0xf90003c9, 0xfa0003ca, 0xfb0003cb, 0xfc0003cc, 0xfd0003cd, 0xfe0003ce,
		0xaf002015, 0xa1002018, 0xa2002019, 0xa40020ac, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af,
		0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af,
		0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af,
		0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af,
		0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af, 0xa50020af,
	},
}

// ISO8859_8 is the ISO 8859-8 encoding.
var ISO8859_8 *Charmap = &iso8859_8

var iso8859_8 = Charmap{
	name:          "ISO 8859-8",
	mib:           identifier.ISOLatinHebrew,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {2, [3]byte{0xc2, 0xa5, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0x97, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc2, 0xaf, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc2, 0xb3, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc2, 0xb8, 0x00}}, {2, [3]byte{0xc2, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xb7, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbe, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0x97}},
		{2, [3]byte{0xd7, 0x90, 0x00}}, {2, [3]byte{0xd7, 0x91, 0x00}},
		{2, [3]byte{0xd7, 0x92, 0x00}}, {2, [3]byte{0xd7, 0x93, 0x00}},
		{2, [3]byte{0xd7, 0x94, 0x00}}, {2, [3]byte{0xd7, 0x95, 0x00}},
		{2, [3]byte{0xd7, 0x96, 0x00}}, {2, [3]byte{0xd7, 0x97, 0x00}},
		{2, [3]byte{0xd7, 0x98, 0x00}}, {2, [3]byte{0xd7, 0x99, 0x00}},
		{2, [3]byte{0xd7, 0x9a, 0x00}}, {2, [3]byte{0xd7, 0x9b, 0x00}},
		{2, [3]byte{0xd7, 0x9c, 0x00}}, {2, [3]byte{0xd7, 0x9d, 0x00}},
		{2, [3]byte{0xd7, 0x9e, 0x00}}, {2, [3]byte{0xd7, 0x9f, 0x00}},
		{2, [3]byte{0xd7, 0xa0, 0x00}}, {2, [3]byte{0xd7, 0xa1, 0x00}},
		{2, [3]byte{0xd7, 0xa2, 0x00}}, {2, [3]byte{0xd7, 0xa3, 0x00}},
		{2, [3]byte{0xd7, 0xa4, 0x00}}, {2, [3]byte{0xd7, 0xa5, 0x00}},
		{2, [3]byte{0xd7, 0xa6, 0x00}}, {2, [3]byte{0xd7, 0xa7, 0x00}},
		{2, [3]byte{0xd7, 0xa8, 0x00}}, {2, [3]byte{0xd7, 0xa9, 0x00}},
		{2, [3]byte{0xd7, 0xaa, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0x8e}},
		{3, [3]byte{0xe2, 0x80, 0x8f}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa20000a2, 0xa30000a3, 0xa40000a4, 0xa50000a5, 0xa60000a6, 0xa70000a7, 0xa80000a8,
		0xa90000a9, 0xab0000ab, 0xac0000ac, 0xad0000ad, 0xae0000ae, 0xaf0000af, 0xb00000b0, 0xb10000b1,
		0xb20000b2, 0xb30000b3, 0xb40000b4, 0xb50000b5, 0xb60000b6, 0xb70000b7, 0xb80000b8, 0xb90000b9,
		0xbb0000bb, 0xbc0000bc, 0xbd0000bd, 0xbe0000be, 0xaa0000d7, 0xba0000f7, 0xe00005d0, 0xe10005d1,
		0xe20005d2, 0xe30005d3, 0xe40005d4, 0xe50005d5, 0xe60005d6, 0xe70005d7, 0xe80005d8, 0xe90005d9,
		0xea0005da, 0xeb0005db, 0xec0005dc, 0xed0005dd, 0xee0005de, 0xef0005df, 0xf00005e0, 0xf10005e1,
		0xf20005e2, 0xf30005e3, 0xf40005e4, 0xf50005e5, 0xf60005e6, 0xf70005e7, 0xf80005e8, 0xf90005e9,
		0xfa0005ea, 0xfd00200e, 0xfe00200f, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017,
		0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017,
		0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017,
		0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017,
		0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017,
		0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017,
		0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017,
		0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017,
		0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017, 0xdf002017,
	},
}

// ISO8859_9 is the ISO 8859-9 encoding.
var ISO8859_9 *Charmap = &iso8859_9

var iso8859_9 = Charmap{
	name:          "ISO 8859-9",
	mib:           identifier.ISOLatin5,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xc2, 0x80, 0x00}}, {2, [3]byte{0xc2, 0x81, 0x00}},
		{2, [3]byte{0xc2, 0x82, 0x00}}, {2, [3]byte{0xc2, 0x83, 0x00}},
		{2, [3]byte{0xc2, 0x84, 0x00}}, {2, [3]byte{0xc2, 0x85, 0x00}},
		{2, [3]byte{0xc2, 0x86, 0x00}}, {2, [3]byte{0xc2, 0x87, 0x00}},
		{2, [3]byte{0xc2, 0x88, 0x00}}, {2, [3]byte{0xc2, 0x89, 0x00}},
		{2, [3]byte{0xc2, 0x8a, 0x00}}, {2, [3]byte{0xc2, 0x8b, 0x00}},
		{2, [3]byte{0xc2, 0x8c, 0x00}}, {2, [3]byte{0xc2, 0x8d, 0x00}},
		{2, [3]byte{0xc2, 0x8e, 0x00}}, {2, [3]byte{0xc2, 0x8f, 0x00}},
		{2, [3]byte{0xc2, 0x90, 0x00}}, {2, [3]byte{0xc2, 0x91, 0x00}},
		{2, [3]byte{0xc2, 0x92, 0x00}}, {2, [3]byte{0xc2, 0x93, 0x00}},
		{2, [3]byte{0xc2, 0x94, 0x00}}, {2, [3]byte{0xc2, 0x95, 0x00}},
		{2, [3]byte{0xc2, 0x96, 0x00}}, {2, [3]byte{0xc2, 0x97, 0x00}},
		{2, [3]byte{0xc2, 0x98, 0x00}}, {2, [3]byte{0xc2, 0x99, 0x00}},
		{2, [3]byte{0xc2, 0x9a, 0x00}}, {2, [3]byte{0xc2, 0x9b, 0x00}},
		{2, [3]byte{0xc2, 0x9c, 0x00}}, {2, [3]byte{0xc2, 0x9d, 0x00}},
		{2, [3]byte{0xc2, 0x9e, 0x00}}, {2, [3]byte{0xc2, 0x9f, 0x00}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xc2, 0xa1, 0x00}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {2, [3]byte{0xc2, 0xa5, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc2, 0xaf, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc2, 0xb3, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc2, 0xb8, 0x00}}, {2, [3]byte{0xc2, 0xb9, 0x00}},
		{2, [3]byte{0xc2, 0xba, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbe, 0x00}}, {2, [3]byte{0xc2, 0xbf, 0x00}},
		{2, [3]byte{0xc3, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc3, 0x83, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc3, 0x87, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc3, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc3, 0x8f, 0x00}},
		{2, [3]byte{0xc4, 0x9e, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc3, 0x92, 0x00}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{2, [3]byte{0xc3, 0x98, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc4, 0xb0, 0x00}},
		{2, [3]byte{0xc5, 0x9e, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc3, 0xa0, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc3, 0xa3, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa5, 0x00}},
		{2, [3]byte{0xc3, 0xa6, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xac, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc4, 0x9f, 0x00}}, {2, [3]byte{0xc3, 0xb1, 0x00}},
		{2, [3]byte{0xc3, 0xb2, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xb4, 0x00}}, {2, [3]byte{0xc3, 0xb5, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{2, [3]byte{0xc3, 0xb8, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc4, 0xb1, 0x00}},
		{2, [3]byte{0xc5, 0x9f, 0x00}}, {2, [3]byte{0xc3, 0xbf, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0x80000080, 0x81000081, 0x82000082, 0x83000083, 0x84000084, 0x85000085, 0x86000086, 0x87000087,
		0x88000088, 0x89000089, 0x8a00008a, 0x8b00008b, 0x8c00008c, 0x8d00008d, 0x8e00008e, 0x8f00008f,
		0x90000090, 0x91000091, 0x92000092, 0x93000093, 0x94000094, 0x95000095, 0x96000096, 0x97000097,
		0x98000098, 0x99000099, 0x9a00009a, 0x9b00009b, 0x9c00009c, 0x9d00009d, 0x9e00009e, 0x9f00009f,
		0xa00000a0, 0xa10000a1, 0xa20000a2, 0xa30000a3, 0xa40000a4, 0xa50000a5, 0xa60000a6, 0xa70000a7,
		0xa80000a8, 0xa90000a9, 0xaa0000aa, 0xab0000ab, 0xac0000ac, 0xad0000ad, 0xae0000ae, 0xaf0000af,
		0xb00000b0, 0xb10000b1, 0xb20000b2, 0xb30000b3, 0xb40000b4, 0xb50000b5, 0xb60000b6, 0xb70000b7,
		0xb80000b8, 0xb90000b9, 0xba0000ba, 0xbb0000bb, 0xbc0000bc, 0xbd0000bd, 0xbe0000be, 0xbf0000bf,
		0xc00000c0, 0xc10000c1, 0xc20000c2, 0xc30000c3, 0xc40000c4, 0xc50000c5, 0xc60000c6, 0xc70000c7,
		0xc80000c8, 0xc90000c9, 0xca0000ca, 0xcb0000cb, 0xcc0000cc, 0xcd0000cd, 0xce0000ce, 0xcf0000cf,
		0xd10000d1, 0xd20000d2, 0xd30000d3, 0xd40000d4, 0xd50000d5, 0xd60000d6, 0xd70000d7, 0xd80000d8,
		0xd90000d9, 0xda0000da, 0xdb0000db, 0xdc0000dc, 0xdf0000df, 0xe00000e0, 0xe10000e1, 0xe20000e2,
		0xe30000e3, 0xe40000e4, 0xe50000e5, 0xe60000e6, 0xe70000e7, 0xe80000e8, 0xe90000e9, 0xea0000ea,
		0xeb0000eb, 0xec0000ec, 0xed0000ed, 0xee0000ee, 0xef0000ef, 0xf10000f1, 0xf20000f2, 0xf30000f3,
		0xf40000f4, 0xf50000f5, 0xf60000f6, 0xf70000f7, 0xf80000f8, 0xf90000f9, 0xfa0000fa, 0xfb0000fb,
		0xfc0000fc, 0xff0000ff, 0xd000011e, 0xf000011f, 0xdd000130, 0xfd000131, 0xde00015e, 0xfe00015f,
	},
}

// ISO8859_10 is the ISO 8859-10 encoding.
var ISO8859_10 *Charmap = &iso8859_10

var iso8859_10 = Charmap{
	name:          "ISO 8859-10",
	mib:           identifier.ISOLatin6,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xc4, 0x84, 0x00}},
		{2, [3]byte{0xc4, 0x92, 0x00}}, {2, [3]byte{0xc4, 0xa2, 0x00}},
		{2, [3]byte{0xc4, 0xaa, 0x00}}, {2, [3]byte{0xc4, 0xa8, 0x00}},
		{2, [3]byte{0xc4, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc4, 0xbb, 0x00}}, {2, [3]byte{0xc4, 0x90, 0x00}},
		{2, [3]byte{0xc5, 0xa0, 0x00}}, {2, [3]byte{0xc5, 0xa6, 0x00}},
		{2, [3]byte{0xc5, 0xbd, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc5, 0xaa, 0x00}}, {2, [3]byte{0xc5, 0x8a, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc4, 0x85, 0x00}},
		{2, [3]byte{0xc4, 0x93, 0x00}}, {2, [3]byte{0xc4, 0xa3, 0x00}},
		{2, [3]byte{0xc4, 0xab, 0x00}}, {2, [3]byte{0xc4, 0xa9, 0x00}},
		{2, [3]byte{0xc4, 0xb7, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc4, 0xbc, 0x00}}, {2, [3]byte{0xc4, 0x91, 0x00}},
		{2, [3]byte{0xc5, 0xa1, 0x00}}, {2, [3]byte{0xc5, 0xa7, 0x00}},
		{2, [3]byte{0xc5, 0xbe, 0x00}}, {3, [3]byte{0xe2, 0x80, 0x95}},
		{2, [3]byte{0xc5, 0xab, 0x00}}, {2, [3]byte{0xc5, 0x8b, 0x00}},
		{2, [3]byte{0xc4, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc3, 0x83, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc4, 0xae, 0x00}},
		{2, [3]byte{0xc4, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc4, 0x98, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc4, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc3, 0x8f, 0x00}},
		{2, [3]byte{0xc3, 0x90, 0x00}}, {2, [3]byte{0xc5, 0x85, 0x00}},
		{2, [3]byte{0xc5, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc5, 0xa8, 0x00}},
		{2, [3]byte{0xc3, 0x98, 0x00}}, {2, [3]byte{0xc5, 0xb2, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0x9d, 0x00}},
		{2, [3]byte{0xc3, 0x9e, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc4, 0x81, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc3, 0xa3, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa5, 0x00}},
		{2, [3]byte{0xc3, 0xa6, 0x00}}, {2, [3]byte{0xc4, 0xaf, 0x00}},
		{2, [3]byte{0xc4, 0x8d, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc4, 0x99, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc4, 0x97, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xb0, 0x00}}, {2, [3]byte{0xc5, 0x86, 0x00}},
		{2, [3]byte{0xc5, 0x8d, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xb4, 0x00}}, {2, [3]byte{0xc3, 0xb5, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc5, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xb8, 0x00}}, {2, [3]byte{0xc5, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc3, 0xbd, 0x00}},
		{2, [3]byte{0xc3, 0xbe, 0x00}}, {2, [3]byte{0xc4, 0xb8, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa70000a7, 0xad0000ad, 0xb00000b0, 0xb70000b7, 0xc10000c1, 0xc20000c2, 0xc30000c3,
		0xc40000c4, 0xc50000c5, 0xc60000c6, 0xc90000c9, 0xcb0000cb, 0xcd0000cd, 0xce0000ce, 0xcf0000cf,
		0xd00000d0, 0xd30000d3, 0xd40000d4, 0xd50000d5, 0xd60000d6, 0xd80000d8, 0xda0000da, 0xdb0000db,
		0xdc0000dc, 0xdd0000dd, 0xde0000de, 0xdf0000df, 0xe10000e1, 0xe20000e2, 0xe30000e3, 0xe40000e4,
		0xe50000e5, 0xe60000e6, 0xe90000e9, 0xeb0000eb, 0xed0000ed, 0xee0000ee, 0xef0000ef, 0xf00000f0,
		0xf30000f3, 0xf40000f4, 0xf50000f5, 0xf60000f6, 0xf80000f8, 0xfa0000fa, 0xfb0000fb, 0xfc0000fc,
		0xfd0000fd, 0xfe0000fe, 0xc0000100, 0xe0000101, 0xa1000104, 0xb1000105, 0xc800010c, 0xe800010d,
		0xa9000110, 0xb9000111, 0xa2000112, 0xb2000113, 0xcc000116, 0xec000117, 0xca000118, 0xea000119,
		0xa3000122, 0xb3000123, 0xa5000128, 0xb5000129, 0xa400012a, 0xb400012b, 0xc700012e, 0xe700012f,
		0xa6000136, 0xb6000137, 0xff000138, 0xa800013b, 0xb800013c, 0xd1000145, 0xf1000146, 0xaf00014a,
		0xbf00014b, 0xd200014c, 0xf200014d, 0xaa000160, 0xba000161, 0xab000166, 0xbb000167, 0xd7000168,
		0xf7000169, 0xae00016a, 0xbe00016b, 0xd9000172, 0xf9000173, 0xac00017d, 0xbc00017e, 0xbd002015,
		0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015,
		0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015,
		0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015,
		0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015, 0xbd002015,
	},
}

// ISO8859_13 is the ISO 8859-13 encoding.
var ISO8859_13 *Charmap = &iso8859_13

var iso8859_13 = Charmap{
	name:          "ISO 8859-13",
	mib:           identifier.ISO885913,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {3, [3]byte{0xe2, 0x80, 0x9d}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {3, [3]byte{0xe2, 0x80, 0x9e}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0x98, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xc5, 0x96, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc3, 0x86, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc2, 0xb3, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x9c}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc3, 0xb8, 0x00}}, {2, [3]byte{0xc2, 0xb9, 0x00}},
		{2, [3]byte{0xc5, 0x97, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbe, 0x00}}, {2, [3]byte{0xc3, 0xa6, 0x00}},
		{2, [3]byte{0xc4, 0x84, 0x00}}, {2, [3]byte{0xc4, 0xae, 0x00}},
		{2, [3]byte{0xc4, 0x80, 0x00}}, {2, [3]byte{0xc4, 0x86, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc4, 0x98, 0x00}}, {2, [3]byte{0xc4, 0x92, 0x00}},
		{2, [3]byte{0xc4, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc5, 0xb9, 0x00}}, {2, [3]byte{0xc4, 0x96, 0x00}},
		{2, [3]byte{0xc4, 0xa2, 0x00}}, {2, [3]byte{0xc4, 0xb6, 0x00}},
		{2, [3]byte{0xc4, 0xaa, 0x00}}, {2, [3]byte{0xc4, 0xbb, 0x00}},
		{2, [3]byte{0xc5, 0xa0, 0x00}}, {2, [3]byte{0xc5, 0x83, 0x00}},
		{2, [3]byte{0xc5, 0x85, 0x00}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc5, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{2, [3]byte{0xc5, 0xb2, 0x00}}, {2, [3]byte{0xc5, 0x81, 0x00}},
		{2, [3]byte{0xc5, 0x9a, 0x00}}, {2, [3]byte{0xc5, 0xaa, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc5, 0xbb, 0x00}},
		{2, [3]byte{0xc5, 0xbd, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc4, 0x85, 0x00}}, {2, [3]byte{0xc4, 0xaf, 0x00}},
		{2, [3]byte{0xc4, 0x81, 0x00}}, {2, [3]byte{0xc4, 0x87, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa5, 0x00}},
		{2, [3]byte{0xc4, 0x99, 0x00}}, {2, [3]byte{0xc4, 0x93, 0x00}},
		{2, [3]byte{0xc4, 0x8d, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc5, 0xba, 0x00}}, {2, [3]byte{0xc4, 0x97, 0x00}},
		{2, [3]byte{0xc4, 0xa3, 0x00}}, {2, [3]byte{0xc4, 0xb7, 0x00}},
		{2, [3]byte{0xc4, 0xab, 0x00}}, {2, [3]byte{0xc4, 0xbc, 0x00}},
		{2, [3]byte{0xc5, 0xa1, 0x00}}, {2, [3]byte{0xc5, 0x84, 0x00}},
		{2, [3]byte{0xc5, 0x86, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc5, 0x8d, 0x00}}, {2, [3]byte{0xc3, 0xb5, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{2, [3]byte{0xc5, 0xb3, 0x00}}, {2, [3]byte{0xc5, 0x82, 0x00}},
		{2, [3]byte{0xc5, 0x9b, 0x00}}, {2, [3]byte{0xc5, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc5, 0xbc, 0x00}},
		{2, [3]byte{0xc5, 0xbe, 0x00}}, {3, [3]byte{0xe2, 0x80, 0x99}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa20000a2, 0xa30000a3, 0xa40000a4, 0xa60000a6, 0xa70000a7, 0xa90000a9, 0xab0000ab,
		0xac0000ac, 0xad0000ad, 0xae0000ae, 0xb00000b0, 0xb10000b1, 0xb20000b2, 0xb30000b3, 0xb50000b5,
		0xb60000b6, 0xb70000b7, 0xb90000b9, 0xbb0000bb, 0xbc0000bc, 0xbd0000bd, 0xbe0000be, 0xc40000c4,
		0xc50000c5, 0xaf0000c6, 0xc90000c9, 0xd30000d3, 0xd50000d5, 0xd60000d6, 0xd70000d7, 0xa80000d8,
		0xdc0000dc, 0xdf0000df, 0xe40000e4, 0xe50000e5, 0xbf0000e6, 0xe90000e9, 0xf30000f3, 0xf50000f5,
		0xf60000f6, 0xf70000f7, 0xb80000f8, 0xfc0000fc, 0xc2000100, 0xe2000101, 0xc0000104, 0xe0000105,
		0xc3000106, 0xe3000107, 0xc800010c, 0xe800010d, 0xc7000112, 0xe7000113, 0xcb000116, 0xeb000117,
		0xc6000118, 0xe6000119, 0xcc000122, 0xec000123, 0xce00012a, 0xee00012b, 0xc100012e, 0xe100012f,
		0xcd000136, 0xed000137, 0xcf00013b, 0xef00013c, 0xd9000141, 0xf9000142, 0xd1000143, 0xf1000144,
		0xd2000145, 0xf2000146, 0xd400014c, 0xf400014d, 0xaa000156, 0xba000157, 0xda00015a, 0xfa00015b,
		0xd0000160, 0xf0000161, 0xdb00016a, 0xfb00016b, 0xd8000172, 0xf8000173, 0xca000179, 0xea00017a,
		0xdd00017b, 0xfd00017c, 0xde00017d, 0xfe00017e, 0xff002019, 0xb400201c, 0xa100201d, 0xa500201e,
		0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e,
		0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e,
		0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e,
		0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e, 0xa500201e,
	},
}

// ISO8859_14 is the ISO 8859-14 encoding.
var ISO8859_14 *Charmap = &iso8859_14

var iso8859_14 = Charmap{
	name:          "ISO 8859-14",
	mib:           identifier.ISO885914,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {3, [3]byte{0xe1, 0xb8, 0x82}},
		{3, [3]byte{0xe1, 0xb8, 0x83}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc4, 0x8a, 0x00}}, {2, [3]byte{0xc4, 0x8b, 0x00}},
		{3, [3]byte{0xe1, 0xb8, 0x8a}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{3, [3]byte{0xe1, 0xba, 0x80}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{3, [3]byte{0xe1, 0xba, 0x82}}, {3, [3]byte{0xe1, 0xb8, 0x8b}},
		{3, [3]byte{0xe1, 0xbb, 0xb2}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc5, 0xb8, 0x00}},
		{3, [3]byte{0xe1, 0xb8, 0x9e}}, {3, [3]byte{0xe1, 0xb8, 0x9f}},
		{2, [3]byte{0xc4, 0xa0, 0x00}}, {2, [3]byte{0xc4, 0xa1, 0x00}},
		{3, [3]byte{0xe1, 0xb9, 0x80}}, {3, [3]byte{0xe1, 0xb9, 0x81}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {3, [3]byte{0xe1, 0xb9, 0x96}},
		{3, [3]byte{0xe1, 0xba, 0x81}}, {3, [3]byte{0xe1, 0xb9, 0x97}},
		{3, [3]byte{0xe1, 0xba, 0x83}}, {3, [3]byte{0xe1, 0xb9, 0xa0}},
		{3, [3]byte{0xe1, 0xbb, 0xb3}}, {3, [3]byte{0xe1, 0xba, 0x84}},
		{3, [3]byte{0xe1, 0xba, 0x85}}, {3, [3]byte{0xe1, 0xb9, 0xa1}},
		{2, [3]byte{0xc3, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc3, 0x83, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc3, 0x87, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc3, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc3, 0x8f, 0x00}},
		{2, [3]byte{0xc5, 0xb4, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc3, 0x92, 0x00}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {3, [3]byte{0xe1, 0xb9, 0xaa}},
		{2, [3]byte{0xc3, 0x98, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0x9d, 0x00}},
		{2, [3]byte{0xc5, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc3, 0xa0, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc3, 0xa3, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa5, 0x00}},
		{2, [3]byte{0xc3, 0xa6, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xac, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc5, 0xb5, 0x00}}, {2, [3]byte{0xc3, 0xb1, 0x00}},
		{2, [3]byte{0xc3, 0xb2, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xb4, 0x00}}, {2, [3]byte{0xc3, 0xb5, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {3, [3]byte{0xe1, 0xb9, 0xab}},
		{2, [3]byte{0xc3, 0xb8, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc3, 0xbd, 0x00}},
		{2, [3]byte{0xc5, 0xb7, 0x00}}, {2, [3]byte{0xc3, 0xbf, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa30000a3, 0xa70000a7, 0xa90000a9, 0xad0000ad, 0xae0000ae, 0xb60000b6, 0xc00000c0,
		0xc10000c1, 0xc20000c2, 0xc30000c3, 0xc40000c4, 0xc50000c5, 0xc60000c6, 0xc70000c7, 0xc80000c8,
		0xc90000c9, 0xca0000ca, 0xcb0000cb, 0xcc0000cc, 0xcd0000cd, 0xce0000ce, 0xcf0000cf, 0xd10000d1,
		0xd20000d2, 0xd30000d3, 0xd40000d4, 0xd50000d5, 0xd60000d6, 0xd80000d8, 0xd90000d9, 0xda0000da,
		0xdb0000db, 0xdc0000dc, 0xdd0000dd, 0xdf0000df, 0xe00000e0, 0xe10000e1, 0xe20000e2, 0xe30000e3,
		0xe40000e4, 0xe50000e5, 0xe60000e6, 0xe70000e7, 0xe80000e8, 0xe90000e9, 0xea0000ea, 0xeb0000eb,
		0xec0000ec, 0xed0000ed, 0xee0000ee, 0xef0000ef, 0xf10000f1, 0xf20000f2, 0xf30000f3, 0xf40000f4,
		0xf50000f5, 0xf60000f6, 0xf80000f8, 0xf90000f9, 0xfa0000fa, 0xfb0000fb, 0xfc0000fc, 0xfd0000fd,
		0xff0000ff, 0xa400010a, 0xa500010b, 0xb2000120, 0xb3000121, 0xd0000174, 0xf0000175, 0xde000176,
		0xfe000177, 0xaf000178, 0xa1001e02, 0xa2001e03, 0xa6001e0a, 0xab001e0b, 0xb0001e1e, 0xb1001e1f,
		0xb4001e40, 0xb5001e41, 0xb7001e56, 0xb9001e57, 0xbb001e60, 0xbf001e61, 0xd7001e6a, 0xf7001e6b,
		0xa8001e80, 0xb8001e81, 0xaa001e82, 0xba001e83, 0xbd001e84, 0xbe001e85, 0xac001ef2, 0xbc001ef3,
		0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3,
		0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3,
		0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3,
		0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3, 0xbc001ef3,
	},
}

// ISO8859_15 is the ISO 8859-15 encoding.
var ISO8859_15 *Charmap = &iso8859_15

var iso8859_15 = Charmap{
	name:          "ISO 8859-15",
	mib:           identifier.ISO885915,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xc2, 0xa1, 0x00}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xac}}, {2, [3]byte{0xc2, 0xa5, 0x00}},
		{2, [3]byte{0xc5, 0xa0, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc5, 0xa1, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc2, 0xaf, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc2, 0xb3, 0x00}},
		{2, [3]byte{0xc5, 0xbd, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc5, 0xbe, 0x00}}, {2, [3]byte{0xc2, 0xb9, 0x00}},
		{2, [3]byte{0xc2, 0xba, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc5, 0x92, 0x00}}, {2, [3]byte{0xc5, 0x93, 0x00}},
		{2, [3]byte{0xc5, 0xb8, 0x00}}, {2, [3]byte{0xc2, 0xbf, 0x00}},
		{2, [3]byte{0xc3, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc3, 0x83, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc3, 0x87, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc3, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc3, 0x8f, 0x00}},
		{2, [3]byte{0xc3, 0x90, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc3, 0x92, 0x00}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{2, [3]byte{0xc3, 0x98, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0x9d, 0x00}},
		{2, [3]byte{0xc3, 0x9e, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc3, 0xa0, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc3, 0xa3, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa5, 0x00}},
		{2, [3]byte{0xc3, 0xa6, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xac, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xb0, 0x00}}, {2, [3]byte{0xc3, 0xb1, 0x00}},
		{2, [3]byte{0xc3, 0xb2, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xb4, 0x00}}, {2, [3]byte{0xc3, 0xb5, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{2, [3]byte{0xc3, 0xb8, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc3, 0xbd, 0x00}},
		{2, [3]byte{0xc3, 0xbe, 0x00}}, {2, [3]byte{0xc3, 0xbf, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa10000a1, 0xa20000a2, 0xa30000a3, 0xa50000a5, 0xa70000a7, 0xa90000a9, 0xaa0000aa,
		0xab0000ab, 0xac0000ac, 0xad0000ad, 0xae0000ae, 0xaf0000af, 0xb00000b0, 0xb10000b1, 0xb20000b2,
		0xb30000b3, 0xb50000b5, 0xb60000b6, 0xb70000b7, 0xb90000b9, 0xba0000ba, 0xbb0000bb, 0xbf0000bf,
		0xc00000c0, 0xc10000c1, 0xc20000c2, 0xc30000c3, 0xc40000c4, 0xc50000c5, 0xc60000c6, 0xc70000c7,
		0xc80000c8, 0xc90000c9, 0xca0000ca, 0xcb0000cb, 0xcc0000cc, 0xcd0000cd, 0xce0000ce, 0xcf0000cf,
		0xd00000d0, 0xd10000d1, 0xd20000d2, 0xd30000d3, 0xd40000d4, 0xd50000d5, 0xd60000d6, 0xd70000d7,
		0xd80000d8, 0xd90000d9, 0xda0000da, 0xdb0000db, 0xdc0000dc, 0xdd0000dd, 0xde0000de, 0xdf0000df,
		0xe00000e0, 0xe10000e1, 0xe20000e2, 0xe30000e3, 0xe40000e4, 0xe50000e5, 0xe60000e6, 0xe70000e7,
		0xe80000e8, 0xe90000e9, 0xea0000ea, 0xeb0000eb, 0xec0000ec, 0xed0000ed, 0xee0000ee, 0xef0000ef,
		0xf00000f0, 0xf10000f1, 0xf20000f2, 0xf30000f3, 0xf40000f4, 0xf50000f5, 0xf60000f6, 0xf70000f7,
		0xf80000f8, 0xf90000f9, 0xfa0000fa, 0xfb0000fb, 0xfc0000fc, 0xfd0000fd, 0xfe0000fe, 0xff0000ff,
		0xbc000152, 0xbd000153, 0xa6000160, 0xa8000161, 0xbe000178, 0xb400017d, 0xb800017e, 0xa40020ac,
		0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac,
		0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac,
		0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac,
		0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac,
	},
}

// ISO8859_16 is the ISO 8859-16 encoding.
var ISO8859_16 *Charmap = &iso8859_16

var iso8859_16 = Charmap{
	name:          "ISO 8859-16",
	mib:           identifier.ISO885916,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xc4, 0x84, 0x00}},
		{2, [3]byte{0xc4, 0x85, 0x00}}, {2, [3]byte{0xc5, 0x81, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xac}}, {3, [3]byte{0xe2, 0x80, 0x9e}},
		{2, [3]byte{0xc5, 0xa0, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc5, 0xa1, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xc8, 0x98, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc5, 0xb9, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc5, 0xba, 0x00}}, {2, [3]byte{0xc5, 0xbb, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc4, 0x8c, 0x00}}, {2, [3]byte{0xc5, 0x82, 0x00}},
		{2, [3]byte{0xc5, 0xbd, 0x00}}, {3, [3]byte{0xe2, 0x80, 0x9d}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc5, 0xbe, 0x00}}, {2, [3]byte{0xc4, 0x8d, 0x00}},
		{2, [3]byte{0xc8, 0x99, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc5, 0x92, 0x00}}, {2, [3]byte{0xc5, 0x93, 0x00}},
		{2, [3]byte{0xc5, 0xb8, 0x00}}, {2, [3]byte{0xc5, 0xbc, 0x00}},
		{2, [3]byte{0xc3, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc4, 0x82, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc4, 0x86, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc3, 0x87, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc3, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc3, 0x8f, 0x00}},
		{2, [3]byte{0xc4, 0x90, 0x00}}, {2, [3]byte{0xc5, 0x83, 0x00}},
		{2, [3]byte{0xc3, 0x92, 0x00}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc5, 0x90, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc5, 0x9a, 0x00}},
		{2, [3]byte{0xc5, 0xb0, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc4, 0x98, 0x00}},
		{2, [3]byte{0xc8, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc3, 0xa0, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc4, 0x83, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc4, 0x87, 0x00}},
		{2, [3]byte{0xc3, 0xa6, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xac, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc4, 0x91, 0x00}}, {2, [3]byte{0xc5, 0x84, 0x00}},
		{2, [3]byte{0xc3, 0xb2, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xb4, 0x00}}, {2, [3]byte{0xc5, 0x91, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc5, 0x9b, 0x00}},
		{2, [3]byte{0xc5, 0xb1, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc4, 0x99, 0x00}},
		{2, [3]byte{0xc8, 0x9b, 0x00}}, {2, [3]byte{0xc3, 0xbf, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa70000a7, 0xa90000a9, 0xab0000ab, 0xad0000ad, 0xb00000b0, 0xb10000b1, 0xb60000b6,
		0xb70000b7, 0xbb0000bb, 0xc00000c0, 0xc10000c1, 0xc20000c2, 0xc40000c4, 0xc60000c6, 0xc70000c7,
		0xc80000c8, 0xc90000c9, 0xca0000ca, 0xcb0000cb, 0xcc0000cc, 0xcd0000cd, 0xce0000ce, 0xcf0000cf,
		0xd20000d2, 0xd30000d3, 0xd40000d4, 0xd60000d6, 0xd90000d9, 0xda0000da, 0xdb0000db, 0xdc0000dc,
		0xdf0000df, 0xe00000e0, 0xe10000e1, 0xe20000e2, 0xe40000e4, 0xe60000e6, 0xe70000e7, 0xe80000e8,
		0xe90000e9, 0xea0000ea, 0xeb0000eb, 0xec0000ec, 0xed0000ed, 0xee0000ee, 0xef0000ef, 0xf20000f2,
		0xf30000f3, 0xf40000f4, 0xf60000f6, 0xf90000f9, 0xfa0000fa, 0xfb0000fb, 0xfc0000fc, 0xff0000ff,
		0xc3000102, 0xe3000103, 0xa1000104, 0xa2000105, 0xc5000106, 0xe5000107, 0xb200010c, 0xb900010d,
		0xd0000110, 0xf0000111, 0xdd000118, 0xfd000119, 0xa3000141, 0xb3000142, 0xd1000143, 0xf1000144,
		0xd5000150, 0xf5000151, 0xbc000152, 0xbd000153, 0xd700015a, 0xf700015b, 0xa6000160, 0xa8000161,
		0xd8000170, 0xf8000171, 0xbe000178, 0xac000179, 0xae00017a, 0xaf00017b, 0xbf00017c, 0xb400017d,
		0xb800017e, 0xaa000218, 0xba000219, 0xde00021a, 0xfe00021b, 0xb500201d, 0xa500201e, 0xa40020ac,
		0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac,
		0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac,
		0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac,
		0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac, 0xa40020ac,
	},
}

// KOI8R is the KOI8-R encoding.
var KOI8R *Charmap = &koi8R

var koi8R = Charmap{
	name:          "KOI8-R",
	mib:           identifier.KOI8R,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xe2, 0x94, 0x80}}, {3, [3]byte{0xe2, 0x94, 0x82}},
		{3, [3]byte{0xe2, 0x94, 0x8c}}, {3, [3]byte{0xe2, 0x94, 0x90}},
		{3, [3]byte{0xe2, 0x94, 0x94}}, {3, [3]byte{0xe2, 0x94, 0x98}},
		{3, [3]byte{0xe2, 0x94, 0x9c}}, {3, [3]byte{0xe2, 0x94, 0xa4}},
		{3, [3]byte{0xe2, 0x94, 0xac}}, {3, [3]byte{0xe2, 0x94, 0xb4}},
		{3, [3]byte{0xe2, 0x94, 0xbc}}, {3, [3]byte{0xe2, 0x96, 0x80}},
		{3, [3]byte{0xe2, 0x96, 0x84}}, {3, [3]byte{0xe2, 0x96, 0x88}},
		{3, [3]byte{0xe2, 0x96, 0x8c}}, {3, [3]byte{0xe2, 0x96, 0x90}},
		{3, [3]byte{0xe2, 0x96, 0x91}}, {3, [3]byte{0xe2, 0x96, 0x92}},
		{3, [3]byte{0xe2, 0x96, 0x93}}, {3, [3]byte{0xe2, 0x8c, 0xa0}},
		{3, [3]byte{0xe2, 0x96, 0xa0}}, {3, [3]byte{0xe2, 0x88, 0x99}},
		{3, [3]byte{0xe2, 0x88, 0x9a}}, {3, [3]byte{0xe2, 0x89, 0x88}},
		{3, [3]byte{0xe2, 0x89, 0xa4}}, {3, [3]byte{0xe2, 0x89, 0xa5}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {3, [3]byte{0xe2, 0x8c, 0xa1}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb2, 0x00}},
		{2, [3]byte{0xc2, 0xb7, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{3, [3]byte{0xe2, 0x95, 0x90}}, {3, [3]byte{0xe2, 0x95, 0x91}},
		{3, [3]byte{0xe2, 0x95, 0x92}}, {2, [3]byte{0xd1, 0x91, 0x00}},
		{3, [3]byte{0xe2, 0x95, 0x93}}, {3, [3]byte{0xe2, 0x95, 0x94}},
		{3, [3]byte{0xe2, 0x95, 0x95}}, {3, [3]byte{0xe2, 0x95, 0x96}},
		{3, [3]byte{0xe2, 0x95, 0x97}}, {3, [3]byte{0xe2, 0x95, 0x98}},
		{3, [3]byte{0xe2, 0x95, 0x99}}, {3, [3]byte{0xe2, 0x95, 0x9a}},
		{3, [3]byte{0xe2, 0x95, 0x9b}}, {3, [3]byte{0xe2, 0x95, 0x9c}},
		{3, [3]byte{0xe2, 0x95, 0x9d}}, {3, [3]byte{0xe2, 0x95, 0x9e}},
		{3, [3]byte{0xe2, 0x95, 0x9f}}, {3, [3]byte{0xe2, 0x95, 0xa0}},
		{3, [3]byte{0xe2, 0x95, 0xa1}}, {2, [3]byte{0xd0, 0x81, 0x00}},
		{3, [3]byte{0xe2, 0x95, 0xa2}}, {3, [3]byte{0xe2, 0x95, 0xa3}},
		{3, [3]byte{0xe2, 0x95, 0xa4}}, {3, [3]byte{0xe2, 0x95, 0xa5}},
		{3, [3]byte{0xe2, 0x95, 0xa6}}, {3, [3]byte{0xe2, 0x95, 0xa7}},
		{3, [3]byte{0xe2, 0x95, 0xa8}}, {3, [3]byte{0xe2, 0x95, 0xa9}},
		{3, [3]byte{0xe2, 0x95, 0xaa}}, {3, [3]byte{0xe2, 0x95, 0xab}},
		{3, [3]byte{0xe2, 0x95, 0xac}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xd1, 0x8e, 0x00}}, {2, [3]byte{0xd0, 0xb0, 0x00}},
		{2, [3]byte{0xd0, 0xb1, 0x00}}, {2, [3]byte{0xd1, 0x86, 0x00}},
		{2, [3]byte{0xd0, 0xb4, 0x00}}, {2, [3]byte{0xd0, 0xb5, 0x00}},
		{2, [3]byte{0xd1, 0x84, 0x00}}, {2, [3]byte{0xd0, 0xb3, 0x00}},
		{2, [3]byte{0xd1, 0x85, 0x00}}, {2, [3]byte{0xd0, 0xb8, 0x00}},
		{2, [3]byte{0xd0, 0xb9, 0x00}}, {2, [3]byte{0xd0, 0xba, 0x00}},
		{2, [3]byte{0xd0, 0xbb, 0x00}}, {2, [3]byte{0xd0, 0xbc, 0x00}},
		{2, [3]byte{0xd0, 0xbd, 0x00}}, {2, [3]byte{0xd0, 0xbe, 0x00}},
		{2, [3]byte{0xd0, 0xbf, 0x00}}, {2, [3]byte{0xd1, 0x8f, 0x00}},
		{2, [3]byte{0xd1, 0x80, 0x00}}, {2, [3]byte{0xd1, 0x81, 0x00}},
		{2, [3]byte{0xd1, 0x82, 0x00}}, {2, [3]byte{0xd1, 0x83, 0x00}},
		{2, [3]byte{0xd0, 0xb6, 0x00}}, {2, [3]byte{0xd0, 0xb2, 0x00}},
		{2, [3]byte{0xd1, 0x8c, 0x00}}, {2, [3]byte{0xd1, 0x8b, 0x00}},
		{2, [3]byte{0xd0, 0xb7, 0x00}}, {2, [3]byte{0xd1, 0x88, 0x00}},
		{2, [3]byte{0xd1, 0x8d, 0x00}}, {2, [3]byte{0xd1, 0x89, 0x00}},
		{2, [3]byte{0xd1, 0x87, 0x00}}, {2, [3]byte{0xd1, 0x8a, 0x00}},
		{2, [3]byte{0xd0, 0xae, 0x00}}, {2, [3]byte{0xd0, 0x90, 0x00}},
		{2, [3]byte{0xd0, 0x91, 0x00}}, {2, [3]byte{0xd0, 0xa6, 0x00}},
		{2, [3]byte{0xd0, 0x94, 0x00}}, {2, [3]byte{0xd0, 0x95, 0x00}},
		{2, [3]byte{0xd0, 0xa4, 0x00}}, {2, [3]byte{0xd0, 0x93, 0x00}},
		{2, [3]byte{0xd0, 0xa5, 0x00}}, {2, [3]byte{0xd0, 0x98, 0x00}},
		{2, [3]byte{0xd0, 0x99, 0x00}}, {2, [3]byte{0xd0, 0x9a, 0x00}},
		{2, [3]byte{0xd0, 0x9b, 0x00}}, {2, [3]byte{0xd0, 0x9c, 0x00}},
		{2, [3]byte{0xd0, 0x9d, 0x00}}, {2, [3]byte{0xd0, 0x9e, 0x00}},
		{2, [3]byte{0xd0, 0x9f, 0x00}}, {2, [3]byte{0xd0, 0xaf, 0x00}},
		{2, [3]byte{0xd0, 0xa0, 0x00}}, {2, [3]byte{0xd0, 0xa1, 0x00}},
		{2, [3]byte{0xd0, 0xa2, 0x00}}, {2, [3]byte{0xd0, 0xa3, 0x00}},
		{2, [3]byte{0xd0, 0x96, 0x00}}, {2, [3]byte{0xd0, 0x92, 0x00}},
		{2, [3]byte{0xd0, 0xac, 0x00}}, {2, [3]byte{0xd0, 0xab, 0x00}},
		{2, [3]byte{0xd0, 0x97, 0x00}}, {2, [3]byte{0xd0, 0xa8, 0x00}},
		{2, [3]byte{0xd0, 0xad, 0x00}}, {2, [3]byte{0xd0, 0xa9, 0x00}},
		{2, [3]byte{0xd0, 0xa7, 0x00}}, {2, [3]byte{0xd0, 0xaa, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0x9a0000a0, 0xbf0000a9, 0x9c0000b0, 0x9d0000b2, 0x9e0000b7, 0x9f0000f7, 0xb3000401, 0xe1000410,
		0xe2000411, 0xf7000412, 0xe7000413, 0xe4000414, 0xe5000415, 0xf6000416, 0xfa000417, 0xe9000418,
		0xea000419, 0xeb00041a, 0xec00041b, 0xed00041c, 0xee00041d, 0xef00041e, 0xf000041f, 0xf2000420,
		0xf3000421, 0xf4000422, 0xf5000423, 0xe6000424, 0xe8000425, 0xe3000426, 0xfe000427, 0xfb000428,
		0xfd000429, 0xff00042a, 0xf900042b, 0xf800042c, 0xfc00042d, 0xe000042e, 0xf100042f, 0xc1000430,
		0xc2000431, 0xd7000432, 0xc7000433, 0xc4000434, 0xc5000435, 0xd6000436, 0xda000437, 0xc9000438,
		0xca000439, 0xcb00043a, 0xcc00043b, 0xcd00043c, 0xce00043d, 0xcf00043e, 0xd000043f, 0xd2000440,
		0xd3000441, 0xd4000442, 0xd5000443, 0xc6000444, 0xc8000445, 0xc3000446, 0xde000447, 0xdb000448,
		0xdd000449, 0xdf00044a, 0xd900044b, 0xd800044c, 0xdc00044d, 0xc000044e, 0xd100044f, 0xa3000451,
		0x95002219, 0x9600221a, 0x97002248, 0x98002264, 0x99002265, 0x93002320, 0x9b002321, 0x80002500,
		0x81002502, 0x8200250c, 0x83002510, 0x84002514, 0x85002518, 0x8600251c, 0x87002524, 0x8800252c,
		0x89002534, 0x8a00253c, 0xa0002550, 0xa1002551, 0xa2002552, 0xa4002553, 0xa5002554, 0xa6002555,
		0xa7002556, 0xa8002557, 0xa9002558, 0xaa002559, 0xab00255a, 0xac00255b, 0xad00255c, 0xae00255d,
		0xaf00255e, 0xb000255f, 0xb1002560, 0xb2002561, 0xb4002562, 0xb5002563, 0xb6002564, 0xb7002565,
		0xb8002566, 0xb9002567, 0xba002568, 0xbb002569, 0xbc00256a, 0xbd00256b, 0xbe00256c, 0x8b002580,
		0x8c002584, 0x8d002588, 0x8e00258c, 0x8f002590, 0x90002591, 0x91002592, 0x92002593, 0x940025a0,
	},
}

// KOI8U is the KOI8-U encoding.
var KOI8U *Charmap = &koi8U

var koi8U = Charmap{
	name:          "KOI8-U",
	mib:           identifier.KOI8U,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xe2, 0x94, 0x80}}, {3, [3]byte{0xe2, 0x94, 0x82}},
		{3, [3]byte{0xe2, 0x94, 0x8c}}, {3, [3]byte{0xe2, 0x94, 0x90}},
		{3, [3]byte{0xe2, 0x94, 0x94}}, {3, [3]byte{0xe2, 0x94, 0x98}},
		{3, [3]byte{0xe2, 0x94, 0x9c}}, {3, [3]byte{0xe2, 0x94, 0xa4}},
		{3, [3]byte{0xe2, 0x94, 0xac}}, {3, [3]byte{0xe2, 0x94, 0xb4}},
		{3, [3]byte{0xe2, 0x94, 0xbc}}, {3, [3]byte{0xe2, 0x96, 0x80}},
		{3, [3]byte{0xe2, 0x96, 0x84}}, {3, [3]byte{0xe2, 0x96, 0x88}},
		{3, [3]byte{0xe2, 0x96, 0x8c}}, {3, [3]byte{0xe2, 0x96, 0x90}},
		{3, [3]byte{0xe2, 0x96, 0x91}}, {3, [3]byte{0xe2, 0x96, 0x92}},
		{3, [3]byte{0xe2, 0x96, 0x93}}, {3, [3]byte{0xe2, 0x8c, 0xa0}},
		{3, [3]byte{0xe2, 0x96, 0xa0}}, {3, [3]byte{0xe2, 0x88, 0x99}},
		{3, [3]byte{0xe2, 0x88, 0x9a}}, {3, [3]byte{0xe2, 0x89, 0x88}},
		{3, [3]byte{0xe2, 0x89, 0xa4}}, {3, [3]byte{0xe2, 0x89, 0xa5}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {3, [3]byte{0xe2, 0x8c, 0xa1}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb2, 0x00}},
		{2, [3]byte{0xc2, 0xb7, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{3, [3]byte{0xe2, 0x95, 0x90}}, {3, [3]byte{0xe2, 0x95, 0x91}},
		{3, [3]byte{0xe2, 0x95, 0x92}}, {2, [3]byte{0xd1, 0x91, 0x00}},
		{2, [3]byte{0xd1, 0x94, 0x00}}, {3, [3]byte{0xe2, 0x95, 0x94}},
		{2, [3]byte{0xd1, 0x96, 0x00}}, {2, [3]byte{0xd1, 0x97, 0x00}},
		{3, [3]byte{0xe2, 0x95, 0x97}}, {3, [3]byte{0xe2, 0x95, 0x98}},
		{3, [3]byte{0xe2, 0x95, 0x99}}, {3, [3]byte{0xe2, 0x95, 0x9a}},
		{3, [3]byte{0xe2, 0x95, 0x9b}}, {2, [3]byte{0xd2, 0x91, 0x00}},
		{2, [3]byte{0xd1, 0x9e, 0x00}}, {3, [3]byte{0xe2, 0x95, 0x9e}},
		{3, [3]byte{0xe2, 0x95, 0x9f}}, {3, [3]byte{0xe2, 0x95, 0xa0}},
		{3, [3]byte{0xe2, 0x95, 0xa1}}, {2, [3]byte{0xd0, 0x81, 0x00}},
		{2, [3]byte{0xd0, 0x84, 0x00}}, {3, [3]byte{0xe2, 0x95, 0xa3}},
		{2, [3]byte{0xd0, 0x86, 0x00}}, {2, [3]byte{0xd0, 0x87, 0x00}},
		{3, [3]byte{0xe2, 0x95, 0xa6}}, {3, [3]byte{0xe2, 0x95, 0xa7}},
		{3, [3]byte{0xe2, 0x95, 0xa8}}, {3, [3]byte{0xe2, 0x95, 0xa9}},
		{3, [3]byte{0xe2, 0x95, 0xaa}}, {2, [3]byte{0xd2, 0x90, 0x00}},
		{2, [3]byte{0xd0, 0x8e, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xd1, 0x8e, 0x00}}, {2, [3]byte{0xd0, 0xb0, 0x00}},
		{2, [3]byte{0xd0, 0xb1, 0x00}}, {2, [3]byte{0xd1, 0x86, 0x00}},
		{2, [3]byte{0xd0, 0xb4, 0x00}}, {2, [3]byte{0xd0, 0xb5, 0x00}},
		{2, [3]byte{0xd1, 0x84, 0x00}}, {2, [3]byte{0xd0, 0xb3, 0x00}},
		{2, [3]byte{0xd1, 0x85, 0x00}}, {2, [3]byte{0xd0, 0xb8, 0x00}},
		{2, [3]byte{0xd0, 0xb9, 0x00}}, {2, [3]byte{0xd0, 0xba, 0x00}},
		{2, [3]byte{0xd0, 0xbb, 0x00}}, {2, [3]byte{0xd0, 0xbc, 0x00}},
		{2, [3]byte{0xd0, 0xbd, 0x00}}, {2, [3]byte{0xd0, 0xbe, 0x00}},
		{2, [3]byte{0xd0, 0xbf, 0x00}}, {2, [3]byte{0xd1, 0x8f, 0x00}},
		{2, [3]byte{0xd1, 0x80, 0x00}}, {2, [3]byte{0xd1, 0x81, 0x00}},
		{2, [3]byte{0xd1, 0x82, 0x00}}, {2, [3]byte{0xd1, 0x83, 0x00}},
		{2, [3]byte{0xd0, 0xb6, 0x00}}, {2, [3]byte{0xd0, 0xb2, 0x00}},
		{2, [3]byte{0xd1, 0x8c, 0x00}}, {2, [3]byte{0xd1, 0x8b, 0x00}},
		{2, [3]byte{0xd0, 0xb7, 0x00}}, {2, [3]byte{0xd1, 0x88, 0x00}},
		{2, [3]byte{0xd1, 0x8d, 0x00}}, {2, [3]byte{0xd1, 0x89, 0x00}},
		{2, [3]byte{0xd1, 0x87, 0x00}}, {2, [3]byte{0xd1, 0x8a, 0x00}},
		{2, [3]byte{0xd0, 0xae, 0x00}}, {2, [3]byte{0xd0, 0x90, 0x00}},
		{2, [3]byte{0xd0, 0x91, 0x00}}, {2, [3]byte{0xd0, 0xa6, 0x00}},
		{2, [3]byte{0xd0, 0x94, 0x00}}, {2, [3]byte{0xd0, 0x95, 0x00}},
		{2, [3]byte{0xd0, 0xa4, 0x00}}, {2, [3]byte{0xd0, 0x93, 0x00}},
		{2, [3]byte{0xd0, 0xa5, 0x00}}, {2, [3]byte{0xd0, 0x98, 0x00}},
		{2, [3]byte{0xd0, 0x99, 0x00}}, {2, [3]byte{0xd0, 0x9a, 0x00}},
		{2, [3]byte{0xd0, 0x9b, 0x00}}, {2, [3]byte{0xd0, 0x9c, 0x00}},
		{2, [3]byte{0xd0, 0x9d, 0x00}}, {2, [3]byte{0xd0, 0x9e, 0x00}},
		{2, [3]byte{0xd0, 0x9f, 0x00}}, {2, [3]byte{0xd0, 0xaf, 0x00}},
		{2, [3]byte{0xd0, 0xa0, 0x00}}, {2, [3]byte{0xd0, 0xa1, 0x00}},
		{2, [3]byte{0xd0, 0xa2, 0x00}}, {2, [3]byte{0xd0, 0xa3, 0x00}},
		{2, [3]byte{0xd0, 0x96, 0x00}}, {2, [3]byte{0xd0, 0x92, 0x00}},
		{2, [3]byte{0xd0, 0xac, 0x00}}, {2, [3]byte{0xd0, 0xab, 0x00}},
		{2, [3]byte{0xd0, 0x97, 0x00}}, {2, [3]byte{0xd0, 0xa8, 0x00}},
		{2, [3]byte{0xd0, 0xad, 0x00}}, {2, [3]byte{0xd0, 0xa9, 0x00}},
		{2, [3]byte{0xd0, 0xa7, 0x00}}, {2, [3]byte{0xd0, 0xaa, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0x9a0000a0, 0xbf0000a9, 0x9c0000b0, 0x9d0000b2, 0x9e0000b7, 0x9f0000f7, 0xb3000401, 0xb4000404,
		0xb6000406, 0xb7000407, 0xbe00040e, 0xe1000410, 0xe2000411, 0xf7000412, 0xe7000413, 0xe4000414,
		0xe5000415, 0xf6000416, 0xfa000417, 0xe9000418, 0xea000419, 0xeb00041a, 0xec00041b, 0xed00041c,
		0xee00041d, 0xef00041e, 0xf000041f, 0xf2000420, 0xf3000421, 0xf4000422, 0xf5000423, 0xe6000424,
		0xe8000425, 0xe3000426, 0xfe000427, 0xfb000428, 0xfd000429, 0xff00042a, 0xf900042b, 0xf800042c,
		0xfc00042d, 0xe000042e, 0xf100042f, 0xc1000430, 0xc2000431, 0xd7000432, 0xc7000433, 0xc4000434,
		0xc5000435, 0xd6000436, 0xda000437, 0xc9000438, 0xca000439, 0xcb00043a, 0xcc00043b, 0xcd00043c,
		0xce00043d, 0xcf00043e, 0xd000043f, 0xd2000440, 0xd3000441, 0xd4000442, 0xd5000443, 0xc6000444,
		0xc8000445, 0xc3000446, 0xde000447, 0xdb000448, 0xdd000449, 0xdf00044a, 0xd900044b, 0xd800044c,
		0xdc00044d, 0xc000044e, 0xd100044f, 0xa3000451, 0xa4000454, 0xa6000456, 0xa7000457, 0xae00045e,
		0xbd000490, 0xad000491, 0x95002219, 0x9600221a, 0x97002248, 0x98002264, 0x99002265, 0x93002320,
		0x9b002321, 0x80002500, 0x81002502, 0x8200250c, 0x83002510, 0x84002514, 0x85002518, 0x8600251c,
		0x87002524, 0x8800252c, 0x89002534, 0x8a00253c, 0xa0002550, 0xa1002551, 0xa2002552, 0xa5002554,
		0xa8002557, 0xa9002558, 0xaa002559, 0xab00255a, 0xac00255b, 0xaf00255e, 0xb000255f, 0xb1002560,
		0xb2002561, 0xb5002563, 0xb8002566, 0xb9002567, 0xba002568, 0xbb002569, 0xbc00256a, 0x8b002580,
		0x8c002584, 0x8d002588, 0x8e00258c, 0x8f002590, 0x90002591, 0x91002592, 0x92002593, 0x940025a0,
	},
}

// Macintosh is the Macintosh encoding.
var Macintosh *Charmap = &macintosh

var macintosh = Charmap{
	name:          "Macintosh",
	mib:           identifier.Macintosh,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x87, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc3, 0x91, 0x00}}, {2, [3]byte{0xc3, 0x96, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa0, 0x00}}, {2, [3]byte{0xc3, 0xa2, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa3, 0x00}},
		{2, [3]byte{0xc3, 0xa5, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xa9, 0x00}}, {2, [3]byte{0xc3, 0xa8, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xad, 0x00}}, {2, [3]byte{0xc3, 0xac, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xb1, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xb2, 0x00}}, {2, [3]byte{0xc3, 0xb4, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb5, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xbb, 0x00}}, {2, [3]byte{0xc3, 0xbc, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0xa0}}, {2, [3]byte{0xc2, 0xb0, 0x00}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa7, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xa2}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{3, [3]byte{0xe2, 0x84, 0xa2}}, {2, [3]byte{0xc2, 0xb4, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {3, [3]byte{0xe2, 0x89, 0xa0}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc3, 0x98, 0x00}},
		{3, [3]byte{0xe2, 0x88, 0x9e}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{3, [3]byte{0xe2, 0x89, 0xa4}}, {3, [3]byte{0xe2, 0x89, 0xa5}},
		{2, [3]byte{0xc2, 0xa5, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{3, [3]byte{0xe2, 0x88, 0x82}}, {3, [3]byte{0xe2, 0x88, 0x91}},
		{3, [3]byte{0xe2, 0x88, 0x8f}}, {2, [3]byte{0xcf, 0x80, 0x00}},
		{3, [3]byte{0xe2, 0x88, 0xab}}, {2, [3]byte{0xc2, 0xaa, 0x00}},
		{2, [3]byte{0xc2, 0xba, 0x00}}, {2, [3]byte{0xce, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xa6, 0x00}}, {2, [3]byte{0xc3, 0xb8, 0x00}},
		{2, [3]byte{0xc2, 0xbf, 0x00}}, {2, [3]byte{0xc2, 0xa1, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {3, [3]byte{0xe2, 0x88, 0x9a}},
		{2, [3]byte{0xc6, 0x92, 0x00}}, {3, [3]byte{0xe2, 0x89, 0x88}},
		{3, [3]byte{0xe2, 0x88, 0x86}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xbb, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xa6}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xc3, 0x80, 0x00}},
		{2, [3]byte{0xc3, 0x83, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{2, [3]byte{0xc5, 0x92, 0x00}}, {2, [3]byte{0xc5, 0x93, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x93}}, {3, [3]byte{0xe2, 0x80, 0x94}},
		{3, [3]byte{0xe2, 0x80, 0x9c}}, {3, [3]byte{0xe2, 0x80, 0x9d}},
		{3, [3]byte{0xe2, 0x80, 0x98}}, {3, [3]byte{0xe2, 0x80, 0x99}},
		{2, [3]byte{0xc3, 0xb7, 0x00}}, {3, [3]byte{0xe2, 0x97, 0x8a}},
		{2, [3]byte{0xc3, 0xbf, 0x00}}, {2, [3]byte{0xc5, 0xb8, 0x00}},
		{3, [3]byte{0xe2, 0x81, 0x84}}, {3, [3]byte{0xe2, 0x82, 0xac}},
		{3, [3]byte{0xe2, 0x80, 0xb9}}, {3, [3]byte{0xe2, 0x80, 0xba}},
		{3, [3]byte{0xef, 0xac, 0x81}}, {3, [3]byte{0xef, 0xac, 0x82}},
		{3, [3]byte{0xe2, 0x80, 0xa1}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x9a}}, {3, [3]byte{0xe2, 0x80, 0x9e}},
		{3, [3]byte{0xe2, 0x80, 0xb0}}, {2, [3]byte{0xc3, 0x82, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x8b, 0x00}}, {2, [3]byte{0xc3, 0x88, 0x00}},
		{2, [3]byte{0xc3, 0x8d, 0x00}}, {2, [3]byte{0xc3, 0x8e, 0x00}},
		{2, [3]byte{0xc3, 0x8f, 0x00}}, {2, [3]byte{0xc3, 0x8c, 0x00}},
		{2, [3]byte{0xc3, 0x93, 0x00}}, {2, [3]byte{0xc3, 0x94, 0x00}},
		{3, [3]byte{0xef, 0xa3, 0xbf}}, {2, [3]byte{0xc3, 0x92, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x99, 0x00}}, {2, [3]byte{0xc4, 0xb1, 0x00}},
		{2, [3]byte{0xcb, 0x86, 0x00}}, {2, [3]byte{0xcb, 0x9c, 0x00}},
		{2, [3]byte{0xc2, 0xaf, 0x00}}, {2, [3]byte{0xcb, 0x98, 0x00}},
		{2, [3]byte{0xcb, 0x99, 0x00}}, {2, [3]byte{0xcb, 0x9a, 0x00}},
		{2, [3]byte{0xc2, 0xb8, 0x00}}, {2, [3]byte{0xcb, 0x9d, 0x00}},
		{2, [3]byte{0xcb, 0x9b, 0x00}}, {2, [3]byte{0xcb, 0x87, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xca0000a0, 0xc10000a1, 0xa20000a2, 0xa30000a3, 0xb40000a5, 0xa40000a7, 0xac0000a8, 0xa90000a9,
		0xbb0000aa, 0xc70000ab, 0xc20000ac, 0xa80000ae, 0xf80000af, 0xa10000b0, 0xb10000b1, 0xab0000b4,
		0xb50000b5, 0xa60000b6, 0xe10000b7, 0xfc0000b8, 0xbc0000ba, 0xc80000bb, 0xc00000bf, 0xcb0000c0,
		0xe70000c1, 0xe50000c2, 0xcc0000c3, 0x800000c4, 0x810000c5, 0xae0000c6, 0x820000c7, 0xe90000c8,
		0x830000c9, 0xe60000ca, 0xe80000cb, 0xed0000cc, 0xea0000cd, 0xeb0000ce, 0xec0000cf, 0x840000d1,
		0xf10000d2, 0xee0000d3, 0xef0000d4, 0xcd0000d5, 0x850000d6, 0xaf0000d8, 0xf40000d9, 0xf20000da,
		0xf30000db, 0x860000dc, 0xa70000df, 0x880000e0, 0x870000e1, 0x890000e2, 0x8b0000e3, 0x8a0000e4,
		0x8c0000e5, 0xbe0000e6, 0x8d0000e7, 0x8f0000e8, 0x8e0000e9, 0x900000ea, 0x910000eb, 0x930000ec,
		0x920000ed, 0x940000ee, 0x950000ef, 0x960000f1, 0x980000f2, 0x970000f3, 0x990000f4, 0x9b0000f5,
		0x9a0000f6, 0xd60000f7, 0xbf0000f8, 0x9d0000f9, 0x9c0000fa, 0x9e0000fb, 0x9f0000fc, 0xd80000ff,
		0xf5000131, 0xce000152, 0xcf000153, 0xd9000178, 0xc4000192, 0xf60002c6, 0xff0002c7, 0xf90002d8,
		0xfa0002d9, 0xfb0002da, 0xfe0002db, 0xf70002dc, 0xfd0002dd, 0xbd0003a9, 0xb90003c0, 0xd0002013,
		0xd1002014, 0xd4002018, 0xd5002019, 0xe200201a, 0xd200201c, 0xd300201d, 0xe300201e, 0xa0002020,
		0xe0002021, 0xa5002022, 0xc9002026, 0xe4002030, 0xdc002039, 0xdd00203a, 0xda002044, 0xdb0020ac,
		0xaa002122, 0xb6002202, 0xc6002206, 0xb800220f, 0xb7002211, 0xc300221a, 0xb000221e, 0xba00222b,
		0xc5002248, 0xad002260, 0xb2002264, 0xb3002265, 0xd70025ca, 0xf000f8ff, 0xde00fb01, 0xdf00fb02,
	},
}

// MacintoshCyrillic is the Macintosh Cyrillic encoding.
var MacintoshCyrillic *Charmap = &macintoshCyrillic

var macintoshCyrillic = Charmap{
	name:          "Macintosh Cyrillic",
	mib:           identifier.MacintoshCyrillic,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xd0, 0x90, 0x00}}, {2, [3]byte{0xd0, 0x91, 0x00}},
		{2, [3]byte{0xd0, 0x92, 0x00}}, {2, [3]byte{0xd0, 0x93, 0x00}},
		{2, [3]byte{0xd0, 0x94, 0x00}}, {2, [3]byte{0xd0, 0x95, 0x00}},
		{2, [3]byte{0xd0, 0x96, 0x00}}, {2, [3]byte{0xd0, 0x97, 0x00}},
		{2, [3]byte{0xd0, 0x98, 0x00}}, {2, [3]byte{0xd0, 0x99, 0x00}},
		{2, [3]byte{0xd0, 0x9a, 0x00}}, {2, [3]byte{0xd0, 0x9b, 0x00}},
		{2, [3]byte{0xd0, 0x9c, 0x00}}, {2, [3]byte{0xd0, 0x9d, 0x00}},
		{2, [3]byte{0xd0, 0x9e, 0x00}}, {2, [3]byte{0xd0, 0x9f, 0x00}},
		{2, [3]byte{0xd0, 0xa0, 0x00}}, {2, [3]byte{0xd0, 0xa1, 0x00}},
		{2, [3]byte{0xd0, 0xa2, 0x00}}, {2, [3]byte{0xd0, 0xa3, 0x00}},
		{2, [3]byte{0xd0, 0xa4, 0x00}}, {2, [3]byte{0xd0, 0xa5, 0x00}},
		{2, [3]byte{0xd0, 0xa6, 0x00}}, {2, [3]byte{0xd0, 0xa7, 0x00}},
		{2, [3]byte{0xd0, 0xa8, 0x00}}, {2, [3]byte{0xd0, 0xa9, 0x00}},
		{2, [3]byte{0xd0, 0xaa, 0x00}}, {2, [3]byte{0xd0, 0xab, 0x00}},
		{2, [3]byte{0xd0, 0xac, 0x00}}, {2, [3]byte{0xd0, 0xad, 0x00}},
		{2, [3]byte{0xd0, 0xae, 0x00}}, {2, [3]byte{0xd0, 0xaf, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0xa0}}, {2, [3]byte{0xc2, 0xb0, 0x00}},
		{2, [3]byte{0xd2, 0x90, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa7, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xa2}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xd0, 0x86, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{3, [3]byte{0xe2, 0x84, 0xa2}}, {2, [3]byte{0xd0, 0x82, 0x00}},
		{2, [3]byte{0xd1, 0x92, 0x00}}, {3, [3]byte{0xe2, 0x89, 0xa0}},
		{2, [3]byte{0xd0, 0x83, 0x00}}, {2, [3]byte{0xd1, 0x93, 0x00}},
		{3, [3]byte{0xe2, 0x88, 0x9e}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{3, [3]byte{0xe2, 0x89, 0xa4}}, {3, [3]byte{0xe2, 0x89, 0xa5}},
		{2, [3]byte{0xd1, 0x96, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xd2, 0x91, 0x00}}, {2, [3]byte{0xd0, 0x88, 0x00}},
		{2, [3]byte{0xd0, 0x84, 0x00}}, {2, [3]byte{0xd1, 0x94, 0x00}},
		{2, [3]byte{0xd0, 0x87, 0x00}}, {2, [3]byte{0xd1, 0x97, 0x00}},
		{2, [3]byte{0xd0, 0x89, 0x00}}, {2, [3]byte{0xd1, 0x99, 0x00}},
		{2, [3]byte{0xd0, 0x8a, 0x00}}, {2, [3]byte{0xd1, 0x9a, 0x00}},
		{2, [3]byte{0xd1, 0x98, 0x00}}, {2, [3]byte{0xd0, 0x85, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {3, [3]byte{0xe2, 0x88, 0x9a}},
		{2, [3]byte{0xc6, 0x92, 0x00}}, {3, [3]byte{0xe2, 0x89, 0x88}},
		{3, [3]byte{0xe2, 0x88, 0x86}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xbb, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xa6}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xd0, 0x8b, 0x00}},
		{2, [3]byte{0xd1, 0x9b, 0x00}}, {2, [3]byte{0xd0, 0x8c, 0x00}},
		{2, [3]byte{0xd1, 0x9c, 0x00}}, {2, [3]byte{0xd1, 0x95, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x93}}, {3, [3]byte{0xe2, 0x80, 0x94}},
		{3, [3]byte{0xe2, 0x80, 0x9c}}, {3, [3]byte{0xe2, 0x80, 0x9d}},
		{3, [3]byte{0xe2, 0x80, 0x98}}, {3, [3]byte{0xe2, 0x80, 0x99}},
		{2, [3]byte{0xc3, 0xb7, 0x00}}, {3, [3]byte{0xe2, 0x80, 0x9e}},
		{2, [3]byte{0xd0, 0x8e, 0x00}}, {2, [3]byte{0xd1, 0x9e, 0x00}},
		{2, [3]byte{0xd0, 0x8f, 0x00}}, {2, [3]byte{0xd1, 0x9f, 0x00}},
		{3, [3]byte{0xe2, 0x84, 0x96}}, {2, [3]byte{0xd0, 0x81, 0x00}},
		{2, [3]byte{0xd1, 0x91, 0x00}}, {2, [3]byte{0xd1, 0x8f, 0x00}},
		{2, [3]byte{0xd0, 0xb0, 0x00}}, {2, [3]byte{0xd0, 0xb1, 0x00}},
		{2, [3]byte{0xd0, 0xb2, 0x00}}, {2, [3]byte{0xd0, 0xb3, 0x00}},
		{2, [3]byte{0xd0, 0xb4, 0x00}}, {2, [3]byte{0xd0, 0xb5, 0x00}},
		{2, [3]byte{0xd0, 0xb6, 0x00}}, {2, [3]byte{0xd0, 0xb7, 0x00}},
		{2, [3]byte{0xd0, 0xb8, 0x00}}, {2, [3]byte{0xd0, 0xb9, 0x00}},
		{2, [3]byte{0xd0, 0xba, 0x00}}, {2, [3]byte{0xd0, 0xbb, 0x00}},
		{2, [3]byte{0xd0, 0xbc, 0x00}}, {2, [3]byte{0xd0, 0xbd, 0x00}},
		{2, [3]byte{0xd0, 0xbe, 0x00}}, {2, [3]byte{0xd0, 0xbf, 0x00}},
		{2, [3]byte{0xd1, 0x80, 0x00}}, {2, [3]byte{0xd1, 0x81, 0x00}},
		{2, [3]byte{0xd1, 0x82, 0x00}}, {2, [3]byte{0xd1, 0x83, 0x00}},
		{2, [3]byte{0xd1, 0x84, 0x00}}, {2, [3]byte{0xd1, 0x85, 0x00}},
		{2, [3]byte{0xd1, 0x86, 0x00}}, {2, [3]byte{0xd1, 0x87, 0x00}},
		{2, [3]byte{0xd1, 0x88, 0x00}}, {2, [3]byte{0xd1, 0x89, 0x00}},
		{2, [3]byte{0xd1, 0x8a, 0x00}}, {2, [3]byte{0xd1, 0x8b, 0x00}},
		{2, [3]byte{0xd1, 0x8c, 0x00}}, {2, [3]byte{0xd1, 0x8d, 0x00}},
		{2, [3]byte{0xd1, 0x8e, 0x00}}, {3, [3]byte{0xe2, 0x82, 0xac}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xca0000a0, 0xa30000a3, 0xa40000a7, 0xa90000a9, 0xc70000ab, 0xc20000ac, 0xa80000ae, 0xa10000b0,
		0xb10000b1, 0xb50000b5, 0xa60000b6, 0xc80000bb, 0xd60000f7, 0xc4000192, 0xdd000401, 0xab000402,
		0xae000403, 0xb8000404, 0xc1000405, 0xa7000406, 0xba000407, 0xb7000408, 0xbc000409, 0xbe00040a,
		0xcb00040b, 0xcd00040c, 0xd800040e, 0xda00040f, 0x80000410, 0x81000411, 0x82000412, 0x83000413,
		0x84000414, 0x85000415, 0x86000416, 0x87000417, 0x88000418, 0x89000419, 0x8a00041a, 0x8b00041b,
		0x8c00041c, 0x8d00041d, 0x8e00041e, 0x8f00041f, 0x90000420, 0x91000421, 0x92000422, 0x93000423,
		0x94000424, 0x95000425, 0x96000426, 0x97000427, 0x98000428, 0x99000429, 0x9a00042a, 0x9b00042b,
		0x9c00042c, 0x9d00042d, 0x9e00042e, 0x9f00042f, 0xe0000430, 0xe1000431, 0xe2000432, 0xe3000433,
		0xe4000434, 0xe5000435, 0xe6000436, 0xe7000437, 0xe8000438, 0xe9000439, 0xea00043a, 0xeb00043b,
		0xec00043c, 0xed00043d, 0xee00043e, 0xef00043f, 0xf0000440, 0xf1000441, 0xf2000442, 0xf3000443,
		0xf4000444, 0xf5000445, 0xf6000446, 0xf7000447, 0xf8000448, 0xf9000449, 0xfa00044a, 0xfb00044b,
		0xfc00044c, 0xfd00044d, 0xfe00044e, 0xdf00044f, 0xde000451, 0xac000452, 0xaf000453, 0xb9000454,
		0xcf000455, 0xb4000456, 0xbb000457, 0xc0000458, 0xbd000459, 0xbf00045a, 0xcc00045b, 0xce00045c,
		0xd900045e, 0xdb00045f, 0xa2000490, 0xb6000491, 0xd0002013, 0xd1002014, 0xd4002018, 0xd5002019,
		0xd200201c, 0xd300201d, 0xd700201e, 0xa0002020, 0xa5002022, 0xc9002026, 0xff0020ac, 0xdc002116,
		0xaa002122, 0xc6002206, 0xc300221a, 0xb000221e, 0xc5002248, 0xad002260, 0xb2002264, 0xb3002265,
	},
}

// Windows874 is the Windows 874 encoding.
var Windows874 *Charmap = &windows874

var windows874 = Charmap{
	name:          "Windows 874",
	mib:           identifier.Windows874,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xac}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0xa6}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0x98}},
		{3, [3]byte{0xe2, 0x80, 0x99}}, {3, [3]byte{0xe2, 0x80, 0x9c}},
		{3, [3]byte{0xe2, 0x80, 0x9d}}, {3, [3]byte{0xe2, 0x80, 0xa2}},
		{3, [3]byte{0xe2, 0x80, 0x93}}, {3, [3]byte{0xe2, 0x80, 0x94}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {3, [3]byte{0xe0, 0xb8, 0x81}},
		{3, [3]byte{0xe0, 0xb8, 0x82}}, {3, [3]byte{0xe0, 0xb8, 0x83}},
		{3, [3]byte{0xe0, 0xb8, 0x84}}, {3, [3]byte{0xe0, 0xb8, 0x85}},
		{3, [3]byte{0xe0, 0xb8, 0x86}}, {3, [3]byte{0xe0, 0xb8, 0x87}},
		{3, [3]byte{0xe0, 0xb8, 0x88}}, {3, [3]byte{0xe0, 0xb8, 0x89}},
		{3, [3]byte{0xe0, 0xb8, 0x8a}}, {3, [3]byte{0xe0, 0xb8, 0x8b}},
		{3, [3]byte{0xe0, 0xb8, 0x8c}}, {3, [3]byte{0xe0, 0xb8, 0x8d}},
		{3, [3]byte{0xe0, 0xb8, 0x8e}}, {3, [3]byte{0xe0, 0xb8, 0x8f}},
		{3, [3]byte{0xe0, 0xb8, 0x90}}, {3, [3]byte{0xe0, 0xb8, 0x91}},
		{3, [3]byte{0xe0, 0xb8, 0x92}}, {3, [3]byte{0xe0, 0xb8, 0x93}},
		{3, [3]byte{0xe0, 0xb8, 0x94}}, {3, [3]byte{0xe0, 0xb8, 0x95}},
		{3, [3]byte{0xe0, 0xb8, 0x96}}, {3, [3]byte{0xe0, 0xb8, 0x97}},
		{3, [3]byte{0xe0, 0xb8, 0x98}}, {3, [3]byte{0xe0, 0xb8, 0x99}},
		{3, [3]byte{0xe0, 0xb8, 0x9a}}, {3, [3]byte{0xe0, 0xb8, 0x9b}},
		{3, [3]byte{0xe0, 0xb8, 0x9c}}, {3, [3]byte{0xe0, 0xb8, 0x9d}},
		{3, [3]byte{0xe0, 0xb8, 0x9e}}, {3, [3]byte{0xe0, 0xb8, 0x9f}},
		{3, [3]byte{0xe0, 0xb8, 0xa0}}, {3, [3]byte{0xe0, 0xb8, 0xa1}},
		{3, [3]byte{0xe0, 0xb8, 0xa2}}, {3, [3]byte{0xe0, 0xb8, 0xa3}},
		{3, [3]byte{0xe0, 0xb8, 0xa4}}, {3, [3]byte{0xe0, 0xb8, 0xa5}},
		{3, [3]byte{0xe0, 0xb8, 0xa6}}, {3, [3]byte{0xe0, 0xb8, 0xa7}},
		{3, [3]byte{0xe0, 0xb8, 0xa8}}, {3, [3]byte{0xe0, 0xb8, 0xa9}},
		{3, [3]byte{0xe0, 0xb8, 0xaa}}, {3, [3]byte{0xe0, 0xb8, 0xab}},
		{3, [3]byte{0xe0, 0xb8, 0xac}}, {3, [3]byte{0xe0, 0xb8, 0xad}},
		{3, [3]byte{0xe0, 0xb8, 0xae}}, {3, [3]byte{0xe0, 0xb8, 0xaf}},
		{3, [3]byte{0xe0, 0xb8, 0xb0}}, {3, [3]byte{0xe0, 0xb8, 0xb1}},
		{3, [3]byte{0xe0, 0xb8, 0xb2}}, {3, [3]byte{0xe0, 0xb8, 0xb3}},
		{3, [3]byte{0xe0, 0xb8, 0xb4}}, {3, [3]byte{0xe0, 0xb8, 0xb5}},
		{3, [3]byte{0xe0, 0xb8, 0xb6}}, {3, [3]byte{0xe0, 0xb8, 0xb7}},
		{3, [3]byte{0xe0, 0xb8, 0xb8}}, {3, [3]byte{0xe0, 0xb8, 0xb9}},
		{3, [3]byte{0xe0, 0xb8, 0xba}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe0, 0xb8, 0xbf}},
		{3, [3]byte{0xe0, 0xb9, 0x80}}, {3, [3]byte{0xe0, 0xb9, 0x81}},
		{3, [3]byte{0xe0, 0xb9, 0x82}}, {3, [3]byte{0xe0, 0xb9, 0x83}},
		{3, [3]byte{0xe0, 0xb9, 0x84}}, {3, [3]byte{0xe0, 0xb9, 0x85}},
		{3, [3]byte{0xe0, 0xb9, 0x86}}, {3, [3]byte{0xe0, 0xb9, 0x87}},
		{3, [3]byte{0xe0, 0xb9, 0x88}}, {3, [3]byte{0xe0, 0xb9, 0x89}},
		{3, [3]byte{0xe0, 0xb9, 0x8a}}, {3, [3]byte{0xe0, 0xb9, 0x8b}},
		{3, [3]byte{0xe0, 0xb9, 0x8c}}, {3, [3]byte{0xe0, 0xb9, 0x8d}},
		{3, [3]byte{0xe0, 0xb9, 0x8e}}, {3, [3]byte{0xe0, 0xb9, 0x8f}},
		{3, [3]byte{0xe0, 0xb9, 0x90}}, {3, [3]byte{0xe0, 0xb9, 0x91}},
		{3, [3]byte{0xe0, 0xb9, 0x92}}, {3, [3]byte{0xe0, 0xb9, 0x93}},
		{3, [3]byte{0xe0, 0xb9, 0x94}}, {3, [3]byte{0xe0, 0xb9, 0x95}},
		{3, [3]byte{0xe0, 0xb9, 0x96}}, {3, [3]byte{0xe0, 0xb9, 0x97}},
		{3, [3]byte{0xe0, 0xb9, 0x98}}, {3, [3]byte{0xe0, 0xb9, 0x99}},
		{3, [3]byte{0xe0, 0xb9, 0x9a}}, {3, [3]byte{0xe0, 0xb9, 0x9b}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa1000e01, 0xa2000e02, 0xa3000e03, 0xa4000e04, 0xa5000e05, 0xa6000e06, 0xa7000e07,
		0xa8000e08, 0xa9000e09, 0xaa000e0a, 0xab000e0b, 0xac000e0c, 0xad000e0d, 0xae000e0e, 0xaf000e0f,
		0xb0000e10, 0xb1000e11, 0xb2000e12, 0xb3000e13, 0xb4000e14, 0xb5000e15, 0xb6000e16, 0xb7000e17,
		0xb8000e18, 0xb9000e19, 0xba000e1a, 0xbb000e1b, 0xbc000e1c, 0xbd000e1d, 0xbe000e1e, 0xbf000e1f,
		0xc0000e20, 0xc1000e21, 0xc2000e22, 0xc3000e23, 0xc4000e24, 0xc5000e25, 0xc6000e26, 0xc7000e27,
		0xc8000e28, 0xc9000e29, 0xca000e2a, 0xcb000e2b, 0xcc000e2c, 0xcd000e2d, 0xce000e2e, 0xcf000e2f,
		0xd0000e30, 0xd1000e31, 0xd2000e32, 0xd3000e33, 0xd4000e34, 0xd5000e35, 0xd6000e36, 0xd7000e37,
		0xd8000e38, 0xd9000e39, 0xda000e3a, 0xdf000e3f, 0xe0000e40, 0xe1000e41, 0xe2000e42, 0xe3000e43,
		0xe4000e44, 0xe5000e45, 0xe6000e46, 0xe7000e47, 0xe8000e48, 0xe9000e49, 0xea000e4a, 0xeb000e4b,
		0xec000e4c, 0xed000e4d, 0xee000e4e, 0xef000e4f, 0xf0000e50, 0xf1000e51, 0xf2000e52, 0xf3000e53,
		0xf4000e54, 0xf5000e55, 0xf6000e56, 0xf7000e57, 0xf8000e58, 0xf9000e59, 0xfa000e5a, 0xfb000e5b,
		0x96002013, 0x97002014, 0x91002018, 0x92002019, 0x9300201c, 0x9400201d, 0x95002022, 0x85002026,
		0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac,
		0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac,
		0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac,
		0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac, 0x800020ac,
	},
}

// Windows1250 is the Windows 1250 encoding.
var Windows1250 *Charmap = &windows1250

var windows1250 = Charmap{
	name:          "Windows 1250",
	mib:           identifier.Windows1250,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xac}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xe2, 0x80, 0x9a}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xe2, 0x80, 0x9e}}, {3, [3]byte{0xe2, 0x80, 0xa6}},
		{3, [3]byte{0xe2, 0x80, 0xa0}}, {3, [3]byte{0xe2, 0x80, 0xa1}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0xb0}},
		{2, [3]byte{0xc5, 0xa0, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xb9}},
		{2, [3]byte{0xc5, 0x9a, 0x00}}, {2, [3]byte{0xc5, 0xa4, 0x00}},
		{2, [3]byte{0xc5, 0xbd, 0x00}}, {2, [3]byte{0xc5, 0xb9, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0x98}},
		{3, [3]byte{0xe2, 0x80, 0x99}}, {3, [3]byte{0xe2, 0x80, 0x9c}},
		{3, [3]byte{0xe2, 0x80, 0x9d}}, {3, [3]byte{0xe2, 0x80, 0xa2}},
		{3, [3]byte{0xe2, 0x80, 0x93}}, {3, [3]byte{0xe2, 0x80, 0x94}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x84, 0xa2}},
		{2, [3]byte{0xc5, 0xa1, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xba}},
		{2, [3]byte{0xc5, 0x9b, 0x00}}, {2, [3]byte{0xc5, 0xa5, 0x00}},
		{2, [3]byte{0xc5, 0xbe, 0x00}}, {2, [3]byte{0xc5, 0xba, 0x00}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xcb, 0x87, 0x00}},
		{2, [3]byte{0xcb, 0x98, 0x00}}, {2, [3]byte{0xc5, 0x81, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {2, [3]byte{0xc4, 0x84, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xc5, 0x9e, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc5, 0xbb, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xcb, 0x9b, 0x00}}, {2, [3]byte{0xc5, 0x82, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc2, 0xb8, 0x00}}, {2, [3]byte{0xc4, 0x85, 0x00}},
		{2, [3]byte{0xc5, 0x9f, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc4, 0xbd, 0x00}}, {2, [3]byte{0xcb, 0x9d, 0x00}},
		{2, [3]byte{0xc4, 0xbe, 0x00}}, {2, [3]byte{0xc5, 0xbc, 0x00}},
		{2, [3]byte{0xc5, 0x94, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc4, 0x82, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc4, 0xb9, 0x00}},
		{2, [3]byte{0xc4, 0x86, 0x00}}, {2, [3]byte{0xc3, 0x87, 0x00}},
		{2, [3]byte{0xc4, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc4, 0x98, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc4, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc4, 0x8e, 0x00}},
		{2, [3]byte{0xc4, 0x90, 0x00}}, {2, [3]byte{0xc5, 0x83, 0x00}},
		{2, [3]byte{0xc5, 0x87, 0x00}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc5, 0x90, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{2, [3]byte{0xc5, 0x98, 0x00}}, {2, [3]byte{0xc5, 0xae, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc5, 0xb0, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0x9d, 0x00}},
		{2, [3]byte{0xc5, 0xa2, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc5, 0x95, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc4, 0x83, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc4, 0xba, 0x00}},
		{2, [3]byte{0xc4, 0x87, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc4, 0x8d, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc4, 0x99, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc4, 0x9b, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc4, 0x8f, 0x00}},
		{2, [3]byte{0xc4, 0x91, 0x00}}, {2, [3]byte{0xc5, 0x84, 0x00}},
		{2, [3]byte{0xc5, 0x88, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xb4, 0x00}}, {2, [3]byte{0xc5, 0x91, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{2, [3]byte{0xc5, 0x99, 0x00}}, {2, [3]byte{0xc5, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc5, 0xb1, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc3, 0xbd, 0x00}},
		{2, [3]byte{0xc5, 0xa3, 0x00}}, {2, [3]byte{0xcb, 0x99, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa40000a4, 0xa60000a6, 0xa70000a7, 0xa80000a8, 0xa90000a9, 0xab0000ab, 0xac0000ac,
		0xad0000ad, 0xae0000ae, 0xb00000b0, 0xb10000b1, 0xb40000b4, 0xb50000b5, 0xb60000b6, 0xb70000b7,
		0xb80000b8, 0xbb0000bb, 0xc10000c1, 0xc20000c2, 0xc40000c4, 0xc70000c7, 0xc90000c9, 0xcb0000cb,
		0xcd0000cd, 0xce0000ce, 0xd30000d3, 0xd40000d4, 0xd60000d6, 0xd70000d7, 0xda0000da, 0xdc0000dc,
		0xdd0000dd, 0xdf0000df, 0xe10000e1, 0xe20000e2, 0xe40000e4, 0xe70000e7, 0xe90000e9, 0xeb0000eb,
		0xed0000ed, 0xee0000ee, 0xf30000f3, 0xf40000f4, 0xf60000f6, 0xf70000f7, 0xfa0000fa, 0xfc0000fc,
		0xfd0000fd, 0xc3000102, 0xe3000103, 0xa5000104, 0xb9000105, 0xc6000106, 0xe6000107, 0xc800010c,
		0xe800010d, 0xcf00010e, 0xef00010f, 0xd0000110, 0xf0000111, 0xca000118, 0xea000119, 0xcc00011a,
		0xec00011b, 0xc5000139, 0xe500013a, 0xbc00013d, 0xbe00013e, 0xa3000141, 0xb3000142, 0xd1000143,
		0xf1000144, 0xd2000147, 0xf2000148, 0xd5000150, 0xf5000151, 0xc0000154, 0xe0000155, 0xd8000158,
		0xf8000159, 0x8c00015a, 0x9c00015b, 0xaa00015e, 0xba00015f, 0x8a000160, 0x9a000161, 0xde000162,
		0xfe000163, 0x8d000164, 0x9d000165, 0xd900016e, 0xf900016f, 0xdb000170, 0xfb000171, 0x8f000179,
		0x9f00017a, 0xaf00017b, 0xbf00017c, 0x8e00017d, 0x9e00017e, 0xa10002c7, 0xa20002d8, 0xff0002d9,
		0xb20002db, 0xbd0002dd, 0x96002013, 0x97002014, 0x91002018, 0x92002019, 0x8200201a, 0x9300201c,
		0x9400201d, 0x8400201e, 0x86002020, 0x87002021, 0x95002022, 0x85002026, 0x89002030, 0x8b002039,
		0x9b00203a, 0x800020ac, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122,
	},
}

// Windows1251 is the Windows 1251 encoding.
var Windows1251 *Charmap = &windows1251

var windows1251 = Charmap{
	name:          "Windows 1251",
	mib:           identifier.Windows1251,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{2, [3]byte{0xd0, 0x82, 0x00}}, {2, [3]byte{0xd0, 0x83, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x9a}}, {2, [3]byte{0xd1, 0x93, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x9e}}, {3, [3]byte{0xe2, 0x80, 0xa6}},
		{3, [3]byte{0xe2, 0x80, 0xa0}}, {3, [3]byte{0xe2, 0x80, 0xa1}},
		{3, [3]byte{0xe2, 0x82, 0xac}}, {3, [3]byte{0xe2, 0x80, 0xb0}},
		{2, [3]byte{0xd0, 0x89, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xb9}},
		{2, [3]byte{0xd0, 0x8a, 0x00}}, {2, [3]byte{0xd0, 0x8c, 0x00}},
		{2, [3]byte{0xd0, 0x8b, 0x00}}, {2, [3]byte{0xd0, 0x8f, 0x00}},
		{2, [3]byte{0xd1, 0x92, 0x00}}, {3, [3]byte{0xe2, 0x80, 0x98}},
		{3, [3]byte{0xe2, 0x80, 0x99}}, {3, [3]byte{0xe2, 0x80, 0x9c}},
		{3, [3]byte{0xe2, 0x80, 0x9d}}, {3, [3]byte{0xe2, 0x80, 0xa2}},
		{3, [3]byte{0xe2, 0x80, 0x93}}, {3, [3]byte{0xe2, 0x80, 0x94}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x84, 0xa2}},
		{2, [3]byte{0xd1, 0x99, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xba}},
		{2, [3]byte{0xd1, 0x9a, 0x00}}, {2, [3]byte{0xd1, 0x9c, 0x00}},
		{2, [3]byte{0xd1, 0x9b, 0x00}}, {2, [3]byte{0xd1, 0x9f, 0x00}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xd0, 0x8e, 0x00}},
		{2, [3]byte{0xd1, 0x9e, 0x00}}, {2, [3]byte{0xd0, 0x88, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {2, [3]byte{0xd2, 0x90, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xd0, 0x81, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xd0, 0x84, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xd0, 0x87, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xd0, 0x86, 0x00}}, {2, [3]byte{0xd1, 0x96, 0x00}},
		{2, [3]byte{0xd2, 0x91, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xd1, 0x91, 0x00}}, {3, [3]byte{0xe2, 0x84, 0x96}},
		{2, [3]byte{0xd1, 0x94, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xd1, 0x98, 0x00}}, {2, [3]byte{0xd0, 0x85, 0x00}},
		{2, [3]byte{0xd1, 0x95, 0x00}}, {2, [3]byte{0xd1, 0x97, 0x00}},
		{2, [3]byte{0xd0, 0x90, 0x00}}, {2, [3]byte{0xd0, 0x91, 0x00}},
		{2, [3]byte{0xd0, 0x92, 0x00}}, {2, [3]byte{0xd0, 0x93, 0x00}},
		{2, [3]byte{0xd0, 0x94, 0x00}}, {2, [3]byte{0xd0, 0x95, 0x00}},
		{2, [3]byte{0xd0, 0x96, 0x00}}, {2, [3]byte{0xd0, 0x97, 0x00}},
		{2, [3]byte{0xd0, 0x98, 0x00}}, {2, [3]byte{0xd0, 0x99, 0x00}},
		{2, [3]byte{0xd0, 0x9a, 0x00}}, {2, [3]byte{0xd0, 0x9b, 0x00}},
		{2, [3]byte{0xd0, 0x9c, 0x00}}, {2, [3]byte{0xd0, 0x9d, 0x00}},
		{2, [3]byte{0xd0, 0x9e, 0x00}}, {2, [3]byte{0xd0, 0x9f, 0x00}},
		{2, [3]byte{0xd0, 0xa0, 0x00}}, {2, [3]byte{0xd0, 0xa1, 0x00}},
		{2, [3]byte{0xd0, 0xa2, 0x00}}, {2, [3]byte{0xd0, 0xa3, 0x00}},
		{2, [3]byte{0xd0, 0xa4, 0x00}}, {2, [3]byte{0xd0, 0xa5, 0x00}},
		{2, [3]byte{0xd0, 0xa6, 0x00}}, {2, [3]byte{0xd0, 0xa7, 0x00}},
		{2, [3]byte{0xd0, 0xa8, 0x00}}, {2, [3]byte{0xd0, 0xa9, 0x00}},
		{2, [3]byte{0xd0, 0xaa, 0x00}}, {2, [3]byte{0xd0, 0xab, 0x00}},
		{2, [3]byte{0xd0, 0xac, 0x00}}, {2, [3]byte{0xd0, 0xad, 0x00}},
		{2, [3]byte{0xd0, 0xae, 0x00}}, {2, [3]byte{0xd0, 0xaf, 0x00}},
		{2, [3]byte{0xd0, 0xb0, 0x00}}, {2, [3]byte{0xd0, 0xb1, 0x00}},
		{2, [3]byte{0xd0, 0xb2, 0x00}}, {2, [3]byte{0xd0, 0xb3, 0x00}},
		{2, [3]byte{0xd0, 0xb4, 0x00}}, {2, [3]byte{0xd0, 0xb5, 0x00}},
		{2, [3]byte{0xd0, 0xb6, 0x00}}, {2, [3]byte{0xd0, 0xb7, 0x00}},
		{2, [3]byte{0xd0, 0xb8, 0x00}}, {2, [3]byte{0xd0, 0xb9, 0x00}},
		{2, [3]byte{0xd0, 0xba, 0x00}}, {2, [3]byte{0xd0, 0xbb, 0x00}},
		{2, [3]byte{0xd0, 0xbc, 0x00}}, {2, [3]byte{0xd0, 0xbd, 0x00}},
		{2, [3]byte{0xd0, 0xbe, 0x00}}, {2, [3]byte{0xd0, 0xbf, 0x00}},
		{2, [3]byte{0xd1, 0x80, 0x00}}, {2, [3]byte{0xd1, 0x81, 0x00}},
		{2, [3]byte{0xd1, 0x82, 0x00}}, {2, [3]byte{0xd1, 0x83, 0x00}},
		{2, [3]byte{0xd1, 0x84, 0x00}}, {2, [3]byte{0xd1, 0x85, 0x00}},
		{2, [3]byte{0xd1, 0x86, 0x00}}, {2, [3]byte{0xd1, 0x87, 0x00}},
		{2, [3]byte{0xd1, 0x88, 0x00}}, {2, [3]byte{0xd1, 0x89, 0x00}},
		{2, [3]byte{0xd1, 0x8a, 0x00}}, {2, [3]byte{0xd1, 0x8b, 0x00}},
		{2, [3]byte{0xd1, 0x8c, 0x00}}, {2, [3]byte{0xd1, 0x8d, 0x00}},
		{2, [3]byte{0xd1, 0x8e, 0x00}}, {2, [3]byte{0xd1, 0x8f, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa40000a4, 0xa60000a6, 0xa70000a7, 0xa90000a9, 0xab0000ab, 0xac0000ac, 0xad0000ad,
		0xae0000ae, 0xb00000b0, 0xb10000b1, 0xb50000b5, 0xb60000b6, 0xb70000b7, 0xbb0000bb, 0xa8000401,
		0x80000402, 0x81000403, 0xaa000404, 0xbd000405, 0xb2000406, 0xaf000407, 0xa3000408, 0x8a000409,
		0x8c00040a, 0x8e00040b, 0x8d00040c, 0xa100040e, 0x8f00040f, 0xc0000410, 0xc1000411, 0xc2000412,
		0xc3000413, 0xc4000414, 0xc5000415, 0xc6000416, 0xc7000417, 0xc8000418, 0xc9000419, 0xca00041a,
		0xcb00041b, 0xcc00041c, 0xcd00041d, 0xce00041e, 0xcf00041f, 0xd0000420, 0xd1000421, 0xd2000422,
		0xd3000423, 0xd4000424, 0xd5000425, 0xd6000426, 0xd7000427, 0xd8000428, 0xd9000429, 0xda00042a,
		0xdb00042b, 0xdc00042c, 0xdd00042d, 0xde00042e, 0xdf00042f, 0xe0000430, 0xe1000431, 0xe2000432,
		0xe3000433, 0xe4000434, 0xe5000435, 0xe6000436, 0xe7000437, 0xe8000438, 0xe9000439, 0xea00043a,
		0xeb00043b, 0xec00043c, 0xed00043d, 0xee00043e, 0xef00043f, 0xf0000440, 0xf1000441, 0xf2000442,
		0xf3000443, 0xf4000444, 0xf5000445, 0xf6000446, 0xf7000447, 0xf8000448, 0xf9000449, 0xfa00044a,
		0xfb00044b, 0xfc00044c, 0xfd00044d, 0xfe00044e, 0xff00044f, 0xb8000451, 0x90000452, 0x83000453,
		0xba000454, 0xbe000455, 0xb3000456, 0xbf000457, 0xbc000458, 0x9a000459, 0x9c00045a, 0x9e00045b,
		0x9d00045c, 0xa200045e, 0x9f00045f, 0xa5000490, 0xb4000491, 0x96002013, 0x97002014, 0x91002018,
		0x92002019, 0x8200201a, 0x9300201c, 0x9400201d, 0x8400201e, 0x86002020, 0x87002021, 0x95002022,
		0x85002026, 0x89002030, 0x8b002039, 0x9b00203a, 0x880020ac, 0xb9002116, 0x99002122, 0x99002122,
	},
}

// Windows1252 is the Windows 1252 encoding.
var Windows1252 *Charmap = &windows1252

var windows1252 = Charmap{
	name:          "Windows 1252",
	mib:           identifier.Windows1252,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xac}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xe2, 0x80, 0x9a}}, {2, [3]byte{0xc6, 0x92, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x9e}}, {3, [3]byte{0xe2, 0x80, 0xa6}},
		{3, [3]byte{0xe2, 0x80, 0xa0}}, {3, [3]byte{0xe2, 0x80, 0xa1}},
		{2, [3]byte{0xcb, 0x86, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xb0}},
		{2, [3]byte{0xc5, 0xa0, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xb9}},
		{2, [3]byte{0xc5, 0x92, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc5, 0xbd, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0x98}},
		{3, [3]byte{0xe2, 0x80, 0x99}}, {3, [3]byte{0xe2, 0x80, 0x9c}},
		{3, [3]byte{0xe2, 0x80, 0x9d}}, {3, [3]byte{0xe2, 0x80, 0xa2}},
		{3, [3]byte{0xe2, 0x80, 0x93}}, {3, [3]byte{0xe2, 0x80, 0x94}},
		{2, [3]byte{0xcb, 0x9c, 0x00}}, {3, [3]byte{0xe2, 0x84, 0xa2}},
		{2, [3]byte{0xc5, 0xa1, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xba}},
		{2, [3]byte{0xc5, 0x93, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc5, 0xbe, 0x00}}, {2, [3]byte{0xc5, 0xb8, 0x00}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xc2, 0xa1, 0x00}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {2, [3]byte{0xc2, 0xa5, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc2, 0xaf, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc2, 0xb3, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc2, 0xb8, 0x00}}, {2, [3]byte{0xc2, 0xb9, 0x00}},
		{2, [3]byte{0xc2, 0xba, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbe, 0x00}}, {2, [3]byte{0xc2, 0xbf, 0x00}},
		{2, [3]byte{0xc3, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc3, 0x83, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc3, 0x87, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc3, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc3, 0x8f, 0x00}},
		{2, [3]byte{0xc3, 0x90, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc3, 0x92, 0x00}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{2, [3]byte{0xc3, 0x98, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc3, 0x9d, 0x00}},
		{2, [3]byte{0xc3, 0x9e, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc3, 0xa0, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc3, 0xa3, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa5, 0x00}},
		{2, [3]byte{0xc3, 0xa6, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xac, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc3, 0xb0, 0x00}}, {2, [3]byte{0xc3, 0xb1, 0x00}},
		{2, [3]byte{0xc3, 0xb2, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xb4, 0x00}}, {2, [3]byte{0xc3, 0xb5, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{2, [3]byte{0xc3, 0xb8, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc3, 0xbd, 0x00}},
		{2, [3]byte{0xc3, 0xbe, 0x00}}, {2, [3]byte{0xc3, 0xbf, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa10000a1, 0xa20000a2, 0xa30000a3, 0xa40000a4, 0xa50000a5, 0xa60000a6, 0xa70000a7,
		0xa80000a8, 0xa90000a9, 0xaa0000aa, 0xab0000ab, 0xac0000ac, 0xad0000ad, 0xae0000ae, 0xaf0000af,
		0xb00000b0, 0xb10000b1, 0xb20000b2, 0xb30000b3, 0xb40000b4, 0xb50000b5, 0xb60000b6, 0xb70000b7,
		0xb80000b8, 0xb90000b9, 0xba0000ba, 0xbb0000bb, 0xbc0000bc, 0xbd0000bd, 0xbe0000be, 0xbf0000bf,
		0xc00000c0, 0xc10000c1, 0xc20000c2, 0xc30000c3, 0xc40000c4, 0xc50000c5, 0xc60000c6, 0xc70000c7,
		0xc80000c8, 0xc90000c9, 0xca0000ca, 0xcb0000cb, 0xcc0000cc, 0xcd0000cd, 0xce0000ce, 0xcf0000cf,
		0xd00000d0, 0xd10000d1, 0xd20000d2, 0xd30000d3, 0xd40000d4, 0xd50000d5, 0xd60000d6, 0xd70000d7,
		0xd80000d8, 0xd90000d9, 0xda0000da, 0xdb0000db, 0xdc0000dc, 0xdd0000dd, 0xde0000de, 0xdf0000df,
		0xe00000e0, 0xe10000e1, 0xe20000e2, 0xe30000e3, 0xe40000e4, 0xe50000e5, 0xe60000e6, 0xe70000e7,
		0xe80000e8, 0xe90000e9, 0xea0000ea, 0xeb0000eb, 0xec0000ec, 0xed0000ed, 0xee0000ee, 0xef0000ef,
		0xf00000f0, 0xf10000f1, 0xf20000f2, 0xf30000f3, 0xf40000f4, 0xf50000f5, 0xf60000f6, 0xf70000f7,
		0xf80000f8, 0xf90000f9, 0xfa0000fa, 0xfb0000fb, 0xfc0000fc, 0xfd0000fd, 0xfe0000fe, 0xff0000ff,
		0x8c000152, 0x9c000153, 0x8a000160, 0x9a000161, 0x9f000178, 0x8e00017d, 0x9e00017e, 0x83000192,
		0x880002c6, 0x980002dc, 0x96002013, 0x97002014, 0x91002018, 0x92002019, 0x8200201a, 0x9300201c,
		0x9400201d, 0x8400201e, 0x86002020, 0x87002021, 0x95002022, 0x85002026, 0x89002030, 0x8b002039,
		0x9b00203a, 0x800020ac, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122,
	},
}

// Windows1253 is the Windows 1253 encoding.
var Windows1253 *Charmap = &windows1253

var windows1253 = Charmap{
	name:          "Windows 1253",
	mib:           identifier.Windows1253,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xac}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xe2, 0x80, 0x9a}}, {2, [3]byte{0xc6, 0x92, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x9e}}, {3, [3]byte{0xe2, 0x80, 0xa6}},
		{3, [3]byte{0xe2, 0x80, 0xa0}}, {3, [3]byte{0xe2, 0x80, 0xa1}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0xb0}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0xb9}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0x98}},
		{3, [3]byte{0xe2, 0x80, 0x99}}, {3, [3]byte{0xe2, 0x80, 0x9c}},
		{3, [3]byte{0xe2, 0x80, 0x9d}}, {3, [3]byte{0xe2, 0x80, 0xa2}},
		{3, [3]byte{0xe2, 0x80, 0x93}}, {3, [3]byte{0xe2, 0x80, 0x94}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x84, 0xa2}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0xba}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xce, 0x85, 0x00}},
		{2, [3]byte{0xce, 0x86, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {2, [3]byte{0xc2, 0xa5, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {3, [3]byte{0xe2, 0x80, 0x95}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc2, 0xb3, 0x00}},
		{2, [3]byte{0xce, 0x84, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xce, 0x88, 0x00}}, {2, [3]byte{0xce, 0x89, 0x00}},
		{2, [3]byte{0xce, 0x8a, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xce, 0x8c, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xce, 0x8e, 0x00}}, {2, [3]byte{0xce, 0x8f, 0x00}},
		{2, [3]byte{0xce, 0x90, 0x00}}, {2, [3]byte{0xce, 0x91, 0x00}},
		{2, [3]byte{0xce, 0x92, 0x00}}, {2, [3]byte{0xce, 0x93, 0x00}},
		{2, [3]byte{0xce, 0x94, 0x00}}, {2, [3]byte{0xce, 0x95, 0x00}},
		{2, [3]byte{0xce, 0x96, 0x00}}, {2, [3]byte{0xce, 0x97, 0x00}},
		{2, [3]byte{0xce, 0x98, 0x00}}, {2, [3]byte{0xce, 0x99, 0x00}},
		{2, [3]byte{0xce, 0x9a, 0x00}}, {2, [3]byte{0xce, 0x9b, 0x00}},
		{2, [3]byte{0xce, 0x9c, 0x00}}, {2, [3]byte{0xce, 0x9d, 0x00}},
		{2, [3]byte{0xce, 0x9e, 0x00}}, {2, [3]byte{0xce, 0x9f, 0x00}},
		{2, [3]byte{0xce, 0xa0, 0x00}}, {2, [3]byte{0xce, 0xa1, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {2, [3]byte{0xce, 0xa3, 0x00}},
		{2, [3]byte{0xce, 0xa4, 0x00}}, {2, [3]byte{0xce, 0xa5, 0x00}},
		{2, [3]byte{0xce, 0xa6, 0x00}}, {2, [3]byte{0xce, 0xa7, 0x00}},
		{2, [3]byte{0xce, 0xa8, 0x00}}, {2, [3]byte{0xce, 0xa9, 0x00}},
		{2, [3]byte{0xce, 0xaa, 0x00}}, {2, [3]byte{0xce, 0xab, 0x00}},
		{2, [3]byte{0xce, 0xac, 0x00}}, {2, [3]byte{0xce, 0xad, 0x00}},
		{2, [3]byte{0xce, 0xae, 0x00}}, {2, [3]byte{0xce, 0xaf, 0x00}},
		{2, [3]byte{0xce, 0xb0, 0x00}}, {2, [3]byte{0xce, 0xb1, 0x00}},
		{2, [3]byte{0xce, 0xb2, 0x00}}, {2, [3]byte{0xce, 0xb3, 0x00}},
		{2, [3]byte{0xce, 0xb4, 0x00}}, {2, [3]byte{0xce, 0xb5, 0x00}},
		{2, [3]byte{0xce, 0xb6, 0x00}}, {2, [3]byte{0xce, 0xb7, 0x00}},
		{2, [3]byte{0xce, 0xb8, 0x00}}, {2, [3]byte{0xce, 0xb9, 0x00}},
		{2, [3]byte{0xce, 0xba, 0x00}}, {2, [3]byte{0xce, 0xbb, 0x00}},
		{2, [3]byte{0xce, 0xbc, 0x00}}, {2, [3]byte{0xce, 0xbd, 0x00}},
		{2, [3]byte{0xce, 0xbe, 0x00}}, {2, [3]byte{0xce, 0xbf, 0x00}},
		{2, [3]byte{0xcf, 0x80, 0x00}}, {2, [3]byte{0xcf, 0x81, 0x00}},
		{2, [3]byte{0xcf, 0x82, 0x00}}, {2, [3]byte{0xcf, 0x83, 0x00}},
		{2, [3]byte{0xcf, 0x84, 0x00}}, {2, [3]byte{0xcf, 0x85, 0x00}},
		{2, [3]byte{0xcf, 0x86, 0x00}}, {2, [3]byte{0xcf, 0x87, 0x00}},
		{2, [3]byte{0xcf, 0x88, 0x00}}, {2, [3]byte{0xcf, 0x89, 0x00}},
		{2, [3]byte{0xcf, 0x8a, 0x00}}, {2, [3]byte{0xcf, 0x8b, 0x00}},
		{2, [3]byte{0xcf, 0x8c, 0x00}}, {2, [3]byte{0xcf, 0x8d, 0x00}},
		{2, [3]byte{0xcf, 0x8e, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa30000a3, 0xa40000a4, 0xa50000a5, 0xa60000a6, 0xa70000a7, 0xa80000a8, 0xa90000a9,
		0xab0000ab, 0xac0000ac, 0xad0000ad, 0xae0000ae, 0xb00000b0, 0xb10000b1, 0xb20000b2, 0xb30000b3,
		0xb50000b5, 0xb60000b6, 0xb70000b7, 0xbb0000bb, 0xbd0000bd, 0x83000192, 0xb4000384, 0xa1000385,
		0xa2000386, 0xb8000388, 0xb9000389, 0xba00038a, 0xbc00038c, 0xbe00038e, 0xbf00038f, 0xc0000390,
		0xc1000391, 0xc2000392, 0xc3000393, 0xc4000394, 0xc5000395, 0xc6000396, 0xc7000397, 0xc8000398,
		0xc9000399, 0xca00039a, 0xcb00039b, 0xcc00039c, 0xcd00039d, 0xce00039e, 0xcf00039f, 0xd00003a0,
		0xd10003a1, 0xd30003a3, 0xd40003a4, 0xd50003a5, 0xd60003a6, 0xd70003a7, 0xd80003a8, 0xd90003a9,
		0xda0003aa, 0xdb0003ab, 0xdc0003ac, 0xdd0003ad, 0xde0003ae, 0xdf0003af, 0xe00003b0, 0xe10003b1,
		0xe20003b2, 0xe30003b3, 0xe40003b4, 0xe50003b5, 0xe60003b6, 0xe70003b7, 0xe80003b8, 0xe90003b9,
		0xea0003ba, 0xeb0003bb, 0xec0003bc, 0xed0003bd, 0xee0003be, 0xef0003bf, 0xf00003c0, 0xf10003c1,
		0xf20003c2, 0xf30003c3, 0xf40003c4, 0xf50003c5, 0xf60003c6, 0xf70003c7, 0xf80003c8, 0xf90003c9,
		0xfa0003ca, 0xfb0003cb, 0xfc0003cc, 0xfd0003cd, 0xfe0003ce, 0x96002013, 0x97002014, 0xaf002015,
		0x91002018, 0x92002019, 0x8200201a, 0x9300201c, 0x9400201d, 0x8400201e, 0x86002020, 0x87002021,
		0x95002022, 0x85002026, 0x89002030, 0x8b002039, 0x9b00203a, 0x800020ac, 0x99002122, 0x99002122,
		0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122,
		0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122,
	},
}

// Windows1254 is the Windows 1254 encoding.
var Windows1254 *Charmap = &windows1254

var windows1254 = Charmap{
	name:          "Windows 1254",
	mib:           identifier.Windows1254,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xac}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xe2, 0x80, 0x9a}}, {2, [3]byte{0xc6, 0x92, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x9e}}, {3, [3]byte{0xe2, 0x80, 0xa6}},
		{3, [3]byte{0xe2, 0x80, 0xa0}}, {3, [3]byte{0xe2, 0x80, 0xa1}},
		{2, [3]byte{0xcb, 0x86, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xb0}},
		{2, [3]byte{0xc5, 0xa0, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xb9}},
		{2, [3]byte{0xc5, 0x92, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0x98}},
		{3, [3]byte{0xe2, 0x80, 0x99}}, {3, [3]byte{0xe2, 0x80, 0x9c}},
		{3, [3]byte{0xe2, 0x80, 0x9d}}, {3, [3]byte{0xe2, 0x80, 0xa2}},
		{3, [3]byte{0xe2, 0x80, 0x93}}, {3, [3]byte{0xe2, 0x80, 0x94}},
		{2, [3]byte{0xcb, 0x9c, 0x00}}, {3, [3]byte{0xe2, 0x84, 0xa2}},
		{2, [3]byte{0xc5, 0xa1, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xba}},
		{2, [3]byte{0xc5, 0x93, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {2, [3]byte{0xc5, 0xb8, 0x00}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xc2, 0xa1, 0x00}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {2, [3]byte{0xc2, 0xa5, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc2, 0xaf, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc2, 0xb3, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc2, 0xb8, 0x00}}, {2, [3]byte{0xc2, 0xb9, 0x00}},
		{2, [3]byte{0xc2, 0xba, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbe, 0x00}}, {2, [3]byte{0xc2, 0xbf, 0x00}},
		{2, [3]byte{0xc3, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc3, 0x83, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc3, 0x87, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xc3, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc3, 0x8f, 0x00}},
		{2, [3]byte{0xc4, 0x9e, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xc3, 0x92, 0x00}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{2, [3]byte{0xc3, 0x98, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc4, 0xb0, 0x00}},
		{2, [3]byte{0xc5, 0x9e, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc3, 0xa0, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc3, 0xa3, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa5, 0x00}},
		{2, [3]byte{0xc3, 0xa6, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xac, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc4, 0x9f, 0x00}}, {2, [3]byte{0xc3, 0xb1, 0x00}},
		{2, [3]byte{0xc3, 0xb2, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xb4, 0x00}}, {2, [3]byte{0xc3, 0xb5, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{2, [3]byte{0xc3, 0xb8, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc4, 0xb1, 0x00}},
		{2, [3]byte{0xc5, 0x9f, 0x00}}, {2, [3]byte{0xc3, 0xbf, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa10000a1, 0xa20000a2, 0xa30000a3, 0xa40000a4, 0xa50000a5, 0xa60000a6, 0xa70000a7,
		0xa80000a8, 0xa90000a9, 0xaa0000aa, 0xab0000ab, 0xac0000ac, 0xad0000ad, 0xae0000ae, 0xaf0000af,
		0xb00000b0, 0xb10000b1, 0xb20000b2, 0xb30000b3, 0xb40000b4, 0xb50000b5, 0xb60000b6, 0xb70000b7,
		0xb80000b8, 0xb90000b9, 0xba0000ba, 0xbb0000bb, 0xbc0000bc, 0xbd0000bd, 0xbe0000be, 0xbf0000bf,
		0xc00000c0, 0xc10000c1, 0xc20000c2, 0xc30000c3, 0xc40000c4, 0xc50000c5, 0xc60000c6, 0xc70000c7,
		0xc80000c8, 0xc90000c9, 0xca0000ca, 0xcb0000cb, 0xcc0000cc, 0xcd0000cd, 0xce0000ce, 0xcf0000cf,
		0xd10000d1, 0xd20000d2, 0xd30000d3, 0xd40000d4, 0xd50000d5, 0xd60000d6, 0xd70000d7, 0xd80000d8,
		0xd90000d9, 0xda0000da, 0xdb0000db, 0xdc0000dc, 0xdf0000df, 0xe00000e0, 0xe10000e1, 0xe20000e2,
		0xe30000e3, 0xe40000e4, 0xe50000e5, 0xe60000e6, 0xe70000e7, 0xe80000e8, 0xe90000e9, 0xea0000ea,
		0xeb0000eb, 0xec0000ec, 0xed0000ed, 0xee0000ee, 0xef0000ef, 0xf10000f1, 0xf20000f2, 0xf30000f3,
		0xf40000f4, 0xf50000f5, 0xf60000f6, 0xf70000f7, 0xf80000f8, 0xf90000f9, 0xfa0000fa, 0xfb0000fb,
		0xfc0000fc, 0xff0000ff, 0xd000011e, 0xf000011f, 0xdd000130, 0xfd000131, 0x8c000152, 0x9c000153,
		0xde00015e, 0xfe00015f, 0x8a000160, 0x9a000161, 0x9f000178, 0x83000192, 0x880002c6, 0x980002dc,
		0x96002013, 0x97002014, 0x91002018, 0x92002019, 0x8200201a, 0x9300201c, 0x9400201d, 0x8400201e,
		0x86002020, 0x87002021, 0x95002022, 0x85002026, 0x89002030, 0x8b002039, 0x9b00203a, 0x800020ac,
		0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122,
	},
}

// Windows1255 is the Windows 1255 encoding.
var Windows1255 *Charmap = &windows1255

var windows1255 = Charmap{
	name:          "Windows 1255",
	mib:           identifier.Windows1255,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xac}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xe2, 0x80, 0x9a}}, {2, [3]byte{0xc6, 0x92, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x9e}}, {3, [3]byte{0xe2, 0x80, 0xa6}},
		{3, [3]byte{0xe2, 0x80, 0xa0}}, {3, [3]byte{0xe2, 0x80, 0xa1}},
		{2, [3]byte{0xcb, 0x86, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xb0}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0xb9}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0x98}},
		{3, [3]byte{0xe2, 0x80, 0x99}}, {3, [3]byte{0xe2, 0x80, 0x9c}},
		{3, [3]byte{0xe2, 0x80, 0x9d}}, {3, [3]byte{0xe2, 0x80, 0xa2}},
		{3, [3]byte{0xe2, 0x80, 0x93}}, {3, [3]byte{0xe2, 0x80, 0x94}},
		{2, [3]byte{0xcb, 0x9c, 0x00}}, {3, [3]byte{0xe2, 0x84, 0xa2}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0xba}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xc2, 0xa1, 0x00}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xaa}}, {2, [3]byte{0xc2, 0xa5, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0x97, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc2, 0xaf, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc2, 0xb3, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc2, 0xb8, 0x00}}, {2, [3]byte{0xc2, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xb7, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbe, 0x00}}, {2, [3]byte{0xc2, 0xbf, 0x00}},
		{2, [3]byte{0xd6, 0xb0, 0x00}}, {2, [3]byte{0xd6, 0xb1, 0x00}},
		{2, [3]byte{0xd6, 0xb2, 0x00}}, {2, [3]byte{0xd6, 0xb3, 0x00}},
		{2, [3]byte{0xd6, 0xb4, 0x00}}, {2, [3]byte{0xd6, 0xb5, 0x00}},
		{2, [3]byte{0xd6, 0xb6, 0x00}}, {2, [3]byte{0xd6, 0xb7, 0x00}},
		{2, [3]byte{0xd6, 0xb8, 0x00}}, {2, [3]byte{0xd6, 0xb9, 0x00}},
		{2, [3]byte{0xd6, 0xba, 0x00}}, {2, [3]byte{0xd6, 0xbb, 0x00}},
		{2, [3]byte{0xd6, 0xbc, 0x00}}, {2, [3]byte{0xd6, 0xbd, 0x00}},
		{2, [3]byte{0xd6, 0xbe, 0x00}}, {2, [3]byte{0xd6, 0xbf, 0x00}},
		{2, [3]byte{0xd7, 0x80, 0x00}}, {2, [3]byte{0xd7, 0x81, 0x00}},
		{2, [3]byte{0xd7, 0x82, 0x00}}, {2, [3]byte{0xd7, 0x83, 0x00}},
		{2, [3]byte{0xd7, 0xb0, 0x00}}, {2, [3]byte{0xd7, 0xb1, 0x00}},
		{2, [3]byte{0xd7, 0xb2, 0x00}}, {2, [3]byte{0xd7, 0xb3, 0x00}},
		{2, [3]byte{0xd7, 0xb4, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xd7, 0x90, 0x00}}, {2, [3]byte{0xd7, 0x91, 0x00}},
		{2, [3]byte{0xd7, 0x92, 0x00}}, {2, [3]byte{0xd7, 0x93, 0x00}},
		{2, [3]byte{0xd7, 0x94, 0x00}}, {2, [3]byte{0xd7, 0x95, 0x00}},
		{2, [3]byte{0xd7, 0x96, 0x00}}, {2, [3]byte{0xd7, 0x97, 0x00}},
		{2, [3]byte{0xd7, 0x98, 0x00}}, {2, [3]byte{0xd7, 0x99, 0x00}},
		{2, [3]byte{0xd7, 0x9a, 0x00}}, {2, [3]byte{0xd7, 0x9b, 0x00}},
		{2, [3]byte{0xd7, 0x9c, 0x00}}, {2, [3]byte{0xd7, 0x9d, 0x00}},
		{2, [3]byte{0xd7, 0x9e, 0x00}}, {2, [3]byte{0xd7, 0x9f, 0x00}},
		{2, [3]byte{0xd7, 0xa0, 0x00}}, {2, [3]byte{0xd7, 0xa1, 0x00}},
		{2, [3]byte{0xd7, 0xa2, 0x00}}, {2, [3]byte{0xd7, 0xa3, 0x00}},
		{2, [3]byte{0xd7, 0xa4, 0x00}}, {2, [3]byte{0xd7, 0xa5, 0x00}},
		{2, [3]byte{0xd7, 0xa6, 0x00}}, {2, [3]byte{0xd7, 0xa7, 0x00}},
		{2, [3]byte{0xd7, 0xa8, 0x00}}, {2, [3]byte{0xd7, 0xa9, 0x00}},
		{2, [3]byte{0xd7, 0xaa, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0x8e}},
		{3, [3]byte{0xe2, 0x80, 0x8f}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa10000a1, 0xa20000a2, 0xa30000a3, 0xa50000a5, 0xa60000a6, 0xa70000a7, 0xa80000a8,
		0xa90000a9, 0xab0000ab, 0xac0000ac, 0xad0000ad, 0xae0000ae, 0xaf0000af, 0xb00000b0, 0xb10000b1,
		0xb20000b2, 0xb30000b3, 0xb40000b4, 0xb50000b5, 0xb60000b6, 0xb70000b7, 0xb80000b8, 0xb90000b9,
		0xbb0000bb, 0xbc0000bc, 0xbd0000bd, 0xbe0000be, 0xbf0000bf, 0xaa0000d7, 0xba0000f7, 0x83000192,
		0x880002c6, 0x980002dc, 0xc00005b0, 0xc10005b1, 0xc20005b2, 0xc30005b3, 0xc40005b4, 0xc50005b5,
		0xc60005b6, 0xc70005b7, 0xc80005b8, 0xc90005b9, 0xca0005ba, 0xcb0005bb, 0xcc0005bc, 0xcd0005bd,
		0xce0005be, 0xcf0005bf, 0xd00005c0, 0xd10005c1, 0xd20005c2, 0xd30005c3, 0xe00005d0, 0xe10005d1,
		0xe20005d2, 0xe30005d3, 0xe40005d4, 0xe50005d5, 0xe60005d6, 0xe70005d7, 0xe80005d8, 0xe90005d9,
		0xea0005da, 0xeb0005db, 0xec0005dc, 0xed0005dd, 0xee0005de, 0xef0005df, 0xf00005e0, 0xf10005e1,
		0xf20005e2, 0xf30005e3, 0xf40005e4, 0xf50005e5, 0xf60005e6, 0xf70005e7, 0xf80005e8, 0xf90005e9,
		0xfa0005ea, 0xd40005f0, 0xd50005f1, 0xd60005f2, 0xd70005f3, 0xd80005f4, 0xfd00200e, 0xfe00200f,
		0x96002013, 0x97002014, 0x91002018, 0x92002019, 0x8200201a, 0x9300201c, 0x9400201d, 0x8400201e,
		0x86002020, 0x87002021, 0x95002022, 0x85002026, 0x89002030, 0x8b002039, 0x9b00203a, 0xa40020aa,
		0x800020ac, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122,
		0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122,
		0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122,
	},
}

// Windows1256 is the Windows 1256 encoding.
var Windows1256 *Charmap = &windows1256

var windows1256 = Charmap{
	name:          "Windows 1256",
	mib:           identifier.Windows1256,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xac}}, {2, [3]byte{0xd9, 0xbe, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x9a}}, {2, [3]byte{0xc6, 0x92, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x9e}}, {3, [3]byte{0xe2, 0x80, 0xa6}},
		{3, [3]byte{0xe2, 0x80, 0xa0}}, {3, [3]byte{0xe2, 0x80, 0xa1}},
		{2, [3]byte{0xcb, 0x86, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xb0}},
		{2, [3]byte{0xd9, 0xb9, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xb9}},
		{2, [3]byte{0xc5, 0x92, 0x00}}, {2, [3]byte{0xda, 0x86, 0x00}},
		{2, [3]byte{0xda, 0x98, 0x00}}, {2, [3]byte{0xda, 0x88, 0x00}},
		{2, [3]byte{0xda, 0xaf, 0x00}}, {3, [3]byte{0xe2, 0x80, 0x98}},
		{3, [3]byte{0xe2, 0x80, 0x99}}, {3, [3]byte{0xe2, 0x80, 0x9c}},
		{3, [3]byte{0xe2, 0x80, 0x9d}}, {3, [3]byte{0xe2, 0x80, 0xa2}},
		{3, [3]byte{0xe2, 0x80, 0x93}}, {3, [3]byte{0xe2, 0x80, 0x94}},
		{2, [3]byte{0xda, 0xa9, 0x00}}, {3, [3]byte{0xe2, 0x84, 0xa2}},
		{2, [3]byte{0xda, 0x91, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xba}},
		{2, [3]byte{0xc5, 0x93, 0x00}}, {3, [3]byte{0xe2, 0x80, 0x8c}},
		{3, [3]byte{0xe2, 0x80, 0x8d}}, {2, [3]byte{0xda, 0xba, 0x00}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xd8, 0x8c, 0x00}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {2, [3]byte{0xc2, 0xa5, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xda, 0xbe, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc2, 0xaf, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc2, 0xb3, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc2, 0xb8, 0x00}}, {2, [3]byte{0xc2, 0xb9, 0x00}},
		{2, [3]byte{0xd8, 0x9b, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbe, 0x00}}, {2, [3]byte{0xd8, 0x9f, 0x00}},
		{2, [3]byte{0xdb, 0x81, 0x00}}, {2, [3]byte{0xd8, 0xa1, 0x00}},
		{2, [3]byte{0xd8, 0xa2, 0x00}}, {2, [3]byte{0xd8, 0xa3, 0x00}},
		{2, [3]byte{0xd8, 0xa4, 0x00}}, {2, [3]byte{0xd8, 0xa5, 0x00}},
		{2, [3]byte{0xd8, 0xa6, 0x00}}, {2, [3]byte{0xd8, 0xa7, 0x00}},
		{2, [3]byte{0xd8, 0xa8, 0x00}}, {2, [3]byte{0xd8, 0xa9, 0x00}},
		{2, [3]byte{0xd8, 0xaa, 0x00}}, {2, [3]byte{0xd8, 0xab, 0x00}},
		{2, [3]byte{0xd8, 0xac, 0x00}}, {2, [3]byte{0xd8, 0xad, 0x00}},
		{2, [3]byte{0xd8, 0xae, 0x00}}, {2, [3]byte{0xd8, 0xaf, 0x00}},
		{2, [3]byte{0xd8, 0xb0, 0x00}}, {2, [3]byte{0xd8, 0xb1, 0x00}},
		{2, [3]byte{0xd8, 0xb2, 0x00}}, {2, [3]byte{0xd8, 0xb3, 0x00}},
		{2, [3]byte{0xd8, 0xb4, 0x00}}, {2, [3]byte{0xd8, 0xb5, 0x00}},
		{2, [3]byte{0xd8, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{2, [3]byte{0xd8, 0xb7, 0x00}}, {2, [3]byte{0xd8, 0xb8, 0x00}},
		{2, [3]byte{0xd8, 0xb9, 0x00}}, {2, [3]byte{0xd8, 0xba, 0x00}},
		{2, [3]byte{0xd9, 0x80, 0x00}}, {2, [3]byte{0xd9, 0x81, 0x00}},
		{2, [3]byte{0xd9, 0x82, 0x00}}, {2, [3]byte{0xd9, 0x83, 0x00}},
		{2, [3]byte{0xc3, 0xa0, 0x00}}, {2, [3]byte{0xd9, 0x84, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xd9, 0x85, 0x00}},
		{2, [3]byte{0xd9, 0x86, 0x00}}, {2, [3]byte{0xd9, 0x87, 0x00}},
		{2, [3]byte{0xd9, 0x88, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xd9, 0x89, 0x00}}, {2, [3]byte{0xd9, 0x8a, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xd9, 0x8b, 0x00}}, {2, [3]byte{0xd9, 0x8c, 0x00}},
		{2, [3]byte{0xd9, 0x8d, 0x00}}, {2, [3]byte{0xd9, 0x8e, 0x00}},
		{2, [3]byte{0xc3, 0xb4, 0x00}}, {2, [3]byte{0xd9, 0x8f, 0x00}},
		{2, [3]byte{0xd9, 0x90, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{2, [3]byte{0xd9, 0x91, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xd9, 0x92, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {3, [3]byte{0xe2, 0x80, 0x8e}},
		{3, [3]byte{0xe2, 0x80, 0x8f}}, {2, [3]byte{0xdb, 0x92, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa20000a2, 0xa30000a3, 0xa40000a4, 0xa50000a5, 0xa60000a6, 0xa70000a7, 0xa80000a8,
		0xa90000a9, 0xab0000ab, 0xac0000ac, 0xad0000ad, 0xae0000ae, 0xaf0000af, 0xb00000b0, 0xb10000b1,
		0xb20000b2, 0xb30000b3, 0xb40000b4, 0xb50000b5, 0xb60000b6, 0xb70000b7, 0xb80000b8, 0xb90000b9,
		0xbb0000bb, 0xbc0000bc, 0xbd0000bd, 0xbe0000be, 0xd70000d7, 0xe00000e0, 0xe20000e2, 0xe70000e7,
		0xe80000e8, 0xe90000e9, 0xea0000ea, 0xeb0000eb, 0xee0000ee, 0xef0000ef, 0xf40000f4, 0xf70000f7,
		0xf90000f9, 0xfb0000fb, 0xfc0000fc, 0x8c000152, 0x9c000153, 0x83000192, 0x880002c6, 0xa100060c,
		0xba00061b, 0xbf00061f, 0xc1000621, 0xc2000622, 0xc3000623, 0xc4000624, 0xc5000625, 0xc6000626,
		0xc7000627, 0xc8000628, 0xc9000629, 0xca00062a, 0xcb00062b, 0xcc00062c, 0xcd00062d, 0xce00062e,
		0xcf00062f, 0xd0000630, 0xd1000631, 0xd2000632, 0xd3000633, 0xd4000634, 0xd5000635, 0xd6000636,
		0xd8000637, 0xd9000638, 0xda000639, 0xdb00063a, 0xdc000640, 0xdd000641, 0xde000642, 0xdf000643,
		0xe1000644, 0xe3000645, 0xe4000646, 0xe5000647, 0xe6000648, 0xec000649, 0xed00064a, 0xf000064b,
		0xf100064c, 0xf200064d, 0xf300064e, 0xf500064f, 0xf6000650, 0xf8000651, 0xfa000652, 0x8a000679,
		0x8100067e, 0x8d000686, 0x8f000688, 0x9a000691, 0x8e000698, 0x980006a9, 0x900006af, 0x9f0006ba,
		0xaa0006be, 0xc00006c1, 0xff0006d2, 0x9d00200c, 0x9e00200d, 0xfd00200e, 0xfe00200f, 0x96002013,
		0x97002014, 0x91002018, 0x92002019, 0x8200201a, 0x9300201c, 0x9400201d, 0x8400201e, 0x86002020,
		0x87002021, 0x95002022, 0x85002026, 0x89002030, 0x8b002039, 0x9b00203a, 0x800020ac, 0x99002122,
	},
}

// Windows1257 is the Windows 1257 encoding.
var Windows1257 *Charmap = &windows1257

var windows1257 = Charmap{
	name:          "Windows 1257",
	mib:           identifier.Windows1257,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xac}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xe2, 0x80, 0x9a}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xe2, 0x80, 0x9e}}, {3, [3]byte{0xe2, 0x80, 0xa6}},
		{3, [3]byte{0xe2, 0x80, 0xa0}}, {3, [3]byte{0xe2, 0x80, 0xa1}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0xb0}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0xb9}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {2, [3]byte{0xc2, 0xa8, 0x00}},
		{2, [3]byte{0xcb, 0x87, 0x00}}, {2, [3]byte{0xc2, 0xb8, 0x00}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0x98}},
		{3, [3]byte{0xe2, 0x80, 0x99}}, {3, [3]byte{0xe2, 0x80, 0x9c}},
		{3, [3]byte{0xe2, 0x80, 0x9d}}, {3, [3]byte{0xe2, 0x80, 0xa2}},
		{3, [3]byte{0xe2, 0x80, 0x93}}, {3, [3]byte{0xe2, 0x80, 0x94}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x84, 0xa2}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0xba}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {2, [3]byte{0xc2, 0xaf, 0x00}},
		{2, [3]byte{0xcb, 0x9b, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0x98, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xc5, 0x96, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc3, 0x86, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc2, 0xb3, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc3, 0xb8, 0x00}}, {2, [3]byte{0xc2, 0xb9, 0x00}},
		{2, [3]byte{0xc5, 0x97, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbe, 0x00}}, {2, [3]byte{0xc3, 0xa6, 0x00}},
		{2, [3]byte{0xc4, 0x84, 0x00}}, {2, [3]byte{0xc4, 0xae, 0x00}},
		{2, [3]byte{0xc4, 0x80, 0x00}}, {2, [3]byte{0xc4, 0x86, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc4, 0x98, 0x00}}, {2, [3]byte{0xc4, 0x92, 0x00}},
		{2, [3]byte{0xc4, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc5, 0xb9, 0x00}}, {2, [3]byte{0xc4, 0x96, 0x00}},
		{2, [3]byte{0xc4, 0xa2, 0x00}}, {2, [3]byte{0xc4, 0xb6, 0x00}},
		{2, [3]byte{0xc4, 0xaa, 0x00}}, {2, [3]byte{0xc4, 0xbb, 0x00}},
		{2, [3]byte{0xc5, 0xa0, 0x00}}, {2, [3]byte{0xc5, 0x83, 0x00}},
		{2, [3]byte{0xc5, 0x85, 0x00}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc5, 0x8c, 0x00}}, {2, [3]byte{0xc3, 0x95, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{2, [3]byte{0xc5, 0xb2, 0x00}}, {2, [3]byte{0xc5, 0x81, 0x00}},
		{2, [3]byte{0xc5, 0x9a, 0x00}}, {2, [3]byte{0xc5, 0xaa, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc5, 0xbb, 0x00}},
		{2, [3]byte{0xc5, 0xbd, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc4, 0x85, 0x00}}, {2, [3]byte{0xc4, 0xaf, 0x00}},
		{2, [3]byte{0xc4, 0x81, 0x00}}, {2, [3]byte{0xc4, 0x87, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa5, 0x00}},
		{2, [3]byte{0xc4, 0x99, 0x00}}, {2, [3]byte{0xc4, 0x93, 0x00}},
		{2, [3]byte{0xc4, 0x8d, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc5, 0xba, 0x00}}, {2, [3]byte{0xc4, 0x97, 0x00}},
		{2, [3]byte{0xc4, 0xa3, 0x00}}, {2, [3]byte{0xc4, 0xb7, 0x00}},
		{2, [3]byte{0xc4, 0xab, 0x00}}, {2, [3]byte{0xc4, 0xbc, 0x00}},
		{2, [3]byte{0xc5, 0xa1, 0x00}}, {2, [3]byte{0xc5, 0x84, 0x00}},
		{2, [3]byte{0xc5, 0x86, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc5, 0x8d, 0x00}}, {2, [3]byte{0xc3, 0xb5, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{2, [3]byte{0xc5, 0xb3, 0x00}}, {2, [3]byte{0xc5, 0x82, 0x00}},
		{2, [3]byte{0xc5, 0x9b, 0x00}}, {2, [3]byte{0xc5, 0xab, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc5, 0xbc, 0x00}},
		{2, [3]byte{0xc5, 0xbe, 0x00}}, {2, [3]byte{0xcb, 0x99, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa20000a2, 0xa30000a3, 0xa40000a4, 0xa60000a6, 0xa70000a7, 0x8d0000a8, 0xa90000a9,
		0xab0000ab, 0xac0000ac, 0xad0000ad, 0xae0000ae, 0x9d0000af, 0xb00000b0, 0xb10000b1, 0xb20000b2,
		0xb30000b3, 0xb40000b4, 0xb50000b5, 0xb60000b6, 0xb70000b7, 0x8f0000b8, 0xb90000b9, 0xbb0000bb,
		0xbc0000bc, 0xbd0000bd, 0xbe0000be, 0xc40000c4, 0xc50000c5, 0xaf0000c6, 0xc90000c9, 0xd30000d3,
		0xd50000d5, 0xd60000d6, 0xd70000d7, 0xa80000d8, 0xdc0000dc, 0xdf0000df, 0xe40000e4, 0xe50000e5,
		0xbf0000e6, 0xe90000e9, 0xf30000f3, 0xf50000f5, 0xf60000f6, 0xf70000f7, 0xb80000f8, 0xfc0000fc,
		0xc2000100, 0xe2000101, 0xc0000104, 0xe0000105, 0xc3000106, 0xe3000107, 0xc800010c, 0xe800010d,
		0xc7000112, 0xe7000113, 0xcb000116, 0xeb000117, 0xc6000118, 0xe6000119, 0xcc000122, 0xec000123,
		0xce00012a, 0xee00012b, 0xc100012e, 0xe100012f, 0xcd000136, 0xed000137, 0xcf00013b, 0xef00013c,
		0xd9000141, 0xf9000142, 0xd1000143, 0xf1000144, 0xd2000145, 0xf2000146, 0xd400014c, 0xf400014d,
		0xaa000156, 0xba000157, 0xda00015a, 0xfa00015b, 0xd0000160, 0xf0000161, 0xdb00016a, 0xfb00016b,
		0xd8000172, 0xf8000173, 0xca000179, 0xea00017a, 0xdd00017b, 0xfd00017c, 0xde00017d, 0xfe00017e,
		0x8e0002c7, 0xff0002d9, 0x9e0002db, 0x96002013, 0x97002014, 0x91002018, 0x92002019, 0x8200201a,
		0x9300201c, 0x9400201d, 0x8400201e, 0x86002020, 0x87002021, 0x95002022, 0x85002026, 0x89002030,
		0x8b002039, 0x9b00203a, 0x800020ac, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122,
		0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122,
	},
}

// Windows1258 is the Windows 1258 encoding.
var Windows1258 *Charmap = &windows1258

var windows1258 = Charmap{
	name:          "Windows 1258",
	mib:           identifier.Windows1258,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xac}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xe2, 0x80, 0x9a}}, {2, [3]byte{0xc6, 0x92, 0x00}},
		{3, [3]byte{0xe2, 0x80, 0x9e}}, {3, [3]byte{0xe2, 0x80, 0xa6}},
		{3, [3]byte{0xe2, 0x80, 0xa0}}, {3, [3]byte{0xe2, 0x80, 0xa1}},
		{2, [3]byte{0xcb, 0x86, 0x00}}, {3, [3]byte{0xe2, 0x80, 0xb0}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0xb9}},
		{2, [3]byte{0xc5, 0x92, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0x98}},
		{3, [3]byte{0xe2, 0x80, 0x99}}, {3, [3]byte{0xe2, 0x80, 0x9c}},
		{3, [3]byte{0xe2, 0x80, 0x9d}}, {3, [3]byte{0xe2, 0x80, 0xa2}},
		{3, [3]byte{0xe2, 0x80, 0x93}}, {3, [3]byte{0xe2, 0x80, 0x94}},
		{2, [3]byte{0xcb, 0x9c, 0x00}}, {3, [3]byte{0xe2, 0x84, 0xa2}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {3, [3]byte{0xe2, 0x80, 0xba}},
		{2, [3]byte{0xc5, 0x93, 0x00}}, {3, [3]byte{0xef, 0xbf, 0xbd}},
		{3, [3]byte{0xef, 0xbf, 0xbd}}, {2, [3]byte{0xc5, 0xb8, 0x00}},
		{2, [3]byte{0xc2, 0xa0, 0x00}}, {2, [3]byte{0xc2, 0xa1, 0x00}},
		{2, [3]byte{0xc2, 0xa2, 0x00}}, {2, [3]byte{0xc2, 0xa3, 0x00}},
		{2, [3]byte{0xc2, 0xa4, 0x00}}, {2, [3]byte{0xc2, 0xa5, 0x00}},
		{2, [3]byte{0xc2, 0xa6, 0x00}}, {2, [3]byte{0xc2, 0xa7, 0x00}},
		{2, [3]byte{0xc2, 0xa8, 0x00}}, {2, [3]byte{0xc2, 0xa9, 0x00}},
		{2, [3]byte{0xc2, 0xaa, 0x00}}, {2, [3]byte{0xc2, 0xab, 0x00}},
		{2, [3]byte{0xc2, 0xac, 0x00}}, {2, [3]byte{0xc2, 0xad, 0x00}},
		{2, [3]byte{0xc2, 0xae, 0x00}}, {2, [3]byte{0xc2, 0xaf, 0x00}},
		{2, [3]byte{0xc2, 0xb0, 0x00}}, {2, [3]byte{0xc2, 0xb1, 0x00}},
		{2, [3]byte{0xc2, 0xb2, 0x00}}, {2, [3]byte{0xc2, 0xb3, 0x00}},
		{2, [3]byte{0xc2, 0xb4, 0x00}}, {2, [3]byte{0xc2, 0xb5, 0x00}},
		{2, [3]byte{0xc2, 0xb6, 0x00}}, {2, [3]byte{0xc2, 0xb7, 0x00}},
		{2, [3]byte{0xc2, 0xb8, 0x00}}, {2, [3]byte{0xc2, 0xb9, 0x00}},
		{2, [3]byte{0xc2, 0xba, 0x00}}, {2, [3]byte{0xc2, 0xbb, 0x00}},
		{2, [3]byte{0xc2, 0xbc, 0x00}}, {2, [3]byte{0xc2, 0xbd, 0x00}},
		{2, [3]byte{0xc2, 0xbe, 0x00}}, {2, [3]byte{0xc2, 0xbf, 0x00}},
		{2, [3]byte{0xc3, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x81, 0x00}},
		{2, [3]byte{0xc3, 0x82, 0x00}}, {2, [3]byte{0xc4, 0x82, 0x00}},
		{2, [3]byte{0xc3, 0x84, 0x00}}, {2, [3]byte{0xc3, 0x85, 0x00}},
		{2, [3]byte{0xc3, 0x86, 0x00}}, {2, [3]byte{0xc3, 0x87, 0x00}},
		{2, [3]byte{0xc3, 0x88, 0x00}}, {2, [3]byte{0xc3, 0x89, 0x00}},
		{2, [3]byte{0xc3, 0x8a, 0x00}}, {2, [3]byte{0xc3, 0x8b, 0x00}},
		{2, [3]byte{0xcc, 0x80, 0x00}}, {2, [3]byte{0xc3, 0x8d, 0x00}},
		{2, [3]byte{0xc3, 0x8e, 0x00}}, {2, [3]byte{0xc3, 0x8f, 0x00}},
		{2, [3]byte{0xc4, 0x90, 0x00}}, {2, [3]byte{0xc3, 0x91, 0x00}},
		{2, [3]byte{0xcc, 0x89, 0x00}}, {2, [3]byte{0xc3, 0x93, 0x00}},
		{2, [3]byte{0xc3, 0x94, 0x00}}, {2, [3]byte{0xc6, 0xa0, 0x00}},
		{2, [3]byte{0xc3, 0x96, 0x00}}, {2, [3]byte{0xc3, 0x97, 0x00}},
		{2, [3]byte{0xc3, 0x98, 0x00}}, {2, [3]byte{0xc3, 0x99, 0x00}},
		{2, [3]byte{0xc3, 0x9a, 0x00}}, {2, [3]byte{0xc3, 0x9b, 0x00}},
		{2, [3]byte{0xc3, 0x9c, 0x00}}, {2, [3]byte{0xc6, 0xaf, 0x00}},
		{2, [3]byte{0xcc, 0x83, 0x00}}, {2, [3]byte{0xc3, 0x9f, 0x00}},
		{2, [3]byte{0xc3, 0xa0, 0x00}}, {2, [3]byte{0xc3, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xa2, 0x00}}, {2, [3]byte{0xc4, 0x83, 0x00}},
		{2, [3]byte{0xc3, 0xa4, 0x00}}, {2, [3]byte{0xc3, 0xa5, 0x00}},
		{2, [3]byte{0xc3, 0xa6, 0x00}}, {2, [3]byte{0xc3, 0xa7, 0x00}},
		{2, [3]byte{0xc3, 0xa8, 0x00}}, {2, [3]byte{0xc3, 0xa9, 0x00}},
		{2, [3]byte{0xc3, 0xaa, 0x00}}, {2, [3]byte{0xc3, 0xab, 0x00}},
		{2, [3]byte{0xcc, 0x81, 0x00}}, {2, [3]byte{0xc3, 0xad, 0x00}},
		{2, [3]byte{0xc3, 0xae, 0x00}}, {2, [3]byte{0xc3, 0xaf, 0x00}},
		{2, [3]byte{0xc4, 0x91, 0x00}}, {2, [3]byte{0xc3, 0xb1, 0x00}},
		{2, [3]byte{0xcc, 0xa3, 0x00}}, {2, [3]byte{0xc3, 0xb3, 0x00}},
		{2, [3]byte{0xc3, 0xb4, 0x00}}, {2, [3]byte{0xc6, 0xa1, 0x00}},
		{2, [3]byte{0xc3, 0xb6, 0x00}}, {2, [3]byte{0xc3, 0xb7, 0x00}},
		{2, [3]byte{0xc3, 0xb8, 0x00}}, {2, [3]byte{0xc3, 0xb9, 0x00}},
		{2, [3]byte{0xc3, 0xba, 0x00}}, {2, [3]byte{0xc3, 0xbb, 0x00}},
		{2, [3]byte{0xc3, 0xbc, 0x00}}, {2, [3]byte{0xc6, 0xb0, 0x00}},
		{3, [3]byte{0xe2, 0x82, 0xab}}, {2, [3]byte{0xc3, 0xbf, 0x00}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0xa00000a0, 0xa10000a1, 0xa20000a2, 0xa30000a3, 0xa40000a4, 0xa50000a5, 0xa60000a6, 0xa70000a7,
		0xa80000a8, 0xa90000a9, 0xaa0000aa, 0xab0000ab, 0xac0000ac, 0xad0000ad, 0xae0000ae, 0xaf0000af,
		0xb00000b0, 0xb10000b1, 0xb20000b2, 0xb30000b3, 0xb40000b4, 0xb50000b5, 0xb60000b6, 0xb70000b7,
		0xb80000b8, 0xb90000b9, 0xba0000ba, 0xbb0000bb, 0xbc0000bc, 0xbd0000bd, 0xbe0000be, 0xbf0000bf,
		0xc00000c0, 0xc10000c1, 0xc20000c2, 0xc40000c4, 0xc50000c5, 0xc60000c6, 0xc70000c7, 0xc80000c8,
		0xc90000c9, 0xca0000ca, 0xcb0000cb, 0xcd0000cd, 0xce0000ce, 0xcf0000cf, 0xd10000d1, 0xd30000d3,
		0xd40000d4, 0xd60000d6, 0xd70000d7, 0xd80000d8, 0xd90000d9, 0xda0000da, 0xdb0000db, 0xdc0000dc,
		0xdf0000df, 0xe00000e0, 0xe10000e1, 0xe20000e2, 0xe40000e4, 0xe50000e5, 0xe60000e6, 0xe70000e7,
		0xe80000e8, 0xe90000e9, 0xea0000ea, 0xeb0000eb, 0xed0000ed, 0xee0000ee, 0xef0000ef, 0xf10000f1,
		0xf30000f3, 0xf40000f4, 0xf60000f6, 0xf70000f7, 0xf80000f8, 0xf90000f9, 0xfa0000fa, 0xfb0000fb,
		0xfc0000fc, 0xff0000ff, 0xc3000102, 0xe3000103, 0xd0000110, 0xf0000111, 0x8c000152, 0x9c000153,
		0x9f000178, 0x83000192, 0xd50001a0, 0xf50001a1, 0xdd0001af, 0xfd0001b0, 0x880002c6, 0x980002dc,
		0xcc000300, 0xec000301, 0xde000303, 0xd2000309, 0xf2000323, 0x96002013, 0x97002014, 0x91002018,
		0x92002019, 0x8200201a, 0x9300201c, 0x9400201d, 0x8400201e, 0x86002020, 0x87002021, 0x95002022,
		0x85002026, 0x89002030, 0x8b002039, 0x9b00203a, 0xfe0020ab, 0x800020ac, 0x99002122, 0x99002122,
		0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122, 0x99002122,
	},
}

// XUserDefined is the X-User-Defined encoding.
//
// It is defined at http://encoding.spec.whatwg.org/#x-user-defined
var XUserDefined *Charmap = &xUserDefined

var xUserDefined = Charmap{
	name:          "X-User-Defined",
	mib:           identifier.XUserDefined,
	asciiSuperset: true,
	low:           0x80,
	replacement:   0x1a,
	decode: [256]utf8Enc{
		{1, [3]byte{0x00, 0x00, 0x00}}, {1, [3]byte{0x01, 0x00, 0x00}},
		{1, [3]byte{0x02, 0x00, 0x00}}, {1, [3]byte{0x03, 0x00, 0x00}},
		{1, [3]byte{0x04, 0x00, 0x00}}, {1, [3]byte{0x05, 0x00, 0x00}},
		{1, [3]byte{0x06, 0x00, 0x00}}, {1, [3]byte{0x07, 0x00, 0x00}},
		{1, [3]byte{0x08, 0x00, 0x00}}, {1, [3]byte{0x09, 0x00, 0x00}},
		{1, [3]byte{0x0a, 0x00, 0x00}}, {1, [3]byte{0x0b, 0x00, 0x00}},
		{1, [3]byte{0x0c, 0x00, 0x00}}, {1, [3]byte{0x0d, 0x00, 0x00}},
		{1, [3]byte{0x0e, 0x00, 0x00}}, {1, [3]byte{0x0f, 0x00, 0x00}},
		{1, [3]byte{0x10, 0x00, 0x00}}, {1, [3]byte{0x11, 0x00, 0x00}},
		{1, [3]byte{0x12, 0x00, 0x00}}, {1, [3]byte{0x13, 0x00, 0x00}},
		{1, [3]byte{0x14, 0x00, 0x00}}, {1, [3]byte{0x15, 0x00, 0x00}},
		{1, [3]byte{0x16, 0x00, 0x00}}, {1, [3]byte{0x17, 0x00, 0x00}},
		{1, [3]byte{0x18, 0x00, 0x00}}, {1, [3]byte{0x19, 0x00, 0x00}},
		{1, [3]byte{0x1a, 0x00, 0x00}}, {1, [3]byte{0x1b, 0x00, 0x00}},
		{1, [3]byte{0x1c, 0x00, 0x00}}, {1, [3]byte{0x1d, 0x00, 0x00}},
		{1, [3]byte{0x1e, 0x00, 0x00}}, {1, [3]byte{0x1f, 0x00, 0x00}},
		{1, [3]byte{0x20, 0x00, 0x00}}, {1, [3]byte{0x21, 0x00, 0x00}},
		{1, [3]byte{0x22, 0x00, 0x00}}, {1, [3]byte{0x23, 0x00, 0x00}},
		{1, [3]byte{0x24, 0x00, 0x00}}, {1, [3]byte{0x25, 0x00, 0x00}},
		{1, [3]byte{0x26, 0x00, 0x00}}, {1, [3]byte{0x27, 0x00, 0x00}},
		{1, [3]byte{0x28, 0x00, 0x00}}, {1, [3]byte{0x29, 0x00, 0x00}},
		{1, [3]byte{0x2a, 0x00, 0x00}}, {1, [3]byte{0x2b, 0x00, 0x00}},
		{1, [3]byte{0x2c, 0x00, 0x00}}, {1, [3]byte{0x2d, 0x00, 0x00}},
		{1, [3]byte{0x2e, 0x00, 0x00}}, {1, [3]byte{0x2f, 0x00, 0x00}},
		{1, [3]byte{0x30, 0x00, 0x00}}, {1, [3]byte{0x31, 0x00, 0x00}},
		{1, [3]byte{0x32, 0x00, 0x00}}, {1, [3]byte{0x33, 0x00, 0x00}},
		{1, [3]byte{0x34, 0x00, 0x00}}, {1, [3]byte{0x35, 0x00, 0x00}},
		{1, [3]byte{0x36, 0x00, 0x00}}, {1, [3]byte{0x37, 0x00, 0x00}},
		{1, [3]byte{0x38, 0x00, 0x00}}, {1, [3]byte{0x39, 0x00, 0x00}},
		{1, [3]byte{0x3a, 0x00, 0x00}}, {1, [3]byte{0x3b, 0x00, 0x00}},
		{1, [3]byte{0x3c, 0x00, 0x00}}, {1, [3]byte{0x3d, 0x00, 0x00}},
		{1, [3]byte{0x3e, 0x00, 0x00}}, {1, [3]byte{0x3f, 0x00, 0x00}},
		{1, [3]byte{0x40, 0x00, 0x00}}, {1, [3]byte{0x41, 0x00, 0x00}},
		{1, [3]byte{0x42, 0x00, 0x00}}, {1, [3]byte{0x43, 0x00, 0x00}},
		{1, [3]byte{0x44, 0x00, 0x00}}, {1, [3]byte{0x45, 0x00, 0x00}},
		{1, [3]byte{0x46, 0x00, 0x00}}, {1, [3]byte{0x47, 0x00, 0x00}},
		{1, [3]byte{0x48, 0x00, 0x00}}, {1, [3]byte{0x49, 0x00, 0x00}},
		{1, [3]byte{0x4a, 0x00, 0x00}}, {1, [3]byte{0x4b, 0x00, 0x00}},
		{1, [3]byte{0x4c, 0x00, 0x00}}, {1, [3]byte{0x4d, 0x00, 0x00}},
		{1, [3]byte{0x4e, 0x00, 0x00}}, {1, [3]byte{0x4f, 0x00, 0x00}},
		{1, [3]byte{0x50, 0x00, 0x00}}, {1, [3]byte{0x51, 0x00, 0x00}},
		{1, [3]byte{0x52, 0x00, 0x00}}, {1, [3]byte{0x53, 0x00, 0x00}},
		{1, [3]byte{0x54, 0x00, 0x00}}, {1, [3]byte{0x55, 0x00, 0x00}},
		{1, [3]byte{0x56, 0x00, 0x00}}, {1, [3]byte{0x57, 0x00, 0x00}},
		{1, [3]byte{0x58, 0x00, 0x00}}, {1, [3]byte{0x59, 0x00, 0x00}},
		{1, [3]byte{0x5a, 0x00, 0x00}}, {1, [3]byte{0x5b, 0x00, 0x00}},
		{1, [3]byte{0x5c, 0x00, 0x00}}, {1, [3]byte{0x5d, 0x00, 0x00}},
		{1, [3]byte{0x5e, 0x00, 0x00}}, {1, [3]byte{0x5f, 0x00, 0x00}},
		{1, [3]byte{0x60, 0x00, 0x00}}, {1, [3]byte{0x61, 0x00, 0x00}},
		{1, [3]byte{0x62, 0x00, 0x00}}, {1, [3]byte{0x63, 0x00, 0x00}},
		{1, [3]byte{0x64, 0x00, 0x00}}, {1, [3]byte{0x65, 0x00, 0x00}},
		{1, [3]byte{0x66, 0x00, 0x00}}, {1, [3]byte{0x67, 0x00, 0x00}},
		{1, [3]byte{0x68, 0x00, 0x00}}, {1, [3]byte{0x69, 0x00, 0x00}},
		{1, [3]byte{0x6a, 0x00, 0x00}}, {1, [3]byte{0x6b, 0x00, 0x00}},
		{1, [3]byte{0x6c, 0x00, 0x00}}, {1, [3]byte{0x6d, 0x00, 0x00}},
		{1, [3]byte{0x6e, 0x00, 0x00}}, {1, [3]byte{0x6f, 0x00, 0x00}},
		{1, [3]byte{0x70, 0x00, 0x00}}, {1, [3]byte{0x71, 0x00, 0x00}},
		{1, [3]byte{0x72, 0x00, 0x00}}, {1, [3]byte{0x73, 0x00, 0x00}},
		{1, [3]byte{0x74, 0x00, 0x00}}, {1, [3]byte{0x75, 0x00, 0x00}},
		{1, [3]byte{0x76, 0x00, 0x00}}, {1, [3]byte{0x77, 0x00, 0x00}},
		{1, [3]byte{0x78, 0x00, 0x00}}, {1, [3]byte{0x79, 0x00, 0x00}},
		{1, [3]byte{0x7a, 0x00, 0x00}}, {1, [3]byte{0x7b, 0x00, 0x00}},
		{1, [3]byte{0x7c, 0x00, 0x00}}, {1, [3]byte{0x7d, 0x00, 0x00}},
		{1, [3]byte{0x7e, 0x00, 0x00}}, {1, [3]byte{0x7f, 0x00, 0x00}},
		{3, [3]byte{0xef, 0x9e, 0x80}}, {3, [3]byte{0xef, 0x9e, 0x81}},
		{3, [3]byte{0xef, 0x9e, 0x82}}, {3, [3]byte{0xef, 0x9e, 0x83}},
		{3, [3]byte{0xef, 0x9e, 0x84}}, {3, [3]byte{0xef, 0x9e, 0x85}},
		{3, [3]byte{0xef, 0x9e, 0x86}}, {3, [3]byte{0xef, 0x9e, 0x87}},
		{3, [3]byte{0xef, 0x9e, 0x88}}, {3, [3]byte{0xef, 0x9e, 0x89}},
		{3, [3]byte{0xef, 0x9e, 0x8a}}, {3, [3]byte{0xef, 0x9e, 0x8b}},
		{3, [3]byte{0xef, 0x9e, 0x8c}}, {3, [3]byte{0xef, 0x9e, 0x8d}},
		{3, [3]byte{0xef, 0x9e, 0x8e}}, {3, [3]byte{0xef, 0x9e, 0x8f}},
		{3, [3]byte{0xef, 0x9e, 0x90}}, {3, [3]byte{0xef, 0x9e, 0x91}},
		{3, [3]byte{0xef, 0x9e, 0x92}}, {3, [3]byte{0xef, 0x9e, 0x93}},
		{3, [3]byte{0xef, 0x9e, 0x94}}, {3, [3]byte{0xef, 0x9e, 0x95}},
		{3, [3]byte{0xef, 0x9e, 0x96}}, {3, [3]byte{0xef, 0x9e, 0x97}},
		{3, [3]byte{0xef, 0x9e, 0x98}}, {3, [3]byte{0xef, 0x9e, 0x99}},
		{3, [3]byte{0xef, 0x9e, 0x9a}}, {3, [3]byte{0xef, 0x9e, 0x9b}},
		{3, [3]byte{0xef, 0x9e, 0x9c}}, {3, [3]byte{0xef, 0x9e, 0x9d}},
		{3, [3]byte{0xef, 0x9e, 0x9e}}, {3, [3]byte{0xef, 0x9e, 0x9f}},
		{3, [3]byte{0xef, 0x9e, 0xa0}}, {3, [3]byte{0xef, 0x9e, 0xa1}},
		{3, [3]byte{0xef, 0x9e, 0xa2}}, {3, [3]byte{0xef, 0x9e, 0xa3}},
		{3, [3]byte{0xef, 0x9e, 0xa4}}, {3, [3]byte{0xef, 0x9e, 0xa5}},
		{3, [3]byte{0xef, 0x9e, 0xa6}}, {3, [3]byte{0xef, 0x9e, 0xa7}},
		{3, [3]byte{0xef, 0x9e, 0xa8}}, {3, [3]byte{0xef, 0x9e, 0xa9}},
		{3, [3]byte{0xef, 0x9e, 0xaa}}, {3, [3]byte{0xef, 0x9e, 0xab}},
		{3, [3]byte{0xef, 0x9e, 0xac}}, {3, [3]byte{0xef, 0x9e, 0xad}},
		{3, [3]byte{0xef, 0x9e, 0xae}}, {3, [3]byte{0xef, 0x9e, 0xaf}},
		{3, [3]byte{0xef, 0x9e, 0xb0}}, {3, [3]byte{0xef, 0x9e, 0xb1}},
		{3, [3]byte{0xef, 0x9e, 0xb2}}, {3, [3]byte{0xef, 0x9e, 0xb3}},
		{3, [3]byte{0xef, 0x9e, 0xb4}}, {3, [3]byte{0xef, 0x9e, 0xb5}},
		{3, [3]byte{0xef, 0x9e, 0xb6}}, {3, [3]byte{0xef, 0x9e, 0xb7}},
		{3, [3]byte{0xef, 0x9e, 0xb8}}, {3, [3]byte{0xef, 0x9e, 0xb9}},
		{3, [3]byte{0xef, 0x9e, 0xba}}, {3, [3]byte{0xef, 0x9e, 0xbb}},
		{3, [3]byte{0xef, 0x9e, 0xbc}}, {3, [3]byte{0xef, 0x9e, 0xbd}},
		{3, [3]byte{0xef, 0x9e, 0xbe}}, {3, [3]byte{0xef, 0x9e, 0xbf}},
		{3, [3]byte{0xef, 0x9f, 0x80}}, {3, [3]byte{0xef, 0x9f, 0x81}},
		{3, [3]byte{0xef, 0x9f, 0x82}}, {3, [3]byte{0xef, 0x9f, 0x83}},
		{3, [3]byte{0xef, 0x9f, 0x84}}, {3, [3]byte{0xef, 0x9f, 0x85}},
		{3, [3]byte{0xef, 0x9f, 0x86}}, {3, [3]byte{0xef, 0x9f, 0x87}},
		{3, [3]byte{0xef, 0x9f, 0x88}}, {3, [3]byte{0xef, 0x9f, 0x89}},
		{3, [3]byte{0xef, 0x9f, 0x8a}}, {3, [3]byte{0xef, 0x9f, 0x8b}},
		{3, [3]byte{0xef, 0x9f, 0x8c}}, {3, [3]byte{0xef, 0x9f, 0x8d}},
		{3, [3]byte{0xef, 0x9f, 0x8e}}, {3, [3]byte{0xef, 0x9f, 0x8f}},
		{3, [3]byte{0xef, 0x9f, 0x90}}, {3, [3]byte{0xef, 0x9f, 0x91}},
		{3, [3]byte{0xef, 0x9f, 0x92}}, {3, [3]byte{0xef, 0x9f, 0x93}},
		{3, [3]byte{0xef, 0x9f, 0x94}}, {3, [3]byte{0xef, 0x9f, 0x95}},
		{3, [3]byte{0xef, 0x9f, 0x96}}, {3, [3]byte{0xef, 0x9f, 0x97}},
		{3, [3]byte{0xef, 0x9f, 0x98}}, {3, [3]byte{0xef, 0x9f, 0x99}},
		{3, [3]byte{0xef, 0x9f, 0x9a}}, {3, [3]byte{0xef, 0x9f, 0x9b}},
		{3, [3]byte{0xef, 0x9f, 0x9c}}, {3, [3]byte{0xef, 0x9f, 0x9d}},
		{3, [3]byte{0xef, 0x9f, 0x9e}}, {3, [3]byte{0xef, 0x9f, 0x9f}},
		{3, [3]byte{0xef, 0x9f, 0xa0}}, {3, [3]byte{0xef, 0x9f, 0xa1}},
		{3, [3]byte{0xef, 0x9f, 0xa2}}, {3, [3]byte{0xef, 0x9f, 0xa3}},
		{3, [3]byte{0xef, 0x9f, 0xa4}}, {3, [3]byte{0xef, 0x9f, 0xa5}},
		{3, [3]byte{0xef, 0x9f, 0xa6}}, {3, [3]byte{0xef, 0x9f, 0xa7}},
		{3, [3]byte{0xef, 0x9f, 0xa8}}, {3, [3]byte{0xef, 0x9f, 0xa9}},
		{3, [3]byte{0xef, 0x9f, 0xaa}}, {3, [3]byte{0xef, 0x9f, 0xab}},
		{3, [3]byte{0xef, 0x9f, 0xac}}, {3, [3]byte{0xef, 0x9f, 0xad}},
		{3, [3]byte{0xef, 0x9f, 0xae}}, {3, [3]byte{0xef, 0x9f, 0xaf}},
		{3, [3]byte{0xef, 0x9f, 0xb0}}, {3, [3]byte{0xef, 0x9f, 0xb1}},
		{3, [3]byte{0xef, 0x9f, 0xb2}}, {3, [3]byte{0xef, 0x9f, 0xb3}},
		{3, [3]byte{0xef, 0x9f, 0xb4}}, {3, [3]byte{0xef, 0x9f, 0xb5}},
		{3, [3]byte{0xef, 0x9f, 0xb6}}, {3, [3]byte{0xef, 0x9f, 0xb7}},
		{3, [3]byte{0xef, 0x9f, 0xb8}}, {3, [3]byte{0xef, 0x9f, 0xb9}},
		{3, [3]byte{0xef, 0x9f, 0xba}}, {3, [3]byte{0xef, 0x9f, 0xbb}},
		{3, [3]byte{0xef, 0x9f, 0xbc}}, {3, [3]byte{0xef, 0x9f, 0xbd}},
		{3, [3]byte{0xef, 0x9f, 0xbe}}, {3, [3]byte{0xef, 0x9f, 0xbf}},
	},
	encode: [256]uint32{
		0x00000000, 0x01000001, 0x02000002, 0x03000003, 0x04000004, 0x05000005, 0x06000006, 0x07000007,
		0x08000008, 0x09000009, 0x0a00000a, 0x0b00000b, 0x0c00000c, 0x0d00000d, 0x0e00000e, 0x0f00000f,
		0x10000010, 0x11000011, 0x12000012, 0x13000013, 0x14000014, 0x15000015, 0x16000016, 0x17000017,
		0x18000018, 0x19000019, 0x1a00001a, 0x1b00001b, 0x1c00001c, 0x1d00001d, 0x1e00001e, 0x1f00001f,
		0x20000020, 0x21000021, 0x22000022, 0x23000023, 0x24000024, 0x25000025, 0x26000026, 0x27000027,
		0x28000028, 0x29000029, 0x2a00002a, 0x2b00002b, 0x2c00002c, 0x2d00002d, 0x2e00002e, 0x2f00002f,
		0x30000030, 0x31000031, 0x32000032, 0x33000033, 0x34000034, 0x35000035, 0x36000036, 0x37000037,
		0x38000038, 0x39000039, 0x3a00003a, 0x3b00003b, 0x3c00003c, 0x3d00003d, 0x3e00003e, 0x3f00003f,
		0x40000040, 0x41000041, 0x42000042, 0x43000043, 0x44000044, 0x45000045, 0x46000046, 0x47000047,
		0x48000048, 0x49000049, 0x4a00004a, 0x4b00004b, 0x4c00004c, 0x4d00004d, 0x4e00004e, 0x4f00004f,
		0x50000050, 0x51000051, 0x52000052, 0x53000053, 0x54000054, 0x55000055, 0x56000056, 0x57000057,
		0x58000058, 0x59000059, 0x5a00005a, 0x5b00005b, 0x5c00005c, 0x5d00005d, 0x5e00005e, 0x5f00005f,
		0x60000060, 0x61000061, 0x62000062, 0x63000063, 0x64000064, 0x65000065, 0x66000066, 0x67000067,
		0x68000068, 0x69000069, 0x6a00006a, 0x6b00006b, 0x6c00006c, 0x6d00006d, 0x6e00006e, 0x6f00006f,
		0x70000070, 0x71000071, 0x72000072, 0x73000073, 0x74000074, 0x75000075, 0x76000076, 0x77000077,
		0x78000078, 0x79000079, 0x7a00007a, 0x7b00007b, 0x7c00007c, 0x7d00007d, 0x7e00007e, 0x7f00007f,
		0x8000f780, 0x8100f781, 0x8200f782, 0x8300f783, 0x8400f784, 0x8500f785, 0x8600f786, 0x8700f787,
		0x8800f788, 0x8900f789, 0x8a00f78a, 0x8b00f78b, 0x8c00f78c, 0x8d00f78d, 0x8e00f78e, 0x8f00f78f,
		0x9000f790, 0x9100f791, 0x9200f792, 0x9300f793, 0x9400f794, 0x9500f795, 0x9600f796, 0x9700f797,
		0x9800f798, 0x9900f799, 0x9a00f79a, 0x9b00f79b, 0x9c00f79c, 0x9d00f79d, 0x9e00f79e, 0x9f00f79f,
		0xa000f7a0, 0xa100f7a1, 0xa200f7a2, 0xa300f7a3, 0xa400f7a4, 0xa500f7a5, 0xa600f7a6, 0xa700f7a7,
		0xa800f7a8, 0xa900f7a9, 0xaa00f7aa, 0xab00f7ab, 0xac00f7ac, 0xad00f7ad, 0xae00f7ae, 0xaf00f7af,
		0xb000f7b0, 0xb100f7b1, 0xb200f7b2, 0xb300f7b3, 0xb400f7b4, 0xb500f7b5, 0xb600f7b6, 0xb700f7b7,
		0xb800f7b8, 0xb900f7b9, 0xba00f7ba, 0xbb00f7bb, 0xbc00f7bc, 0xbd00f7bd, 0xbe00f7be, 0xbf00f7bf,
		0xc000f7c0, 0xc100f7c1, 0xc200f7c2, 0xc300f7c3, 0xc400f7c4, 0xc500f7c5, 0xc600f7c6, 0xc700f7c7,
		0xc800f7c8, 0xc900f7c9, 0xca00f7ca, 0xcb00f7cb, 0xcc00f7cc, 0xcd00f7cd, 0xce00f7ce, 0xcf00f7cf,
		0xd000f7d0, 0xd100f7d1, 0xd200f7d2, 0xd300f7d3, 0xd400f7d4, 0xd500f7d5, 0xd600f7d6, 0xd700f7d7,
		0xd800f7d8, 0xd900f7d9, 0xda00f7da, 0xdb00f7db, 0xdc00f7dc, 0xdd00f7dd, 0xde00f7de, 0xdf00f7df,
		0xe000f7e0, 0xe100f7e1, 0xe200f7e2, 0xe300f7e3, 0xe400f7e4, 0xe500f7e5, 0xe600f7e6, 0xe700f7e7,
		0xe800f7e8, 0xe900f7e9, 0xea00f7ea, 0xeb00f7eb, 0xec00f7ec, 0xed00f7ed, 0xee00f7ee, 0xef00f7ef,
		0xf000f7f0, 0xf100f7f1, 0xf200f7f2, 0xf300f7f3, 0xf400f7f4, 0xf500f7f5, 0xf600f7f6, 0xf700f7f7,
		0xf800f7f8, 0xf900f7f9, 0xfa00f7fa, 0xfb00f7fb, 0xfc00f7fc, 0xfd00f7fd, 0xfe00f7fe, 0xff00f7ff,
	},
}
var listAll = []encoding.Encoding{
	CodePage037,
	CodePage437,
	CodePage850,
	CodePage852,
	CodePage855,
	CodePage858,
	CodePage860,
	CodePage862,
	CodePage863,
	CodePage865,
	CodePage866,
	CodePage1047,
	CodePage1140,
	ISO8859_1,
	ISO8859_2,
	ISO8859_3,
	ISO8859_4,
	ISO8859_5,
	ISO8859_6,
	ISO8859_6E,
	ISO8859_6I,
	ISO8859_7,
	ISO8859_8,
	ISO8859_8E,
	ISO8859_8I,
	ISO8859_9,
	ISO8859_10,
	ISO8859_13,
	ISO8859_14,
	ISO8859_15,
	ISO8859_16,
	KOI8R,
	KOI8U,
	Macintosh,
	MacintoshCyrillic,
	Windows874,
	Windows1250,
	Windows1251,
	Windows1252,
	Windows1253,
	Windows1254,
	Windows1255,
	Windows1256,
	Windows1257,
	Windows1258,
	XUserDefined,
}

// Total table size 87024 bytes (84KiB); checksum: 811C9DC5
