{"language": "en-US", "messages": [{"id": "inline {ARG1}", "key": "inline %s", "message": "inline {ARG1}", "translation": "", "placeholders": [{"id": "ARG1", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "\"ARG1\""}], "position": "testdata/ssa/ssa.go:16:7"}, {"id": "global printer used {ARG1}", "key": "global printer used %s", "message": "global printer used {ARG1}", "translation": "", "placeholders": [{"id": "ARG1", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "\"ARG1\""}], "position": "testdata/ssa/ssa.go:17:8"}, {"id": "number: {2}, string: {STRING_ARG}, bool: {True}", "key": "number: %d, string: %s, bool: %v", "message": "number: {2}, string: {STRING_ARG}, bool: {True}", "translation": "", "placeholders": [{"id": "2", "string": "%[1]d", "type": "int", "underlyingType": "int", "argNum": 1, "expr": "2"}, {"id": "STRING_ARG", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "\"STRING ARG\""}, {"id": "True", "string": "%[3]v", "type": "bool", "underlyingType": "bool", "argNum": 3, "expr": "true"}], "position": "testdata/ssa/ssa.go:22:9"}, {"id": "empty string", "key": "empty string", "message": "empty string", "translation": "", "position": "testdata/ssa/ssa.go:23:9"}, {"id": "Lovely weather today!", "key": "Lovely weather today!", "message": "Lovely weather today!", "translation": "", "position": "testdata/ssa/ssa.go:24:8"}, {"id": "number one", "key": "number one", "message": "number one", "translation": "", "position": "testdata/ssa/ssa.go:32:8"}, {"id": ["v", "number: {C}"], "key": "number: %d", "message": "number: {C}", "translation": "", "placeholders": [{"id": "C", "string": "%[1]d", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "c"}], "position": "testdata/ssa/ssa.go:79:10"}, {"id": ["format", "constant local {Args}"], "key": "constant local %s", "message": "constant local {Args}", "translation": "", "placeholders": [{"id": "<PERSON><PERSON><PERSON>", "string": "%[1]s", "type": "[]interface{}", "underlyingType": "[]interface{}", "argNum": 1, "expr": "args"}], "position": "testdata/ssa/ssa.go:88:11"}, {"id": ["a", "foo {Arg1} {B}"], "key": "foo %s %s", "message": "foo {Arg1} {B}", "translation": "", "placeholders": [{"id": "Arg1", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "arg1"}, {"id": "B", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "b"}], "position": "testdata/ssa/ssa.go:139:7"}, {"id": ["a", "bar {Arg1} {B}"], "key": "bar %s %s", "message": "bar {Arg1} {B}", "translation": "", "placeholders": [{"id": "Arg1", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "arg1"}, {"id": "B", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "b"}], "position": "testdata/ssa/ssa.go:139:7"}, {"id": ["a", "foo"], "key": "foo", "message": "foo", "translation": "", "position": "testdata/ssa/ssa.go:153:8"}, {"id": ["a", "bar"], "key": "bar", "message": "bar", "translation": "", "position": "testdata/ssa/ssa.go:153:8"}, {"id": ["a", "baz"], "key": "baz", "message": "baz", "translation": "", "position": "testdata/ssa/ssa.go:153:8"}, {"id": ["str", "const str"], "key": "const str", "message": "const str", "translation": "", "position": "testdata/ssa/ssa.go:168:11"}, {"id": ["globalStr", "See you around in {City}!"], "key": "See you around in %s!", "message": "See you around in {City}!", "translation": "", "placeholders": [{"id": "City", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "city"}], "position": "testdata/ssa/ssa.go:181:5"}, {"id": ["constFood", "Please eat your {Food}!"], "key": "Please eat your %s!", "message": "Please eat your {Food}!", "translation": "", "comment": "Ho ho ho", "placeholders": [{"id": "Food", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "food", "comment": "the food to be consumed by the subject"}], "position": "testdata/ssa/ssa.go:193:2"}, {"id": ["msgHello", "Hello, {<PERSON><PERSON><PERSON>} and {Arg_2}!"], "key": "Hello, %d and %s!", "message": "Hello, {<PERSON><PERSON><PERSON>} and {Arg_2}!", "translation": "", "comment": "Ho ho ho", "placeholders": [{"id": "Integer", "string": "%[1]d", "type": "", "underlyingType": "int", "argNum": 1}, {"id": "Arg_2", "string": "%[2]s", "type": "", "underlyingType": "string", "argNum": 2}], "position": "testdata/ssa/ssa.go:193:2"}]}