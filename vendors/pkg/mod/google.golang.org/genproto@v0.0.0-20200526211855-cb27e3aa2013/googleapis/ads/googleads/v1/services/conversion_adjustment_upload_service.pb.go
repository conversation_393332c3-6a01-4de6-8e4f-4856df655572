// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.22.0
// 	protoc        v3.11.2
// source: google/ads/googleads/v1/services/conversion_adjustment_upload_service.proto

package services

import (
	context "context"
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	enums "google.golang.org/genproto/googleapis/ads/googleads/v1/enums"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	status "google.golang.org/genproto/googleapis/rpc/status"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status1 "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// Request message for
// [ConversionAdjustmentUploadService.UploadConversionAdjustments][google.ads.googleads.v1.services.ConversionAdjustmentUploadService.UploadConversionAdjustments].
type UploadConversionAdjustmentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The ID of the customer performing the upload.
	CustomerId string `protobuf:"bytes,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Required. The conversion adjustments that are being uploaded.
	ConversionAdjustments []*ConversionAdjustment `protobuf:"bytes,2,rep,name=conversion_adjustments,json=conversionAdjustments,proto3" json:"conversion_adjustments,omitempty"`
	// Required. If true, successful operations will be carried out and invalid
	// operations will return errors. If false, all operations will be carried out
	// in one transaction if and only if they are all valid. This should always be
	// set to true.
	PartialFailure bool `protobuf:"varint,3,opt,name=partial_failure,json=partialFailure,proto3" json:"partial_failure,omitempty"`
	// If true, the request is validated but not executed. Only errors are
	// returned, not results.
	ValidateOnly bool `protobuf:"varint,4,opt,name=validate_only,json=validateOnly,proto3" json:"validate_only,omitempty"`
}

func (x *UploadConversionAdjustmentsRequest) Reset() {
	*x = UploadConversionAdjustmentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadConversionAdjustmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadConversionAdjustmentsRequest) ProtoMessage() {}

func (x *UploadConversionAdjustmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadConversionAdjustmentsRequest.ProtoReflect.Descriptor instead.
func (*UploadConversionAdjustmentsRequest) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDescGZIP(), []int{0}
}

func (x *UploadConversionAdjustmentsRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *UploadConversionAdjustmentsRequest) GetConversionAdjustments() []*ConversionAdjustment {
	if x != nil {
		return x.ConversionAdjustments
	}
	return nil
}

func (x *UploadConversionAdjustmentsRequest) GetPartialFailure() bool {
	if x != nil {
		return x.PartialFailure
	}
	return false
}

func (x *UploadConversionAdjustmentsRequest) GetValidateOnly() bool {
	if x != nil {
		return x.ValidateOnly
	}
	return false
}

// Response message for
// [ConversionAdjustmentUploadService.UploadConversionAdjustments][google.ads.googleads.v1.services.ConversionAdjustmentUploadService.UploadConversionAdjustments].
type UploadConversionAdjustmentsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Errors that pertain to conversion adjustment failures in the partial
	// failure mode. Returned when all errors occur inside the adjustments. If any
	// errors occur outside the adjustments (e.g. auth errors), we return an RPC
	// level error.
	PartialFailureError *status.Status `protobuf:"bytes,1,opt,name=partial_failure_error,json=partialFailureError,proto3" json:"partial_failure_error,omitempty"`
	// Returned for successfully processed conversion adjustments. Proto will be
	// empty for rows that received an error. Results are not returned when
	// validate_only is true.
	Results []*ConversionAdjustmentResult `protobuf:"bytes,2,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *UploadConversionAdjustmentsResponse) Reset() {
	*x = UploadConversionAdjustmentsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadConversionAdjustmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadConversionAdjustmentsResponse) ProtoMessage() {}

func (x *UploadConversionAdjustmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadConversionAdjustmentsResponse.ProtoReflect.Descriptor instead.
func (*UploadConversionAdjustmentsResponse) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDescGZIP(), []int{1}
}

func (x *UploadConversionAdjustmentsResponse) GetPartialFailureError() *status.Status {
	if x != nil {
		return x.PartialFailureError
	}
	return nil
}

func (x *UploadConversionAdjustmentsResponse) GetResults() []*ConversionAdjustmentResult {
	if x != nil {
		return x.Results
	}
	return nil
}

// A conversion adjustment.
type ConversionAdjustment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Resource name of the conversion action associated with this conversion
	// adjustment. Note: Although this resource name consists of a customer id and
	// a conversion action id, validation will ignore the customer id and use the
	// conversion action id as the sole identifier of the conversion action.
	ConversionAction *wrappers.StringValue `protobuf:"bytes,3,opt,name=conversion_action,json=conversionAction,proto3" json:"conversion_action,omitempty"`
	// The date time at which the adjustment occurred. Must be after the
	// conversion_date_time. The timezone must be specified. The format is
	// "yyyy-mm-dd hh:mm:ss+|-hh:mm", e.g. "2019-01-01 12:32:45-08:00".
	AdjustmentDateTime *wrappers.StringValue `protobuf:"bytes,4,opt,name=adjustment_date_time,json=adjustmentDateTime,proto3" json:"adjustment_date_time,omitempty"`
	// The adjustment type.
	AdjustmentType enums.ConversionAdjustmentTypeEnum_ConversionAdjustmentType `protobuf:"varint,5,opt,name=adjustment_type,json=adjustmentType,proto3,enum=google.ads.googleads.v1.enums.ConversionAdjustmentTypeEnum_ConversionAdjustmentType" json:"adjustment_type,omitempty"`
	// Information needed to restate the conversion's value.
	// Required for restatements. Should not be supplied for retractions. An error
	// will be returned if provided for a retraction.
	RestatementValue *RestatementValue `protobuf:"bytes,6,opt,name=restatement_value,json=restatementValue,proto3" json:"restatement_value,omitempty"`
	// Identifies the conversion to be adjusted.
	//
	// Types that are assignable to ConversionIdentifier:
	//	*ConversionAdjustment_GclidDateTimePair
	//	*ConversionAdjustment_OrderId
	ConversionIdentifier isConversionAdjustment_ConversionIdentifier `protobuf_oneof:"conversion_identifier"`
}

func (x *ConversionAdjustment) Reset() {
	*x = ConversionAdjustment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConversionAdjustment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConversionAdjustment) ProtoMessage() {}

func (x *ConversionAdjustment) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConversionAdjustment.ProtoReflect.Descriptor instead.
func (*ConversionAdjustment) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDescGZIP(), []int{2}
}

func (x *ConversionAdjustment) GetConversionAction() *wrappers.StringValue {
	if x != nil {
		return x.ConversionAction
	}
	return nil
}

func (x *ConversionAdjustment) GetAdjustmentDateTime() *wrappers.StringValue {
	if x != nil {
		return x.AdjustmentDateTime
	}
	return nil
}

func (x *ConversionAdjustment) GetAdjustmentType() enums.ConversionAdjustmentTypeEnum_ConversionAdjustmentType {
	if x != nil {
		return x.AdjustmentType
	}
	return enums.ConversionAdjustmentTypeEnum_UNSPECIFIED
}

func (x *ConversionAdjustment) GetRestatementValue() *RestatementValue {
	if x != nil {
		return x.RestatementValue
	}
	return nil
}

func (m *ConversionAdjustment) GetConversionIdentifier() isConversionAdjustment_ConversionIdentifier {
	if m != nil {
		return m.ConversionIdentifier
	}
	return nil
}

func (x *ConversionAdjustment) GetGclidDateTimePair() *GclidDateTimePair {
	if x, ok := x.GetConversionIdentifier().(*ConversionAdjustment_GclidDateTimePair); ok {
		return x.GclidDateTimePair
	}
	return nil
}

func (x *ConversionAdjustment) GetOrderId() *wrappers.StringValue {
	if x, ok := x.GetConversionIdentifier().(*ConversionAdjustment_OrderId); ok {
		return x.OrderId
	}
	return nil
}

type isConversionAdjustment_ConversionIdentifier interface {
	isConversionAdjustment_ConversionIdentifier()
}

type ConversionAdjustment_GclidDateTimePair struct {
	// Uniquely identifies a conversion that was reported without an order ID
	// specified.
	GclidDateTimePair *GclidDateTimePair `protobuf:"bytes,1,opt,name=gclid_date_time_pair,json=gclidDateTimePair,proto3,oneof"`
}

type ConversionAdjustment_OrderId struct {
	// The order ID of the conversion to be adjusted. If the conversion was
	// reported with an order ID specified, that order ID must be used as the
	// identifier here.
	OrderId *wrappers.StringValue `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3,oneof"`
}

func (*ConversionAdjustment_GclidDateTimePair) isConversionAdjustment_ConversionIdentifier() {}

func (*ConversionAdjustment_OrderId) isConversionAdjustment_ConversionIdentifier() {}

// Contains information needed to restate a conversion's value.
type RestatementValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The restated conversion value. This is the value of the conversion after
	// restatement. For example, to change the value of a conversion from 100 to
	// 70, an adjusted value of 70 should be reported.
	AdjustedValue *wrappers.DoubleValue `protobuf:"bytes,1,opt,name=adjusted_value,json=adjustedValue,proto3" json:"adjusted_value,omitempty"`
	// The currency of the restated value. If not provided, then the default
	// currency from the conversion action is used, and if that is not set then
	// the account currency is used. This is the ISO 4217 3-character currency
	// code e.g. USD or EUR.
	CurrencyCode *wrappers.StringValue `protobuf:"bytes,2,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
}

func (x *RestatementValue) Reset() {
	*x = RestatementValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RestatementValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestatementValue) ProtoMessage() {}

func (x *RestatementValue) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestatementValue.ProtoReflect.Descriptor instead.
func (*RestatementValue) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDescGZIP(), []int{3}
}

func (x *RestatementValue) GetAdjustedValue() *wrappers.DoubleValue {
	if x != nil {
		return x.AdjustedValue
	}
	return nil
}

func (x *RestatementValue) GetCurrencyCode() *wrappers.StringValue {
	if x != nil {
		return x.CurrencyCode
	}
	return nil
}

// Uniquely identifies a conversion that was reported without an order ID
// specified.
type GclidDateTimePair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Google click ID (gclid) associated with the original conversion for this
	// adjustment.
	Gclid *wrappers.StringValue `protobuf:"bytes,1,opt,name=gclid,proto3" json:"gclid,omitempty"`
	// The date time at which the original conversion for this adjustment
	// occurred. The timezone must be specified. The format is "yyyy-mm-dd
	// hh:mm:ss+|-hh:mm", e.g. "2019-01-01 12:32:45-08:00".
	ConversionDateTime *wrappers.StringValue `protobuf:"bytes,2,opt,name=conversion_date_time,json=conversionDateTime,proto3" json:"conversion_date_time,omitempty"`
}

func (x *GclidDateTimePair) Reset() {
	*x = GclidDateTimePair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GclidDateTimePair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GclidDateTimePair) ProtoMessage() {}

func (x *GclidDateTimePair) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GclidDateTimePair.ProtoReflect.Descriptor instead.
func (*GclidDateTimePair) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDescGZIP(), []int{4}
}

func (x *GclidDateTimePair) GetGclid() *wrappers.StringValue {
	if x != nil {
		return x.Gclid
	}
	return nil
}

func (x *GclidDateTimePair) GetConversionDateTime() *wrappers.StringValue {
	if x != nil {
		return x.ConversionDateTime
	}
	return nil
}

// Information identifying a successfully processed ConversionAdjustment.
type ConversionAdjustmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Resource name of the conversion action associated with this conversion
	// adjustment.
	ConversionAction *wrappers.StringValue `protobuf:"bytes,3,opt,name=conversion_action,json=conversionAction,proto3" json:"conversion_action,omitempty"`
	// The date time at which the adjustment occurred. The format is
	// "yyyy-mm-dd hh:mm:ss+|-hh:mm", e.g. "2019-01-01 12:32:45-08:00".
	AdjustmentDateTime *wrappers.StringValue `protobuf:"bytes,4,opt,name=adjustment_date_time,json=adjustmentDateTime,proto3" json:"adjustment_date_time,omitempty"`
	// The adjustment type.
	AdjustmentType enums.ConversionAdjustmentTypeEnum_ConversionAdjustmentType `protobuf:"varint,5,opt,name=adjustment_type,json=adjustmentType,proto3,enum=google.ads.googleads.v1.enums.ConversionAdjustmentTypeEnum_ConversionAdjustmentType" json:"adjustment_type,omitempty"`
	// Identifies the conversion that was adjusted.
	//
	// Types that are assignable to ConversionIdentifier:
	//	*ConversionAdjustmentResult_GclidDateTimePair
	//	*ConversionAdjustmentResult_OrderId
	ConversionIdentifier isConversionAdjustmentResult_ConversionIdentifier `protobuf_oneof:"conversion_identifier"`
}

func (x *ConversionAdjustmentResult) Reset() {
	*x = ConversionAdjustmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConversionAdjustmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConversionAdjustmentResult) ProtoMessage() {}

func (x *ConversionAdjustmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConversionAdjustmentResult.ProtoReflect.Descriptor instead.
func (*ConversionAdjustmentResult) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDescGZIP(), []int{5}
}

func (x *ConversionAdjustmentResult) GetConversionAction() *wrappers.StringValue {
	if x != nil {
		return x.ConversionAction
	}
	return nil
}

func (x *ConversionAdjustmentResult) GetAdjustmentDateTime() *wrappers.StringValue {
	if x != nil {
		return x.AdjustmentDateTime
	}
	return nil
}

func (x *ConversionAdjustmentResult) GetAdjustmentType() enums.ConversionAdjustmentTypeEnum_ConversionAdjustmentType {
	if x != nil {
		return x.AdjustmentType
	}
	return enums.ConversionAdjustmentTypeEnum_UNSPECIFIED
}

func (m *ConversionAdjustmentResult) GetConversionIdentifier() isConversionAdjustmentResult_ConversionIdentifier {
	if m != nil {
		return m.ConversionIdentifier
	}
	return nil
}

func (x *ConversionAdjustmentResult) GetGclidDateTimePair() *GclidDateTimePair {
	if x, ok := x.GetConversionIdentifier().(*ConversionAdjustmentResult_GclidDateTimePair); ok {
		return x.GclidDateTimePair
	}
	return nil
}

func (x *ConversionAdjustmentResult) GetOrderId() *wrappers.StringValue {
	if x, ok := x.GetConversionIdentifier().(*ConversionAdjustmentResult_OrderId); ok {
		return x.OrderId
	}
	return nil
}

type isConversionAdjustmentResult_ConversionIdentifier interface {
	isConversionAdjustmentResult_ConversionIdentifier()
}

type ConversionAdjustmentResult_GclidDateTimePair struct {
	// Uniquely identifies a conversion that was reported without an order ID
	// specified.
	GclidDateTimePair *GclidDateTimePair `protobuf:"bytes,1,opt,name=gclid_date_time_pair,json=gclidDateTimePair,proto3,oneof"`
}

type ConversionAdjustmentResult_OrderId struct {
	// The order ID of the conversion that was adjusted.
	OrderId *wrappers.StringValue `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3,oneof"`
}

func (*ConversionAdjustmentResult_GclidDateTimePair) isConversionAdjustmentResult_ConversionIdentifier() {
}

func (*ConversionAdjustmentResult_OrderId) isConversionAdjustmentResult_ConversionIdentifier() {}

var File_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto protoreflect.FileDescriptor

var file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDesc = []byte{
	0x0a, 0x4b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a,
	0x3e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x91, 0x02, 0x0a, 0x22, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x72, 0x0a,
	0x16, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x6a, 0x75,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x2c, 0x0a, 0x0f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x66, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x0e, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x4f, 0x6e, 0x6c, 0x79, 0x22, 0xc5, 0x01, 0x0a, 0x23, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x15,
	0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x13, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x56, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0xcd, 0x04, 0x0a,
	0x14, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x4e, 0x0a, 0x14, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x12, 0x61, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x7d, 0x0a, 0x0f, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x54, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0e, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x5f, 0x0a, 0x11, 0x72, 0x65, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x52, 0x65,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10,
	0x72, 0x65, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x66, 0x0a, 0x14, 0x67, 0x63, 0x6c, 0x69, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x47, 0x63, 0x6c, 0x69, 0x64, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50,
	0x61, 0x69, 0x72, 0x48, 0x00, 0x52, 0x11, 0x67, 0x63, 0x6c, 0x69, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x69, 0x72, 0x12, 0x39, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x42, 0x17, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x9a, 0x01, 0x0a,
	0x10, 0x52, 0x65, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x43, 0x0a, 0x0e, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x65,
	0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x97, 0x01, 0x0a, 0x11, 0x47, 0x63,
	0x6c, 0x69, 0x64, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x69, 0x72, 0x12,
	0x32, 0x0a, 0x05, 0x67, 0x63, 0x6c, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x67, 0x63,
	0x6c, 0x69, 0x64, 0x12, 0x4e, 0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x12, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0xf2, 0x03, 0x0a, 0x1a, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x49, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a,
	0x14, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x12, 0x61, 0x64, 0x6a, 0x75, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x7d, 0x0a,
	0x0f, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x54, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x45,
	0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x61, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x66, 0x0a, 0x14,
	0x67, 0x63, 0x6c, 0x69, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x70, 0x61, 0x69, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47, 0x63,
	0x6c, 0x69, 0x64, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x69, 0x72, 0x48,
	0x00, 0x52, 0x11, 0x67, 0x63, 0x6c, 0x69, 0x64, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x50, 0x61, 0x69, 0x72, 0x12, 0x39, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x42,
	0x17, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x32, 0xe8, 0x02, 0x0a, 0x21, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa5,
	0x02, 0x0a, 0x1b, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x44,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64,
	0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x79, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x3e, 0x22, 0x39, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x73, 0x2f, 0x7b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x3d,
	0x2a, 0x7d, 0x3a, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x3a, 0x01,
	0x2a, 0xda, 0x41, 0x32, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x2c,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x6a, 0x75, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2c, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x1a, 0x1b, 0xca, 0x41, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x42, 0x8d, 0x02, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x42, 0x26, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x48, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67,
	0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x64,
	0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x3b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0xa2, 0x02, 0x03, 0x47, 0x41, 0x41, 0xaa, 0x02, 0x20, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x41, 0x64, 0x73, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x2e, 0x56, 0x31,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0xca, 0x02, 0x20, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x5c, 0x41, 0x64, 0x73, 0x5c, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73,
	0x5c, 0x56, 0x31, 0x5c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0xea, 0x02, 0x24, 0x47,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x41, 0x64, 0x73, 0x3a, 0x3a, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x41, 0x64, 0x73, 0x3a, 0x3a, 0x56, 0x31, 0x3a, 0x3a, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDescOnce sync.Once
	file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDescData = file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDesc
)

func file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDescGZIP() []byte {
	file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDescOnce.Do(func() {
		file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDescData)
	})
	return file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDescData
}

var file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_goTypes = []interface{}{
	(*UploadConversionAdjustmentsRequest)(nil),                       // 0: google.ads.googleads.v1.services.UploadConversionAdjustmentsRequest
	(*UploadConversionAdjustmentsResponse)(nil),                      // 1: google.ads.googleads.v1.services.UploadConversionAdjustmentsResponse
	(*ConversionAdjustment)(nil),                                     // 2: google.ads.googleads.v1.services.ConversionAdjustment
	(*RestatementValue)(nil),                                         // 3: google.ads.googleads.v1.services.RestatementValue
	(*GclidDateTimePair)(nil),                                        // 4: google.ads.googleads.v1.services.GclidDateTimePair
	(*ConversionAdjustmentResult)(nil),                               // 5: google.ads.googleads.v1.services.ConversionAdjustmentResult
	(*status.Status)(nil),                                            // 6: google.rpc.Status
	(*wrappers.StringValue)(nil),                                     // 7: google.protobuf.StringValue
	(enums.ConversionAdjustmentTypeEnum_ConversionAdjustmentType)(0), // 8: google.ads.googleads.v1.enums.ConversionAdjustmentTypeEnum.ConversionAdjustmentType
	(*wrappers.DoubleValue)(nil),                                     // 9: google.protobuf.DoubleValue
}
var file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_depIdxs = []int32{
	2,  // 0: google.ads.googleads.v1.services.UploadConversionAdjustmentsRequest.conversion_adjustments:type_name -> google.ads.googleads.v1.services.ConversionAdjustment
	6,  // 1: google.ads.googleads.v1.services.UploadConversionAdjustmentsResponse.partial_failure_error:type_name -> google.rpc.Status
	5,  // 2: google.ads.googleads.v1.services.UploadConversionAdjustmentsResponse.results:type_name -> google.ads.googleads.v1.services.ConversionAdjustmentResult
	7,  // 3: google.ads.googleads.v1.services.ConversionAdjustment.conversion_action:type_name -> google.protobuf.StringValue
	7,  // 4: google.ads.googleads.v1.services.ConversionAdjustment.adjustment_date_time:type_name -> google.protobuf.StringValue
	8,  // 5: google.ads.googleads.v1.services.ConversionAdjustment.adjustment_type:type_name -> google.ads.googleads.v1.enums.ConversionAdjustmentTypeEnum.ConversionAdjustmentType
	3,  // 6: google.ads.googleads.v1.services.ConversionAdjustment.restatement_value:type_name -> google.ads.googleads.v1.services.RestatementValue
	4,  // 7: google.ads.googleads.v1.services.ConversionAdjustment.gclid_date_time_pair:type_name -> google.ads.googleads.v1.services.GclidDateTimePair
	7,  // 8: google.ads.googleads.v1.services.ConversionAdjustment.order_id:type_name -> google.protobuf.StringValue
	9,  // 9: google.ads.googleads.v1.services.RestatementValue.adjusted_value:type_name -> google.protobuf.DoubleValue
	7,  // 10: google.ads.googleads.v1.services.RestatementValue.currency_code:type_name -> google.protobuf.StringValue
	7,  // 11: google.ads.googleads.v1.services.GclidDateTimePair.gclid:type_name -> google.protobuf.StringValue
	7,  // 12: google.ads.googleads.v1.services.GclidDateTimePair.conversion_date_time:type_name -> google.protobuf.StringValue
	7,  // 13: google.ads.googleads.v1.services.ConversionAdjustmentResult.conversion_action:type_name -> google.protobuf.StringValue
	7,  // 14: google.ads.googleads.v1.services.ConversionAdjustmentResult.adjustment_date_time:type_name -> google.protobuf.StringValue
	8,  // 15: google.ads.googleads.v1.services.ConversionAdjustmentResult.adjustment_type:type_name -> google.ads.googleads.v1.enums.ConversionAdjustmentTypeEnum.ConversionAdjustmentType
	4,  // 16: google.ads.googleads.v1.services.ConversionAdjustmentResult.gclid_date_time_pair:type_name -> google.ads.googleads.v1.services.GclidDateTimePair
	7,  // 17: google.ads.googleads.v1.services.ConversionAdjustmentResult.order_id:type_name -> google.protobuf.StringValue
	0,  // 18: google.ads.googleads.v1.services.ConversionAdjustmentUploadService.UploadConversionAdjustments:input_type -> google.ads.googleads.v1.services.UploadConversionAdjustmentsRequest
	1,  // 19: google.ads.googleads.v1.services.ConversionAdjustmentUploadService.UploadConversionAdjustments:output_type -> google.ads.googleads.v1.services.UploadConversionAdjustmentsResponse
	19, // [19:20] is the sub-list for method output_type
	18, // [18:19] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_init() }
func file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_init() {
	if File_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadConversionAdjustmentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadConversionAdjustmentsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConversionAdjustment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RestatementValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GclidDateTimePair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConversionAdjustmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*ConversionAdjustment_GclidDateTimePair)(nil),
		(*ConversionAdjustment_OrderId)(nil),
	}
	file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*ConversionAdjustmentResult_GclidDateTimePair)(nil),
		(*ConversionAdjustmentResult_OrderId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_goTypes,
		DependencyIndexes: file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_depIdxs,
		MessageInfos:      file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_msgTypes,
	}.Build()
	File_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto = out.File
	file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_rawDesc = nil
	file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_goTypes = nil
	file_google_ads_googleads_v1_services_conversion_adjustment_upload_service_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// ConversionAdjustmentUploadServiceClient is the client API for ConversionAdjustmentUploadService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ConversionAdjustmentUploadServiceClient interface {
	// Processes the given conversion adjustments.
	UploadConversionAdjustments(ctx context.Context, in *UploadConversionAdjustmentsRequest, opts ...grpc.CallOption) (*UploadConversionAdjustmentsResponse, error)
}

type conversionAdjustmentUploadServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewConversionAdjustmentUploadServiceClient(cc grpc.ClientConnInterface) ConversionAdjustmentUploadServiceClient {
	return &conversionAdjustmentUploadServiceClient{cc}
}

func (c *conversionAdjustmentUploadServiceClient) UploadConversionAdjustments(ctx context.Context, in *UploadConversionAdjustmentsRequest, opts ...grpc.CallOption) (*UploadConversionAdjustmentsResponse, error) {
	out := new(UploadConversionAdjustmentsResponse)
	err := c.cc.Invoke(ctx, "/google.ads.googleads.v1.services.ConversionAdjustmentUploadService/UploadConversionAdjustments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConversionAdjustmentUploadServiceServer is the server API for ConversionAdjustmentUploadService service.
type ConversionAdjustmentUploadServiceServer interface {
	// Processes the given conversion adjustments.
	UploadConversionAdjustments(context.Context, *UploadConversionAdjustmentsRequest) (*UploadConversionAdjustmentsResponse, error)
}

// UnimplementedConversionAdjustmentUploadServiceServer can be embedded to have forward compatible implementations.
type UnimplementedConversionAdjustmentUploadServiceServer struct {
}

func (*UnimplementedConversionAdjustmentUploadServiceServer) UploadConversionAdjustments(context.Context, *UploadConversionAdjustmentsRequest) (*UploadConversionAdjustmentsResponse, error) {
	return nil, status1.Errorf(codes.Unimplemented, "method UploadConversionAdjustments not implemented")
}

func RegisterConversionAdjustmentUploadServiceServer(s *grpc.Server, srv ConversionAdjustmentUploadServiceServer) {
	s.RegisterService(&_ConversionAdjustmentUploadService_serviceDesc, srv)
}

func _ConversionAdjustmentUploadService_UploadConversionAdjustments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadConversionAdjustmentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionAdjustmentUploadServiceServer).UploadConversionAdjustments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.ads.googleads.v1.services.ConversionAdjustmentUploadService/UploadConversionAdjustments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionAdjustmentUploadServiceServer).UploadConversionAdjustments(ctx, req.(*UploadConversionAdjustmentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ConversionAdjustmentUploadService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "google.ads.googleads.v1.services.ConversionAdjustmentUploadService",
	HandlerType: (*ConversionAdjustmentUploadServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UploadConversionAdjustments",
			Handler:    _ConversionAdjustmentUploadService_UploadConversionAdjustments_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "google/ads/googleads/v1/services/conversion_adjustment_upload_service.proto",
}
