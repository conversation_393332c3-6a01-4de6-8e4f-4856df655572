// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.22.0
// 	protoc        v3.11.2
// source: google/ads/googleads/v1/common/ad_type_infos.proto

package common

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	enums "google.golang.org/genproto/googleapis/ads/googleads/v1/enums"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// A text ad.
type TextAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The headline of the ad.
	Headline *wrappers.StringValue `protobuf:"bytes,1,opt,name=headline,proto3" json:"headline,omitempty"`
	// The first line of the ad's description.
	Description1 *wrappers.StringValue `protobuf:"bytes,2,opt,name=description1,proto3" json:"description1,omitempty"`
	// The second line of the ad's description.
	Description2 *wrappers.StringValue `protobuf:"bytes,3,opt,name=description2,proto3" json:"description2,omitempty"`
}

func (x *TextAdInfo) Reset() {
	*x = TextAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextAdInfo) ProtoMessage() {}

func (x *TextAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextAdInfo.ProtoReflect.Descriptor instead.
func (*TextAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{0}
}

func (x *TextAdInfo) GetHeadline() *wrappers.StringValue {
	if x != nil {
		return x.Headline
	}
	return nil
}

func (x *TextAdInfo) GetDescription1() *wrappers.StringValue {
	if x != nil {
		return x.Description1
	}
	return nil
}

func (x *TextAdInfo) GetDescription2() *wrappers.StringValue {
	if x != nil {
		return x.Description2
	}
	return nil
}

// An expanded text ad.
type ExpandedTextAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The first part of the ad's headline.
	HeadlinePart1 *wrappers.StringValue `protobuf:"bytes,1,opt,name=headline_part1,json=headlinePart1,proto3" json:"headline_part1,omitempty"`
	// The second part of the ad's headline.
	HeadlinePart2 *wrappers.StringValue `protobuf:"bytes,2,opt,name=headline_part2,json=headlinePart2,proto3" json:"headline_part2,omitempty"`
	// The third part of the ad's headline.
	HeadlinePart3 *wrappers.StringValue `protobuf:"bytes,6,opt,name=headline_part3,json=headlinePart3,proto3" json:"headline_part3,omitempty"`
	// The description of the ad.
	Description *wrappers.StringValue `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// The second description of the ad.
	Description2 *wrappers.StringValue `protobuf:"bytes,7,opt,name=description2,proto3" json:"description2,omitempty"`
	// The text that can appear alongside the ad's displayed URL.
	Path1 *wrappers.StringValue `protobuf:"bytes,4,opt,name=path1,proto3" json:"path1,omitempty"`
	// Additional text that can appear alongside the ad's displayed URL.
	Path2 *wrappers.StringValue `protobuf:"bytes,5,opt,name=path2,proto3" json:"path2,omitempty"`
}

func (x *ExpandedTextAdInfo) Reset() {
	*x = ExpandedTextAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpandedTextAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpandedTextAdInfo) ProtoMessage() {}

func (x *ExpandedTextAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpandedTextAdInfo.ProtoReflect.Descriptor instead.
func (*ExpandedTextAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{1}
}

func (x *ExpandedTextAdInfo) GetHeadlinePart1() *wrappers.StringValue {
	if x != nil {
		return x.HeadlinePart1
	}
	return nil
}

func (x *ExpandedTextAdInfo) GetHeadlinePart2() *wrappers.StringValue {
	if x != nil {
		return x.HeadlinePart2
	}
	return nil
}

func (x *ExpandedTextAdInfo) GetHeadlinePart3() *wrappers.StringValue {
	if x != nil {
		return x.HeadlinePart3
	}
	return nil
}

func (x *ExpandedTextAdInfo) GetDescription() *wrappers.StringValue {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *ExpandedTextAdInfo) GetDescription2() *wrappers.StringValue {
	if x != nil {
		return x.Description2
	}
	return nil
}

func (x *ExpandedTextAdInfo) GetPath1() *wrappers.StringValue {
	if x != nil {
		return x.Path1
	}
	return nil
}

func (x *ExpandedTextAdInfo) GetPath2() *wrappers.StringValue {
	if x != nil {
		return x.Path2
	}
	return nil
}

// A call-only ad.
type CallOnlyAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The country code in the ad.
	CountryCode *wrappers.StringValue `protobuf:"bytes,1,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// The phone number in the ad.
	PhoneNumber *wrappers.StringValue `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// The business name in the ad.
	BusinessName *wrappers.StringValue `protobuf:"bytes,3,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// First headline in the ad.
	Headline1 *wrappers.StringValue `protobuf:"bytes,11,opt,name=headline1,proto3" json:"headline1,omitempty"`
	// Second headline in the ad.
	Headline2 *wrappers.StringValue `protobuf:"bytes,12,opt,name=headline2,proto3" json:"headline2,omitempty"`
	// The first line of the ad's description.
	Description1 *wrappers.StringValue `protobuf:"bytes,4,opt,name=description1,proto3" json:"description1,omitempty"`
	// The second line of the ad's description.
	Description2 *wrappers.StringValue `protobuf:"bytes,5,opt,name=description2,proto3" json:"description2,omitempty"`
	// Whether to enable call tracking for the creative. Enabling call
	// tracking also enables call conversions.
	CallTracked *wrappers.BoolValue `protobuf:"bytes,6,opt,name=call_tracked,json=callTracked,proto3" json:"call_tracked,omitempty"`
	// Whether to disable call conversion for the creative.
	// If set to `true`, disables call conversions even when `call_tracked` is
	// `true`.
	// If `call_tracked` is `false`, this field is ignored.
	DisableCallConversion *wrappers.BoolValue `protobuf:"bytes,7,opt,name=disable_call_conversion,json=disableCallConversion,proto3" json:"disable_call_conversion,omitempty"`
	// The URL to be used for phone number verification.
	PhoneNumberVerificationUrl *wrappers.StringValue `protobuf:"bytes,8,opt,name=phone_number_verification_url,json=phoneNumberVerificationUrl,proto3" json:"phone_number_verification_url,omitempty"`
	// The conversion action to attribute a call conversion to. If not set a
	// default conversion action is used. This field only has effect if
	// call_tracked is set to true. Otherwise this field is ignored.
	ConversionAction *wrappers.StringValue `protobuf:"bytes,9,opt,name=conversion_action,json=conversionAction,proto3" json:"conversion_action,omitempty"`
	// The call conversion behavior of this call only ad. It can use its own call
	// conversion setting, inherit the account level setting, or be disabled.
	ConversionReportingState enums.CallConversionReportingStateEnum_CallConversionReportingState `protobuf:"varint,10,opt,name=conversion_reporting_state,json=conversionReportingState,proto3,enum=google.ads.googleads.v1.enums.CallConversionReportingStateEnum_CallConversionReportingState" json:"conversion_reporting_state,omitempty"`
}

func (x *CallOnlyAdInfo) Reset() {
	*x = CallOnlyAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallOnlyAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallOnlyAdInfo) ProtoMessage() {}

func (x *CallOnlyAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallOnlyAdInfo.ProtoReflect.Descriptor instead.
func (*CallOnlyAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{2}
}

func (x *CallOnlyAdInfo) GetCountryCode() *wrappers.StringValue {
	if x != nil {
		return x.CountryCode
	}
	return nil
}

func (x *CallOnlyAdInfo) GetPhoneNumber() *wrappers.StringValue {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *CallOnlyAdInfo) GetBusinessName() *wrappers.StringValue {
	if x != nil {
		return x.BusinessName
	}
	return nil
}

func (x *CallOnlyAdInfo) GetHeadline1() *wrappers.StringValue {
	if x != nil {
		return x.Headline1
	}
	return nil
}

func (x *CallOnlyAdInfo) GetHeadline2() *wrappers.StringValue {
	if x != nil {
		return x.Headline2
	}
	return nil
}

func (x *CallOnlyAdInfo) GetDescription1() *wrappers.StringValue {
	if x != nil {
		return x.Description1
	}
	return nil
}

func (x *CallOnlyAdInfo) GetDescription2() *wrappers.StringValue {
	if x != nil {
		return x.Description2
	}
	return nil
}

func (x *CallOnlyAdInfo) GetCallTracked() *wrappers.BoolValue {
	if x != nil {
		return x.CallTracked
	}
	return nil
}

func (x *CallOnlyAdInfo) GetDisableCallConversion() *wrappers.BoolValue {
	if x != nil {
		return x.DisableCallConversion
	}
	return nil
}

func (x *CallOnlyAdInfo) GetPhoneNumberVerificationUrl() *wrappers.StringValue {
	if x != nil {
		return x.PhoneNumberVerificationUrl
	}
	return nil
}

func (x *CallOnlyAdInfo) GetConversionAction() *wrappers.StringValue {
	if x != nil {
		return x.ConversionAction
	}
	return nil
}

func (x *CallOnlyAdInfo) GetConversionReportingState() enums.CallConversionReportingStateEnum_CallConversionReportingState {
	if x != nil {
		return x.ConversionReportingState
	}
	return enums.CallConversionReportingStateEnum_UNSPECIFIED
}

// An expanded dynamic search ad.
type ExpandedDynamicSearchAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The description of the ad.
	Description *wrappers.StringValue `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *ExpandedDynamicSearchAdInfo) Reset() {
	*x = ExpandedDynamicSearchAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpandedDynamicSearchAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpandedDynamicSearchAdInfo) ProtoMessage() {}

func (x *ExpandedDynamicSearchAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpandedDynamicSearchAdInfo.ProtoReflect.Descriptor instead.
func (*ExpandedDynamicSearchAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{3}
}

func (x *ExpandedDynamicSearchAdInfo) GetDescription() *wrappers.StringValue {
	if x != nil {
		return x.Description
	}
	return nil
}

// A hotel ad.
type HotelAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HotelAdInfo) Reset() {
	*x = HotelAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotelAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotelAdInfo) ProtoMessage() {}

func (x *HotelAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotelAdInfo.ProtoReflect.Descriptor instead.
func (*HotelAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{4}
}

// A Smart Shopping ad.
type ShoppingSmartAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ShoppingSmartAdInfo) Reset() {
	*x = ShoppingSmartAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShoppingSmartAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShoppingSmartAdInfo) ProtoMessage() {}

func (x *ShoppingSmartAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShoppingSmartAdInfo.ProtoReflect.Descriptor instead.
func (*ShoppingSmartAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{5}
}

// A standard Shopping ad.
type ShoppingProductAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ShoppingProductAdInfo) Reset() {
	*x = ShoppingProductAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShoppingProductAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShoppingProductAdInfo) ProtoMessage() {}

func (x *ShoppingProductAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShoppingProductAdInfo.ProtoReflect.Descriptor instead.
func (*ShoppingProductAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{6}
}

// A Shopping Comparison Listing ad.
type ShoppingComparisonListingAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Headline of the ad. This field is required. Allowed length is between 25
	// and 45 characters.
	Headline *wrappers.StringValue `protobuf:"bytes,1,opt,name=headline,proto3" json:"headline,omitempty"`
}

func (x *ShoppingComparisonListingAdInfo) Reset() {
	*x = ShoppingComparisonListingAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShoppingComparisonListingAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShoppingComparisonListingAdInfo) ProtoMessage() {}

func (x *ShoppingComparisonListingAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShoppingComparisonListingAdInfo.ProtoReflect.Descriptor instead.
func (*ShoppingComparisonListingAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{7}
}

func (x *ShoppingComparisonListingAdInfo) GetHeadline() *wrappers.StringValue {
	if x != nil {
		return x.Headline
	}
	return nil
}

// A Gmail ad.
type GmailAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Gmail teaser.
	Teaser *GmailTeaser `protobuf:"bytes,1,opt,name=teaser,proto3" json:"teaser,omitempty"`
	// The MediaFile resource name of the header image. Valid image types are GIF,
	// JPEG and PNG. The minimum size is 300x100 pixels and the aspect ratio must
	// be between 3:1 and 5:1 (+-1%).
	HeaderImage *wrappers.StringValue `protobuf:"bytes,2,opt,name=header_image,json=headerImage,proto3" json:"header_image,omitempty"`
	// The MediaFile resource name of the marketing image. Valid image types are
	// GIF, JPEG and PNG. The image must either be landscape with a minimum size
	// of 600x314 pixels and aspect ratio of 600:314 (+-1%) or square with a
	// minimum size of 300x300 pixels and aspect ratio of 1:1 (+-1%)
	MarketingImage *wrappers.StringValue `protobuf:"bytes,3,opt,name=marketing_image,json=marketingImage,proto3" json:"marketing_image,omitempty"`
	// Headline of the marketing image.
	MarketingImageHeadline *wrappers.StringValue `protobuf:"bytes,4,opt,name=marketing_image_headline,json=marketingImageHeadline,proto3" json:"marketing_image_headline,omitempty"`
	// Description of the marketing image.
	MarketingImageDescription *wrappers.StringValue `protobuf:"bytes,5,opt,name=marketing_image_description,json=marketingImageDescription,proto3" json:"marketing_image_description,omitempty"`
	// Display-call-to-action of the marketing image.
	MarketingImageDisplayCallToAction *DisplayCallToAction `protobuf:"bytes,6,opt,name=marketing_image_display_call_to_action,json=marketingImageDisplayCallToAction,proto3" json:"marketing_image_display_call_to_action,omitempty"`
	// Product images. Up to 15 images are supported.
	ProductImages []*ProductImage `protobuf:"bytes,7,rep,name=product_images,json=productImages,proto3" json:"product_images,omitempty"`
	// Product videos. Up to 7 videos are supported. At least one product video
	// or a marketing image must be specified.
	ProductVideos []*ProductVideo `protobuf:"bytes,8,rep,name=product_videos,json=productVideos,proto3" json:"product_videos,omitempty"`
}

func (x *GmailAdInfo) Reset() {
	*x = GmailAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmailAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmailAdInfo) ProtoMessage() {}

func (x *GmailAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmailAdInfo.ProtoReflect.Descriptor instead.
func (*GmailAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{8}
}

func (x *GmailAdInfo) GetTeaser() *GmailTeaser {
	if x != nil {
		return x.Teaser
	}
	return nil
}

func (x *GmailAdInfo) GetHeaderImage() *wrappers.StringValue {
	if x != nil {
		return x.HeaderImage
	}
	return nil
}

func (x *GmailAdInfo) GetMarketingImage() *wrappers.StringValue {
	if x != nil {
		return x.MarketingImage
	}
	return nil
}

func (x *GmailAdInfo) GetMarketingImageHeadline() *wrappers.StringValue {
	if x != nil {
		return x.MarketingImageHeadline
	}
	return nil
}

func (x *GmailAdInfo) GetMarketingImageDescription() *wrappers.StringValue {
	if x != nil {
		return x.MarketingImageDescription
	}
	return nil
}

func (x *GmailAdInfo) GetMarketingImageDisplayCallToAction() *DisplayCallToAction {
	if x != nil {
		return x.MarketingImageDisplayCallToAction
	}
	return nil
}

func (x *GmailAdInfo) GetProductImages() []*ProductImage {
	if x != nil {
		return x.ProductImages
	}
	return nil
}

func (x *GmailAdInfo) GetProductVideos() []*ProductVideo {
	if x != nil {
		return x.ProductVideos
	}
	return nil
}

// Gmail teaser data. The teaser is a small header that acts as an invitation
// to view the rest of the ad (the body).
type GmailTeaser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Headline of the teaser.
	Headline *wrappers.StringValue `protobuf:"bytes,1,opt,name=headline,proto3" json:"headline,omitempty"`
	// Description of the teaser.
	Description *wrappers.StringValue `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// Business name of the advertiser.
	BusinessName *wrappers.StringValue `protobuf:"bytes,3,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// The MediaFile resource name of the logo image. Valid image types are GIF,
	// JPEG and PNG. The minimum size is 144x144 pixels and the aspect ratio must
	// be 1:1 (+-1%).
	LogoImage *wrappers.StringValue `protobuf:"bytes,4,opt,name=logo_image,json=logoImage,proto3" json:"logo_image,omitempty"`
}

func (x *GmailTeaser) Reset() {
	*x = GmailTeaser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmailTeaser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmailTeaser) ProtoMessage() {}

func (x *GmailTeaser) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmailTeaser.ProtoReflect.Descriptor instead.
func (*GmailTeaser) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{9}
}

func (x *GmailTeaser) GetHeadline() *wrappers.StringValue {
	if x != nil {
		return x.Headline
	}
	return nil
}

func (x *GmailTeaser) GetDescription() *wrappers.StringValue {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *GmailTeaser) GetBusinessName() *wrappers.StringValue {
	if x != nil {
		return x.BusinessName
	}
	return nil
}

func (x *GmailTeaser) GetLogoImage() *wrappers.StringValue {
	if x != nil {
		return x.LogoImage
	}
	return nil
}

// Data for display call to action. The call to action is a piece of the ad
// that prompts the user to do something. Like clicking a link or making a phone
// call.
type DisplayCallToAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Text for the display-call-to-action.
	Text *wrappers.StringValue `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// Text color for the display-call-to-action in hexadecimal, e.g. #ffffff for
	// white.
	TextColor *wrappers.StringValue `protobuf:"bytes,2,opt,name=text_color,json=textColor,proto3" json:"text_color,omitempty"`
	// Identifies the url collection in the ad.url_collections field. If not set
	// the url defaults to final_url.
	UrlCollectionId *wrappers.StringValue `protobuf:"bytes,3,opt,name=url_collection_id,json=urlCollectionId,proto3" json:"url_collection_id,omitempty"`
}

func (x *DisplayCallToAction) Reset() {
	*x = DisplayCallToAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayCallToAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayCallToAction) ProtoMessage() {}

func (x *DisplayCallToAction) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayCallToAction.ProtoReflect.Descriptor instead.
func (*DisplayCallToAction) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{10}
}

func (x *DisplayCallToAction) GetText() *wrappers.StringValue {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *DisplayCallToAction) GetTextColor() *wrappers.StringValue {
	if x != nil {
		return x.TextColor
	}
	return nil
}

func (x *DisplayCallToAction) GetUrlCollectionId() *wrappers.StringValue {
	if x != nil {
		return x.UrlCollectionId
	}
	return nil
}

// Product image specific data.
type ProductImage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The MediaFile resource name of the product image. Valid image types are
	// GIF, JPEG and PNG. The minimum size is 300x300 pixels and the aspect ratio
	// must be 1:1 (+-1%).
	ProductImage *wrappers.StringValue `protobuf:"bytes,1,opt,name=product_image,json=productImage,proto3" json:"product_image,omitempty"`
	// Description of the product.
	Description *wrappers.StringValue `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// Display-call-to-action of the product image.
	DisplayCallToAction *DisplayCallToAction `protobuf:"bytes,3,opt,name=display_call_to_action,json=displayCallToAction,proto3" json:"display_call_to_action,omitempty"`
}

func (x *ProductImage) Reset() {
	*x = ProductImage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductImage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductImage) ProtoMessage() {}

func (x *ProductImage) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductImage.ProtoReflect.Descriptor instead.
func (*ProductImage) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{11}
}

func (x *ProductImage) GetProductImage() *wrappers.StringValue {
	if x != nil {
		return x.ProductImage
	}
	return nil
}

func (x *ProductImage) GetDescription() *wrappers.StringValue {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *ProductImage) GetDisplayCallToAction() *DisplayCallToAction {
	if x != nil {
		return x.DisplayCallToAction
	}
	return nil
}

// Product video specific data.
type ProductVideo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The MediaFile resource name of a video which must be hosted on YouTube.
	ProductVideo *wrappers.StringValue `protobuf:"bytes,1,opt,name=product_video,json=productVideo,proto3" json:"product_video,omitempty"`
}

func (x *ProductVideo) Reset() {
	*x = ProductVideo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductVideo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductVideo) ProtoMessage() {}

func (x *ProductVideo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductVideo.ProtoReflect.Descriptor instead.
func (*ProductVideo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{12}
}

func (x *ProductVideo) GetProductVideo() *wrappers.StringValue {
	if x != nil {
		return x.ProductVideo
	}
	return nil
}

// An image ad.
type ImageAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Width in pixels of the full size image.
	PixelWidth *wrappers.Int64Value `protobuf:"bytes,4,opt,name=pixel_width,json=pixelWidth,proto3" json:"pixel_width,omitempty"`
	// Height in pixels of the full size image.
	PixelHeight *wrappers.Int64Value `protobuf:"bytes,5,opt,name=pixel_height,json=pixelHeight,proto3" json:"pixel_height,omitempty"`
	// URL of the full size image.
	ImageUrl *wrappers.StringValue `protobuf:"bytes,6,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	// Width in pixels of the preview size image.
	PreviewPixelWidth *wrappers.Int64Value `protobuf:"bytes,7,opt,name=preview_pixel_width,json=previewPixelWidth,proto3" json:"preview_pixel_width,omitempty"`
	// Height in pixels of the preview size image.
	PreviewPixelHeight *wrappers.Int64Value `protobuf:"bytes,8,opt,name=preview_pixel_height,json=previewPixelHeight,proto3" json:"preview_pixel_height,omitempty"`
	// URL of the preview size image.
	PreviewImageUrl *wrappers.StringValue `protobuf:"bytes,9,opt,name=preview_image_url,json=previewImageUrl,proto3" json:"preview_image_url,omitempty"`
	// The mime type of the image.
	MimeType enums.MimeTypeEnum_MimeType `protobuf:"varint,10,opt,name=mime_type,json=mimeType,proto3,enum=google.ads.googleads.v1.enums.MimeTypeEnum_MimeType" json:"mime_type,omitempty"`
	// The name of the image. If the image was created from a MediaFile, this is
	// the MediaFile's name. If the image was created from bytes, this is empty.
	Name *wrappers.StringValue `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`
	// The image to create the ImageAd from. This can be specified in one of
	// two ways.
	// 1. An existing MediaFile resource.
	// 2. The raw image data as bytes.
	//
	// Types that are assignable to Image:
	//	*ImageAdInfo_MediaFile
	//	*ImageAdInfo_Data
	//	*ImageAdInfo_AdIdToCopyImageFrom
	Image isImageAdInfo_Image `protobuf_oneof:"image"`
}

func (x *ImageAdInfo) Reset() {
	*x = ImageAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageAdInfo) ProtoMessage() {}

func (x *ImageAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageAdInfo.ProtoReflect.Descriptor instead.
func (*ImageAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{13}
}

func (x *ImageAdInfo) GetPixelWidth() *wrappers.Int64Value {
	if x != nil {
		return x.PixelWidth
	}
	return nil
}

func (x *ImageAdInfo) GetPixelHeight() *wrappers.Int64Value {
	if x != nil {
		return x.PixelHeight
	}
	return nil
}

func (x *ImageAdInfo) GetImageUrl() *wrappers.StringValue {
	if x != nil {
		return x.ImageUrl
	}
	return nil
}

func (x *ImageAdInfo) GetPreviewPixelWidth() *wrappers.Int64Value {
	if x != nil {
		return x.PreviewPixelWidth
	}
	return nil
}

func (x *ImageAdInfo) GetPreviewPixelHeight() *wrappers.Int64Value {
	if x != nil {
		return x.PreviewPixelHeight
	}
	return nil
}

func (x *ImageAdInfo) GetPreviewImageUrl() *wrappers.StringValue {
	if x != nil {
		return x.PreviewImageUrl
	}
	return nil
}

func (x *ImageAdInfo) GetMimeType() enums.MimeTypeEnum_MimeType {
	if x != nil {
		return x.MimeType
	}
	return enums.MimeTypeEnum_UNSPECIFIED
}

func (x *ImageAdInfo) GetName() *wrappers.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (m *ImageAdInfo) GetImage() isImageAdInfo_Image {
	if m != nil {
		return m.Image
	}
	return nil
}

func (x *ImageAdInfo) GetMediaFile() *wrappers.StringValue {
	if x, ok := x.GetImage().(*ImageAdInfo_MediaFile); ok {
		return x.MediaFile
	}
	return nil
}

func (x *ImageAdInfo) GetData() *wrappers.BytesValue {
	if x, ok := x.GetImage().(*ImageAdInfo_Data); ok {
		return x.Data
	}
	return nil
}

func (x *ImageAdInfo) GetAdIdToCopyImageFrom() *wrappers.Int64Value {
	if x, ok := x.GetImage().(*ImageAdInfo_AdIdToCopyImageFrom); ok {
		return x.AdIdToCopyImageFrom
	}
	return nil
}

type isImageAdInfo_Image interface {
	isImageAdInfo_Image()
}

type ImageAdInfo_MediaFile struct {
	// The MediaFile resource to use for the image.
	MediaFile *wrappers.StringValue `protobuf:"bytes,1,opt,name=media_file,json=mediaFile,proto3,oneof"`
}

type ImageAdInfo_Data struct {
	// Raw image data as bytes.
	Data *wrappers.BytesValue `protobuf:"bytes,2,opt,name=data,proto3,oneof"`
}

type ImageAdInfo_AdIdToCopyImageFrom struct {
	// An ad ID to copy the image from.
	AdIdToCopyImageFrom *wrappers.Int64Value `protobuf:"bytes,3,opt,name=ad_id_to_copy_image_from,json=adIdToCopyImageFrom,proto3,oneof"`
}

func (*ImageAdInfo_MediaFile) isImageAdInfo_Image() {}

func (*ImageAdInfo_Data) isImageAdInfo_Image() {}

func (*ImageAdInfo_AdIdToCopyImageFrom) isImageAdInfo_Image() {}

// Representation of video bumper in-stream ad format (very short in-stream
// non-skippable video ad).
type VideoBumperInStreamAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *VideoBumperInStreamAdInfo) Reset() {
	*x = VideoBumperInStreamAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoBumperInStreamAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoBumperInStreamAdInfo) ProtoMessage() {}

func (x *VideoBumperInStreamAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoBumperInStreamAdInfo.ProtoReflect.Descriptor instead.
func (*VideoBumperInStreamAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{14}
}

// Representation of video non-skippable in-stream ad format (15 second
// in-stream non-skippable video ad).
type VideoNonSkippableInStreamAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *VideoNonSkippableInStreamAdInfo) Reset() {
	*x = VideoNonSkippableInStreamAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoNonSkippableInStreamAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoNonSkippableInStreamAdInfo) ProtoMessage() {}

func (x *VideoNonSkippableInStreamAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoNonSkippableInStreamAdInfo.ProtoReflect.Descriptor instead.
func (*VideoNonSkippableInStreamAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{15}
}

// Representation of video TrueView in-stream ad format (ad shown during video
// playback, often at beginning, which displays a skip button a few seconds into
// the video).
type VideoTrueViewInStreamAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Label on the CTA (call-to-action) button taking the user to the video ad's
	// final URL.
	// Required for TrueView for action campaigns, optional otherwise.
	ActionButtonLabel *wrappers.StringValue `protobuf:"bytes,1,opt,name=action_button_label,json=actionButtonLabel,proto3" json:"action_button_label,omitempty"`
	// Additional text displayed with the CTA (call-to-action) button to give
	// context and encourage clicking on the button.
	ActionHeadline *wrappers.StringValue `protobuf:"bytes,2,opt,name=action_headline,json=actionHeadline,proto3" json:"action_headline,omitempty"`
	// The MediaFile resource name of the companion banner used with the ad.
	CompanionBanner *wrappers.StringValue `protobuf:"bytes,3,opt,name=companion_banner,json=companionBanner,proto3" json:"companion_banner,omitempty"`
}

func (x *VideoTrueViewInStreamAdInfo) Reset() {
	*x = VideoTrueViewInStreamAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoTrueViewInStreamAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoTrueViewInStreamAdInfo) ProtoMessage() {}

func (x *VideoTrueViewInStreamAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoTrueViewInStreamAdInfo.ProtoReflect.Descriptor instead.
func (*VideoTrueViewInStreamAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{16}
}

func (x *VideoTrueViewInStreamAdInfo) GetActionButtonLabel() *wrappers.StringValue {
	if x != nil {
		return x.ActionButtonLabel
	}
	return nil
}

func (x *VideoTrueViewInStreamAdInfo) GetActionHeadline() *wrappers.StringValue {
	if x != nil {
		return x.ActionHeadline
	}
	return nil
}

func (x *VideoTrueViewInStreamAdInfo) GetCompanionBanner() *wrappers.StringValue {
	if x != nil {
		return x.CompanionBanner
	}
	return nil
}

// Representation of video out-stream ad format (ad shown alongside a feed
// with automatic playback, without sound).
type VideoOutstreamAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The headline of the ad.
	Headline *wrappers.StringValue `protobuf:"bytes,1,opt,name=headline,proto3" json:"headline,omitempty"`
	// The description line.
	Description *wrappers.StringValue `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *VideoOutstreamAdInfo) Reset() {
	*x = VideoOutstreamAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoOutstreamAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoOutstreamAdInfo) ProtoMessage() {}

func (x *VideoOutstreamAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoOutstreamAdInfo.ProtoReflect.Descriptor instead.
func (*VideoOutstreamAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{17}
}

func (x *VideoOutstreamAdInfo) GetHeadline() *wrappers.StringValue {
	if x != nil {
		return x.Headline
	}
	return nil
}

func (x *VideoOutstreamAdInfo) GetDescription() *wrappers.StringValue {
	if x != nil {
		return x.Description
	}
	return nil
}

// A video ad.
type VideoAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The MediaFile resource to use for the video.
	MediaFile *wrappers.StringValue `protobuf:"bytes,1,opt,name=media_file,json=mediaFile,proto3" json:"media_file,omitempty"`
	// Format-specific schema for the different video formats.
	//
	// Types that are assignable to Format:
	//	*VideoAdInfo_InStream
	//	*VideoAdInfo_Bumper
	//	*VideoAdInfo_OutStream
	//	*VideoAdInfo_NonSkippable
	Format isVideoAdInfo_Format `protobuf_oneof:"format"`
}

func (x *VideoAdInfo) Reset() {
	*x = VideoAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoAdInfo) ProtoMessage() {}

func (x *VideoAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoAdInfo.ProtoReflect.Descriptor instead.
func (*VideoAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{18}
}

func (x *VideoAdInfo) GetMediaFile() *wrappers.StringValue {
	if x != nil {
		return x.MediaFile
	}
	return nil
}

func (m *VideoAdInfo) GetFormat() isVideoAdInfo_Format {
	if m != nil {
		return m.Format
	}
	return nil
}

func (x *VideoAdInfo) GetInStream() *VideoTrueViewInStreamAdInfo {
	if x, ok := x.GetFormat().(*VideoAdInfo_InStream); ok {
		return x.InStream
	}
	return nil
}

func (x *VideoAdInfo) GetBumper() *VideoBumperInStreamAdInfo {
	if x, ok := x.GetFormat().(*VideoAdInfo_Bumper); ok {
		return x.Bumper
	}
	return nil
}

func (x *VideoAdInfo) GetOutStream() *VideoOutstreamAdInfo {
	if x, ok := x.GetFormat().(*VideoAdInfo_OutStream); ok {
		return x.OutStream
	}
	return nil
}

func (x *VideoAdInfo) GetNonSkippable() *VideoNonSkippableInStreamAdInfo {
	if x, ok := x.GetFormat().(*VideoAdInfo_NonSkippable); ok {
		return x.NonSkippable
	}
	return nil
}

type isVideoAdInfo_Format interface {
	isVideoAdInfo_Format()
}

type VideoAdInfo_InStream struct {
	// Video TrueView in-stream ad format.
	InStream *VideoTrueViewInStreamAdInfo `protobuf:"bytes,2,opt,name=in_stream,json=inStream,proto3,oneof"`
}

type VideoAdInfo_Bumper struct {
	// Video bumper in-stream ad format.
	Bumper *VideoBumperInStreamAdInfo `protobuf:"bytes,3,opt,name=bumper,proto3,oneof"`
}

type VideoAdInfo_OutStream struct {
	// Video out-stream ad format.
	OutStream *VideoOutstreamAdInfo `protobuf:"bytes,4,opt,name=out_stream,json=outStream,proto3,oneof"`
}

type VideoAdInfo_NonSkippable struct {
	// Video non-skippable in-stream ad format.
	NonSkippable *VideoNonSkippableInStreamAdInfo `protobuf:"bytes,5,opt,name=non_skippable,json=nonSkippable,proto3,oneof"`
}

func (*VideoAdInfo_InStream) isVideoAdInfo_Format() {}

func (*VideoAdInfo_Bumper) isVideoAdInfo_Format() {}

func (*VideoAdInfo_OutStream) isVideoAdInfo_Format() {}

func (*VideoAdInfo_NonSkippable) isVideoAdInfo_Format() {}

// A responsive search ad.
//
// Responsive search ads let you create an ad that adapts to show more text, and
// more relevant messages, to your customers. Enter multiple headlines and
// descriptions when creating a responsive search ad, and over time, Google Ads
// will automatically test different combinations and learn which combinations
// perform best. By adapting your ad's content to more closely match potential
// customers' search terms, responsive search ads may improve your campaign's
// performance.
//
// More information at https://support.google.com/google-ads/answer/7684791
type ResponsiveSearchAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of text assets for headlines. When the ad serves the headlines will
	// be selected from this list.
	Headlines []*AdTextAsset `protobuf:"bytes,1,rep,name=headlines,proto3" json:"headlines,omitempty"`
	// List of text assets for descriptions. When the ad serves the descriptions
	// will be selected from this list.
	Descriptions []*AdTextAsset `protobuf:"bytes,2,rep,name=descriptions,proto3" json:"descriptions,omitempty"`
	// First part of text that may appear appended to the url displayed in the ad.
	Path1 *wrappers.StringValue `protobuf:"bytes,3,opt,name=path1,proto3" json:"path1,omitempty"`
	// Second part of text that may appear appended to the url displayed in the
	// ad. This field can only be set when path1 is also set.
	Path2 *wrappers.StringValue `protobuf:"bytes,4,opt,name=path2,proto3" json:"path2,omitempty"`
}

func (x *ResponsiveSearchAdInfo) Reset() {
	*x = ResponsiveSearchAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResponsiveSearchAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponsiveSearchAdInfo) ProtoMessage() {}

func (x *ResponsiveSearchAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponsiveSearchAdInfo.ProtoReflect.Descriptor instead.
func (*ResponsiveSearchAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{19}
}

func (x *ResponsiveSearchAdInfo) GetHeadlines() []*AdTextAsset {
	if x != nil {
		return x.Headlines
	}
	return nil
}

func (x *ResponsiveSearchAdInfo) GetDescriptions() []*AdTextAsset {
	if x != nil {
		return x.Descriptions
	}
	return nil
}

func (x *ResponsiveSearchAdInfo) GetPath1() *wrappers.StringValue {
	if x != nil {
		return x.Path1
	}
	return nil
}

func (x *ResponsiveSearchAdInfo) GetPath2() *wrappers.StringValue {
	if x != nil {
		return x.Path2
	}
	return nil
}

// A legacy responsive display ad. Ads of this type are labeled 'Responsive ads'
// in the Google Ads UI.
type LegacyResponsiveDisplayAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The short version of the ad's headline.
	ShortHeadline *wrappers.StringValue `protobuf:"bytes,1,opt,name=short_headline,json=shortHeadline,proto3" json:"short_headline,omitempty"`
	// The long version of the ad's headline.
	LongHeadline *wrappers.StringValue `protobuf:"bytes,2,opt,name=long_headline,json=longHeadline,proto3" json:"long_headline,omitempty"`
	// The description of the ad.
	Description *wrappers.StringValue `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// The business name in the ad.
	BusinessName *wrappers.StringValue `protobuf:"bytes,4,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// Advertiser's consent to allow flexible color. When true, the ad may be
	// served with different color if necessary. When false, the ad will be served
	// with the specified colors or a neutral color.
	// The default value is true.
	// Must be true if main_color and accent_color are not set.
	AllowFlexibleColor *wrappers.BoolValue `protobuf:"bytes,5,opt,name=allow_flexible_color,json=allowFlexibleColor,proto3" json:"allow_flexible_color,omitempty"`
	// The accent color of the ad in hexadecimal, e.g. #ffffff for white.
	// If one of main_color and accent_color is set, the other is required as
	// well.
	AccentColor *wrappers.StringValue `protobuf:"bytes,6,opt,name=accent_color,json=accentColor,proto3" json:"accent_color,omitempty"`
	// The main color of the ad in hexadecimal, e.g. #ffffff for white.
	// If one of main_color and accent_color is set, the other is required as
	// well.
	MainColor *wrappers.StringValue `protobuf:"bytes,7,opt,name=main_color,json=mainColor,proto3" json:"main_color,omitempty"`
	// The call-to-action text for the ad.
	CallToActionText *wrappers.StringValue `protobuf:"bytes,8,opt,name=call_to_action_text,json=callToActionText,proto3" json:"call_to_action_text,omitempty"`
	// The MediaFile resource name of the logo image used in the ad.
	LogoImage *wrappers.StringValue `protobuf:"bytes,9,opt,name=logo_image,json=logoImage,proto3" json:"logo_image,omitempty"`
	// The MediaFile resource name of the square logo image used in the ad.
	SquareLogoImage *wrappers.StringValue `protobuf:"bytes,10,opt,name=square_logo_image,json=squareLogoImage,proto3" json:"square_logo_image,omitempty"`
	// The MediaFile resource name of the marketing image used in the ad.
	MarketingImage *wrappers.StringValue `protobuf:"bytes,11,opt,name=marketing_image,json=marketingImage,proto3" json:"marketing_image,omitempty"`
	// The MediaFile resource name of the square marketing image used in the ad.
	SquareMarketingImage *wrappers.StringValue `protobuf:"bytes,12,opt,name=square_marketing_image,json=squareMarketingImage,proto3" json:"square_marketing_image,omitempty"`
	// Specifies which format the ad will be served in. Default is ALL_FORMATS.
	FormatSetting enums.DisplayAdFormatSettingEnum_DisplayAdFormatSetting `protobuf:"varint,13,opt,name=format_setting,json=formatSetting,proto3,enum=google.ads.googleads.v1.enums.DisplayAdFormatSettingEnum_DisplayAdFormatSetting" json:"format_setting,omitempty"`
	// Prefix before price. E.g. 'as low as'.
	PricePrefix *wrappers.StringValue `protobuf:"bytes,14,opt,name=price_prefix,json=pricePrefix,proto3" json:"price_prefix,omitempty"`
	// Promotion text used for dyanmic formats of responsive ads. For example
	// 'Free two-day shipping'.
	PromoText *wrappers.StringValue `protobuf:"bytes,15,opt,name=promo_text,json=promoText,proto3" json:"promo_text,omitempty"`
}

func (x *LegacyResponsiveDisplayAdInfo) Reset() {
	*x = LegacyResponsiveDisplayAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LegacyResponsiveDisplayAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LegacyResponsiveDisplayAdInfo) ProtoMessage() {}

func (x *LegacyResponsiveDisplayAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LegacyResponsiveDisplayAdInfo.ProtoReflect.Descriptor instead.
func (*LegacyResponsiveDisplayAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{20}
}

func (x *LegacyResponsiveDisplayAdInfo) GetShortHeadline() *wrappers.StringValue {
	if x != nil {
		return x.ShortHeadline
	}
	return nil
}

func (x *LegacyResponsiveDisplayAdInfo) GetLongHeadline() *wrappers.StringValue {
	if x != nil {
		return x.LongHeadline
	}
	return nil
}

func (x *LegacyResponsiveDisplayAdInfo) GetDescription() *wrappers.StringValue {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *LegacyResponsiveDisplayAdInfo) GetBusinessName() *wrappers.StringValue {
	if x != nil {
		return x.BusinessName
	}
	return nil
}

func (x *LegacyResponsiveDisplayAdInfo) GetAllowFlexibleColor() *wrappers.BoolValue {
	if x != nil {
		return x.AllowFlexibleColor
	}
	return nil
}

func (x *LegacyResponsiveDisplayAdInfo) GetAccentColor() *wrappers.StringValue {
	if x != nil {
		return x.AccentColor
	}
	return nil
}

func (x *LegacyResponsiveDisplayAdInfo) GetMainColor() *wrappers.StringValue {
	if x != nil {
		return x.MainColor
	}
	return nil
}

func (x *LegacyResponsiveDisplayAdInfo) GetCallToActionText() *wrappers.StringValue {
	if x != nil {
		return x.CallToActionText
	}
	return nil
}

func (x *LegacyResponsiveDisplayAdInfo) GetLogoImage() *wrappers.StringValue {
	if x != nil {
		return x.LogoImage
	}
	return nil
}

func (x *LegacyResponsiveDisplayAdInfo) GetSquareLogoImage() *wrappers.StringValue {
	if x != nil {
		return x.SquareLogoImage
	}
	return nil
}

func (x *LegacyResponsiveDisplayAdInfo) GetMarketingImage() *wrappers.StringValue {
	if x != nil {
		return x.MarketingImage
	}
	return nil
}

func (x *LegacyResponsiveDisplayAdInfo) GetSquareMarketingImage() *wrappers.StringValue {
	if x != nil {
		return x.SquareMarketingImage
	}
	return nil
}

func (x *LegacyResponsiveDisplayAdInfo) GetFormatSetting() enums.DisplayAdFormatSettingEnum_DisplayAdFormatSetting {
	if x != nil {
		return x.FormatSetting
	}
	return enums.DisplayAdFormatSettingEnum_UNSPECIFIED
}

func (x *LegacyResponsiveDisplayAdInfo) GetPricePrefix() *wrappers.StringValue {
	if x != nil {
		return x.PricePrefix
	}
	return nil
}

func (x *LegacyResponsiveDisplayAdInfo) GetPromoText() *wrappers.StringValue {
	if x != nil {
		return x.PromoText
	}
	return nil
}

// An app ad.
type AppAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// An optional text asset that, if specified, must always be displayed when
	// the ad is served.
	MandatoryAdText *AdTextAsset `protobuf:"bytes,1,opt,name=mandatory_ad_text,json=mandatoryAdText,proto3" json:"mandatory_ad_text,omitempty"`
	// List of text assets for headlines. When the ad serves the headlines will
	// be selected from this list.
	Headlines []*AdTextAsset `protobuf:"bytes,2,rep,name=headlines,proto3" json:"headlines,omitempty"`
	// List of text assets for descriptions. When the ad serves the descriptions
	// will be selected from this list.
	Descriptions []*AdTextAsset `protobuf:"bytes,3,rep,name=descriptions,proto3" json:"descriptions,omitempty"`
	// List of image assets that may be displayed with the ad.
	Images []*AdImageAsset `protobuf:"bytes,4,rep,name=images,proto3" json:"images,omitempty"`
	// List of YouTube video assets that may be displayed with the ad.
	YoutubeVideos []*AdVideoAsset `protobuf:"bytes,5,rep,name=youtube_videos,json=youtubeVideos,proto3" json:"youtube_videos,omitempty"`
	// List of media bundle assets that may be used with the ad.
	Html5MediaBundles []*AdMediaBundleAsset `protobuf:"bytes,6,rep,name=html5_media_bundles,json=html5MediaBundles,proto3" json:"html5_media_bundles,omitempty"`
}

func (x *AppAdInfo) Reset() {
	*x = AppAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppAdInfo) ProtoMessage() {}

func (x *AppAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppAdInfo.ProtoReflect.Descriptor instead.
func (*AppAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{21}
}

func (x *AppAdInfo) GetMandatoryAdText() *AdTextAsset {
	if x != nil {
		return x.MandatoryAdText
	}
	return nil
}

func (x *AppAdInfo) GetHeadlines() []*AdTextAsset {
	if x != nil {
		return x.Headlines
	}
	return nil
}

func (x *AppAdInfo) GetDescriptions() []*AdTextAsset {
	if x != nil {
		return x.Descriptions
	}
	return nil
}

func (x *AppAdInfo) GetImages() []*AdImageAsset {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *AppAdInfo) GetYoutubeVideos() []*AdVideoAsset {
	if x != nil {
		return x.YoutubeVideos
	}
	return nil
}

func (x *AppAdInfo) GetHtml5MediaBundles() []*AdMediaBundleAsset {
	if x != nil {
		return x.Html5MediaBundles
	}
	return nil
}

// App engagement ads allow you to write text encouraging a specific action in
// the app, like checking in, making a purchase, or booking a flight.
// They allow you to send users to a specific part of your app where they can
// find what they're looking for easier and faster.
type AppEngagementAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of text assets for headlines. When the ad serves the headlines will
	// be selected from this list.
	Headlines []*AdTextAsset `protobuf:"bytes,1,rep,name=headlines,proto3" json:"headlines,omitempty"`
	// List of text assets for descriptions. When the ad serves the descriptions
	// will be selected from this list.
	Descriptions []*AdTextAsset `protobuf:"bytes,2,rep,name=descriptions,proto3" json:"descriptions,omitempty"`
	// List of image assets that may be displayed with the ad.
	Images []*AdImageAsset `protobuf:"bytes,3,rep,name=images,proto3" json:"images,omitempty"`
	// List of video assets that may be displayed with the ad.
	Videos []*AdVideoAsset `protobuf:"bytes,4,rep,name=videos,proto3" json:"videos,omitempty"`
}

func (x *AppEngagementAdInfo) Reset() {
	*x = AppEngagementAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppEngagementAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppEngagementAdInfo) ProtoMessage() {}

func (x *AppEngagementAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppEngagementAdInfo.ProtoReflect.Descriptor instead.
func (*AppEngagementAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{22}
}

func (x *AppEngagementAdInfo) GetHeadlines() []*AdTextAsset {
	if x != nil {
		return x.Headlines
	}
	return nil
}

func (x *AppEngagementAdInfo) GetDescriptions() []*AdTextAsset {
	if x != nil {
		return x.Descriptions
	}
	return nil
}

func (x *AppEngagementAdInfo) GetImages() []*AdImageAsset {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *AppEngagementAdInfo) GetVideos() []*AdVideoAsset {
	if x != nil {
		return x.Videos
	}
	return nil
}

// A legacy app install ad that only can be used by a few select customers.
type LegacyAppInstallAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The id of the mobile app.
	AppId *wrappers.StringValue `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// The app store the mobile app is available in.
	AppStore enums.LegacyAppInstallAdAppStoreEnum_LegacyAppInstallAdAppStore `protobuf:"varint,2,opt,name=app_store,json=appStore,proto3,enum=google.ads.googleads.v1.enums.LegacyAppInstallAdAppStoreEnum_LegacyAppInstallAdAppStore" json:"app_store,omitempty"`
	// The headline of the ad.
	Headline *wrappers.StringValue `protobuf:"bytes,3,opt,name=headline,proto3" json:"headline,omitempty"`
	// The first description line of the ad.
	Description1 *wrappers.StringValue `protobuf:"bytes,4,opt,name=description1,proto3" json:"description1,omitempty"`
	// The second description line of the ad.
	Description2 *wrappers.StringValue `protobuf:"bytes,5,opt,name=description2,proto3" json:"description2,omitempty"`
}

func (x *LegacyAppInstallAdInfo) Reset() {
	*x = LegacyAppInstallAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LegacyAppInstallAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LegacyAppInstallAdInfo) ProtoMessage() {}

func (x *LegacyAppInstallAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LegacyAppInstallAdInfo.ProtoReflect.Descriptor instead.
func (*LegacyAppInstallAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{23}
}

func (x *LegacyAppInstallAdInfo) GetAppId() *wrappers.StringValue {
	if x != nil {
		return x.AppId
	}
	return nil
}

func (x *LegacyAppInstallAdInfo) GetAppStore() enums.LegacyAppInstallAdAppStoreEnum_LegacyAppInstallAdAppStore {
	if x != nil {
		return x.AppStore
	}
	return enums.LegacyAppInstallAdAppStoreEnum_UNSPECIFIED
}

func (x *LegacyAppInstallAdInfo) GetHeadline() *wrappers.StringValue {
	if x != nil {
		return x.Headline
	}
	return nil
}

func (x *LegacyAppInstallAdInfo) GetDescription1() *wrappers.StringValue {
	if x != nil {
		return x.Description1
	}
	return nil
}

func (x *LegacyAppInstallAdInfo) GetDescription2() *wrappers.StringValue {
	if x != nil {
		return x.Description2
	}
	return nil
}

// A responsive display ad.
type ResponsiveDisplayAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Marketing images to be used in the ad. Valid image types are GIF,
	// JPEG, and PNG. The minimum size is 600x314 and the aspect ratio must
	// be 1.91:1 (+-1%). At least one marketing_image is required. Combined with
	// square_marketing_images the maximum is 15.
	MarketingImages []*AdImageAsset `protobuf:"bytes,1,rep,name=marketing_images,json=marketingImages,proto3" json:"marketing_images,omitempty"`
	// Square marketing images to be used in the ad. Valid image types are GIF,
	// JPEG, and PNG. The minimum size is 300x300 and the aspect ratio must
	// be 1:1 (+-1%). At least one square marketing_image is required. Combined
	// with marketing_images the maximum is 15.
	SquareMarketingImages []*AdImageAsset `protobuf:"bytes,2,rep,name=square_marketing_images,json=squareMarketingImages,proto3" json:"square_marketing_images,omitempty"`
	// Logo images to be used in the ad. Valid image types are GIF,
	// JPEG, and PNG. The minimum size is 512x128 and the aspect ratio must
	// be 4:1 (+-1%). Combined with square_logo_images the maximum is 5.
	LogoImages []*AdImageAsset `protobuf:"bytes,3,rep,name=logo_images,json=logoImages,proto3" json:"logo_images,omitempty"`
	// Square logo images to be used in the ad. Valid image types are GIF,
	// JPEG, and PNG. The minimum size is 128x128 and the aspect ratio must
	// be 1:1 (+-1%). Combined with square_logo_images the maximum is 5.
	SquareLogoImages []*AdImageAsset `protobuf:"bytes,4,rep,name=square_logo_images,json=squareLogoImages,proto3" json:"square_logo_images,omitempty"`
	// Short format headlines for the ad. The maximum length is 30 characters.
	// At least 1 and max 5 headlines can be specified.
	Headlines []*AdTextAsset `protobuf:"bytes,5,rep,name=headlines,proto3" json:"headlines,omitempty"`
	// A required long format headline. The maximum length is 90 characters.
	LongHeadline *AdTextAsset `protobuf:"bytes,6,opt,name=long_headline,json=longHeadline,proto3" json:"long_headline,omitempty"`
	// Descriptive texts for the ad. The maximum length is 90 characters. At
	// least 1 and max 5 headlines can be specified.
	Descriptions []*AdTextAsset `protobuf:"bytes,7,rep,name=descriptions,proto3" json:"descriptions,omitempty"`
	// Optional YouTube videos for the ad. A maximum of 5 videos can be specified.
	YoutubeVideos []*AdVideoAsset `protobuf:"bytes,8,rep,name=youtube_videos,json=youtubeVideos,proto3" json:"youtube_videos,omitempty"`
	// The advertiser/brand name. Maximum display width is 25.
	BusinessName *wrappers.StringValue `protobuf:"bytes,9,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// The main color of the ad in hexadecimal, e.g. #ffffff for white.
	// If one of main_color and accent_color is set, the other is required as
	// well.
	MainColor *wrappers.StringValue `protobuf:"bytes,10,opt,name=main_color,json=mainColor,proto3" json:"main_color,omitempty"`
	// The accent color of the ad in hexadecimal, e.g. #ffffff for white.
	// If one of main_color and accent_color is set, the other is required as
	// well.
	AccentColor *wrappers.StringValue `protobuf:"bytes,11,opt,name=accent_color,json=accentColor,proto3" json:"accent_color,omitempty"`
	// Advertiser's consent to allow flexible color. When true, the ad may be
	// served with different color if necessary. When false, the ad will be served
	// with the specified colors or a neutral color.
	// The default value is true.
	// Must be true if main_color and accent_color are not set.
	AllowFlexibleColor *wrappers.BoolValue `protobuf:"bytes,12,opt,name=allow_flexible_color,json=allowFlexibleColor,proto3" json:"allow_flexible_color,omitempty"`
	// The call-to-action text for the ad. Maximum display width is 30.
	CallToActionText *wrappers.StringValue `protobuf:"bytes,13,opt,name=call_to_action_text,json=callToActionText,proto3" json:"call_to_action_text,omitempty"`
	// Prefix before price. E.g. 'as low as'.
	PricePrefix *wrappers.StringValue `protobuf:"bytes,14,opt,name=price_prefix,json=pricePrefix,proto3" json:"price_prefix,omitempty"`
	// Promotion text used for dyanmic formats of responsive ads. For example
	// 'Free two-day shipping'.
	PromoText *wrappers.StringValue `protobuf:"bytes,15,opt,name=promo_text,json=promoText,proto3" json:"promo_text,omitempty"`
	// Specifies which format the ad will be served in. Default is ALL_FORMATS.
	FormatSetting enums.DisplayAdFormatSettingEnum_DisplayAdFormatSetting `protobuf:"varint,16,opt,name=format_setting,json=formatSetting,proto3,enum=google.ads.googleads.v1.enums.DisplayAdFormatSettingEnum_DisplayAdFormatSetting" json:"format_setting,omitempty"`
}

func (x *ResponsiveDisplayAdInfo) Reset() {
	*x = ResponsiveDisplayAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResponsiveDisplayAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponsiveDisplayAdInfo) ProtoMessage() {}

func (x *ResponsiveDisplayAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponsiveDisplayAdInfo.ProtoReflect.Descriptor instead.
func (*ResponsiveDisplayAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{24}
}

func (x *ResponsiveDisplayAdInfo) GetMarketingImages() []*AdImageAsset {
	if x != nil {
		return x.MarketingImages
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetSquareMarketingImages() []*AdImageAsset {
	if x != nil {
		return x.SquareMarketingImages
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetLogoImages() []*AdImageAsset {
	if x != nil {
		return x.LogoImages
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetSquareLogoImages() []*AdImageAsset {
	if x != nil {
		return x.SquareLogoImages
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetHeadlines() []*AdTextAsset {
	if x != nil {
		return x.Headlines
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetLongHeadline() *AdTextAsset {
	if x != nil {
		return x.LongHeadline
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetDescriptions() []*AdTextAsset {
	if x != nil {
		return x.Descriptions
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetYoutubeVideos() []*AdVideoAsset {
	if x != nil {
		return x.YoutubeVideos
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetBusinessName() *wrappers.StringValue {
	if x != nil {
		return x.BusinessName
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetMainColor() *wrappers.StringValue {
	if x != nil {
		return x.MainColor
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetAccentColor() *wrappers.StringValue {
	if x != nil {
		return x.AccentColor
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetAllowFlexibleColor() *wrappers.BoolValue {
	if x != nil {
		return x.AllowFlexibleColor
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetCallToActionText() *wrappers.StringValue {
	if x != nil {
		return x.CallToActionText
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetPricePrefix() *wrappers.StringValue {
	if x != nil {
		return x.PricePrefix
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetPromoText() *wrappers.StringValue {
	if x != nil {
		return x.PromoText
	}
	return nil
}

func (x *ResponsiveDisplayAdInfo) GetFormatSetting() enums.DisplayAdFormatSettingEnum_DisplayAdFormatSetting {
	if x != nil {
		return x.FormatSetting
	}
	return enums.DisplayAdFormatSettingEnum_UNSPECIFIED
}

// A generic type of display ad. The exact ad format is controlled by the
// display_upload_product_type field, which determines what kinds of data
// need to be included with the ad.
type DisplayUploadAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The product type of this ad. See comments on the enum for details.
	DisplayUploadProductType enums.DisplayUploadProductTypeEnum_DisplayUploadProductType `protobuf:"varint,1,opt,name=display_upload_product_type,json=displayUploadProductType,proto3,enum=google.ads.googleads.v1.enums.DisplayUploadProductTypeEnum_DisplayUploadProductType" json:"display_upload_product_type,omitempty"`
	// The asset data that makes up the ad.
	//
	// Types that are assignable to MediaAsset:
	//	*DisplayUploadAdInfo_MediaBundle
	MediaAsset isDisplayUploadAdInfo_MediaAsset `protobuf_oneof:"media_asset"`
}

func (x *DisplayUploadAdInfo) Reset() {
	*x = DisplayUploadAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayUploadAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayUploadAdInfo) ProtoMessage() {}

func (x *DisplayUploadAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayUploadAdInfo.ProtoReflect.Descriptor instead.
func (*DisplayUploadAdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP(), []int{25}
}

func (x *DisplayUploadAdInfo) GetDisplayUploadProductType() enums.DisplayUploadProductTypeEnum_DisplayUploadProductType {
	if x != nil {
		return x.DisplayUploadProductType
	}
	return enums.DisplayUploadProductTypeEnum_UNSPECIFIED
}

func (m *DisplayUploadAdInfo) GetMediaAsset() isDisplayUploadAdInfo_MediaAsset {
	if m != nil {
		return m.MediaAsset
	}
	return nil
}

func (x *DisplayUploadAdInfo) GetMediaBundle() *AdMediaBundleAsset {
	if x, ok := x.GetMediaAsset().(*DisplayUploadAdInfo_MediaBundle); ok {
		return x.MediaBundle
	}
	return nil
}

type isDisplayUploadAdInfo_MediaAsset interface {
	isDisplayUploadAdInfo_MediaAsset()
}

type DisplayUploadAdInfo_MediaBundle struct {
	// A media bundle asset to be used in the ad. For information about the
	// media bundle for HTML5_UPLOAD_AD see
	// https://support.google.com/google-ads/answer/1722096
	// Media bundles that are part of dynamic product types use a special format
	// that needs to be created through the Google Web Designer. See
	// https://support.google.com/webdesigner/answer/7543898 for more
	// information.
	MediaBundle *AdMediaBundleAsset `protobuf:"bytes,2,opt,name=media_bundle,json=mediaBundle,proto3,oneof"`
}

func (*DisplayUploadAdInfo_MediaBundle) isDisplayUploadAdInfo_MediaAsset() {}

var File_google_ads_googleads_v1_common_ad_type_infos_proto protoreflect.FileDescriptor

var file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDesc = []byte{
	0x0a, 0x32, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x2d, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x61, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x43, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3d, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x61, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x43, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x6c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x5f, 0x61,
	0x70, 0x70, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x64, 0x5f, 0x61, 0x70,
	0x70, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x6d, 0x69, 0x6d,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72,
	0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xca, 0x01, 0x0a, 0x0a, 0x54,
	0x65, 0x78, 0x74, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a, 0x08, 0x68, 0x65, 0x61,
	0x64, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64, 0x6c,
	0x69, 0x6e, 0x65, 0x12, 0x40, 0x0a, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x31, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x31, 0x12, 0x40, 0x0a, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x22, 0xcd, 0x03, 0x0a, 0x12, 0x45, 0x78, 0x70, 0x61,
	0x6e, 0x64, 0x65, 0x64, 0x54, 0x65, 0x78, 0x74, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x43,
	0x0a, 0x0e, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x31,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x61,
	0x72, 0x74, 0x31, 0x12, 0x43, 0x0a, 0x0e, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x70, 0x61, 0x72, 0x74, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x68, 0x65, 0x61, 0x64, 0x6c,
	0x69, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x74, 0x32, 0x12, 0x43, 0x0a, 0x0e, 0x68, 0x65, 0x61, 0x64,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x33, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d,
	0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x74, 0x33, 0x12, 0x3e, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a,
	0x0c, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x12,
	0x32, 0x0a, 0x05, 0x70, 0x61, 0x74, 0x68, 0x31, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x70, 0x61,
	0x74, 0x68, 0x31, 0x12, 0x32, 0x0a, 0x05, 0x70, 0x61, 0x74, 0x68, 0x32, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x70, 0x61, 0x74, 0x68, 0x32, 0x22, 0xad, 0x07, 0x0a, 0x0e, 0x43, 0x61, 0x6c, 0x6c,
	0x4f, 0x6e, 0x6c, 0x79, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3f, 0x0a, 0x0c, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0d,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x3a, 0x0a, 0x09, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x09, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x12, 0x3a, 0x0a, 0x09, 0x68,
	0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x68, 0x65,
	0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x12, 0x40, 0x0a, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x31, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x31, 0x12, 0x40, 0x0a, 0x0c, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x12, 0x3d, 0x0a, 0x0c, 0x63,
	0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x63,
	0x61, 0x6c, 0x6c, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x12, 0x52, 0x0a, 0x17, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x15, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x43, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x5f,
	0x0a, 0x1d, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x1a, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12,
	0x49, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x9a, 0x01, 0x0a, 0x1a, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x5c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x43, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x75, 0x6d,
	0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x18, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x5d, 0x0a, 0x1b, 0x45, 0x78, 0x70, 0x61, 0x6e,
	0x64, 0x65, 0x64, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3e, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x0d, 0x0a, 0x0b, 0x48, 0x6f, 0x74, 0x65, 0x6c, 0x41,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x15, 0x0a, 0x13, 0x53, 0x68, 0x6f, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x53, 0x6d, 0x61, 0x72, 0x74, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x17, 0x0a, 0x15,
	0x53, 0x68, 0x6f, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x41,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x5b, 0x0a, 0x1f, 0x53, 0x68, 0x6f, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x69,
	0x6e, 0x67, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64,
	0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69,
	0x6e, 0x65, 0x22, 0xc3, 0x05, 0x0a, 0x0b, 0x47, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x43, 0x0a, 0x06, 0x74, 0x65, 0x61, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x61, 0x73, 0x65, 0x72, 0x52,
	0x06, 0x74, 0x65, 0x61, 0x73, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x0c, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x45, 0x0a, 0x0f, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x56, 0x0a, 0x18, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x16, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x5c, 0x0a, 0x1b, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x19, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x86, 0x01, 0x0a, 0x26, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43,
	0x61, 0x6c, 0x6c, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x21, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x43, 0x61, 0x6c, 0x6c, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x53,
	0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x22, 0x87, 0x02, 0x0a, 0x0b, 0x47, 0x6d, 0x61,
	0x69, 0x6c, 0x54, 0x65, 0x61, 0x73, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64,
	0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69,
	0x6e, 0x65, 0x12, 0x3e, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x6f, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x22, 0xce, 0x01, 0x0a, 0x13, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x61,
	0x6c, 0x6c, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x3b, 0x0a, 0x0a,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09,
	0x74, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x48, 0x0a, 0x11, 0x75, 0x72, 0x6c,
	0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x0f, 0x75, 0x72, 0x6c, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x22, 0xfb, 0x01, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x3e, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x68, 0x0a, 0x16, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x43, 0x61, 0x6c, 0x6c, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x61, 0x6c, 0x6c, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x51, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x12, 0x41, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x22, 0x82, 0x06, 0x0a, 0x0b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3c, 0x0a, 0x0b, 0x70, 0x69, 0x78, 0x65, 0x6c, 0x5f, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x70, 0x69, 0x78, 0x65, 0x6c, 0x57, 0x69, 0x64,
	0x74, 0x68, 0x12, 0x3e, 0x0a, 0x0c, 0x70, 0x69, 0x78, 0x65, 0x6c, 0x5f, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x70, 0x69, 0x78, 0x65, 0x6c, 0x48, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x39, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x4b, 0x0a,
	0x13, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x70, 0x69, 0x78, 0x65, 0x6c, 0x5f, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x11, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x50, 0x69, 0x78, 0x65, 0x6c, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x4d, 0x0a, 0x14, 0x70, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x70, 0x69, 0x78, 0x65, 0x6c, 0x5f, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x12, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x69,
	0x78, 0x65, 0x6c, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x48, 0x0a, 0x11, 0x70, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x0f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x51, 0x0a, 0x09, 0x6d, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x45,
	0x6e, 0x75, 0x6d, 0x2e, 0x4d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x6d, 0x69,
	0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69,
	0x61, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x09, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x48, 0x00, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x54, 0x0a, 0x18, 0x61, 0x64,
	0x5f, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x63, 0x6f, 0x70, 0x79, 0x5f, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x13, 0x61, 0x64, 0x49,
	0x64, 0x54, 0x6f, 0x43, 0x6f, 0x70, 0x79, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x46, 0x72, 0x6f, 0x6d,
	0x42, 0x07, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x22, 0x1b, 0x0a, 0x19, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x42, 0x75, 0x6d, 0x70, 0x65, 0x72, 0x49, 0x6e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x21, 0x0a, 0x1f, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4e,
	0x6f, 0x6e, 0x53, 0x6b, 0x69, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x53, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xfb, 0x01, 0x0a, 0x1b, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x54, 0x72, 0x75, 0x65, 0x56, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x53, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4c, 0x0a, 0x13, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x11, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x75, 0x74, 0x74,
	0x6f, 0x6e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x45, 0x0a, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x47,
	0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x6f,
	0x6e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x22, 0x90, 0x01, 0x0a, 0x14, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x38, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x08, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x3e, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xc4, 0x03, 0x0a, 0x0b, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x0a, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x5a, 0x0a, 0x09, 0x69, 0x6e, 0x5f, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x54, 0x72, 0x75, 0x65, 0x56, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x53, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x08, 0x69, 0x6e, 0x53, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x12, 0x53, 0x0a, 0x06, 0x62, 0x75, 0x6d, 0x70, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x42, 0x75, 0x6d, 0x70, 0x65, 0x72,
	0x49, 0x6e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00,
	0x52, 0x06, 0x62, 0x75, 0x6d, 0x70, 0x65, 0x72, 0x12, 0x55, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x5f,
	0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x41, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x48, 0x00, 0x52, 0x09, 0x6f, 0x75, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12,
	0x66, 0x0a, 0x0d, 0x6e, 0x6f, 0x6e, 0x5f, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4e, 0x6f, 0x6e,
	0x53, 0x6b, 0x69, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x53, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0c, 0x6e, 0x6f, 0x6e, 0x53, 0x6b,
	0x69, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x22, 0x9c, 0x02, 0x0a, 0x16, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x69, 0x76, 0x65,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x49, 0x0a, 0x09,
	0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x41, 0x64, 0x54, 0x65, 0x78, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x09, 0x68, 0x65,
	0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x4f, 0x0a, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41,
	0x64, 0x54, 0x65, 0x78, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0c, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x32, 0x0a, 0x05, 0x70, 0x61, 0x74, 0x68,
	0x31, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x70, 0x61, 0x74, 0x68, 0x31, 0x12, 0x32, 0x0a, 0x05,
	0x70, 0x61, 0x74, 0x68, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x70, 0x61, 0x74, 0x68, 0x32,
	0x22, 0xdc, 0x08, 0x0a, 0x1d, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x69, 0x76, 0x65, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x41, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x43, 0x0a, 0x0e, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x6c, 0x6f, 0x6e, 0x67, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x6c, 0x6f,
	0x6e, 0x67, 0x48, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x3e, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x0d, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4c, 0x0a,
	0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x66, 0x6c, 0x65, 0x78, 0x69, 0x62, 0x6c, 0x65, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x46, 0x6c,
	0x65, 0x78, 0x69, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x3f, 0x0a, 0x0c, 0x61,
	0x63, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0b, 0x61, 0x63, 0x63, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x3b, 0x0a, 0x0a,
	0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09,
	0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x4b, 0x0a, 0x13, 0x63, 0x61, 0x6c,
	0x6c, 0x5f, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x63, 0x61, 0x6c, 0x6c, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x12, 0x3b, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x6f, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x48, 0x0a, 0x11, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65, 0x5f, 0x6c, 0x6f,
	0x67, 0x6f, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x73, 0x71,
	0x75, 0x61, 0x72, 0x65, 0x4c, 0x6f, 0x67, 0x6f, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x45, 0x0a,
	0x0f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x12, 0x52, 0x0a, 0x16, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65, 0x5f, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x14, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x77, 0x0a, 0x0e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x50, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x41, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x44, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x41, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x52, 0x0d, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x12, 0x3f, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69,
	0x78, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x65, 0x50, 0x72, 0x65, 0x66,
	0x69, 0x78, 0x12, 0x3b, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x54, 0x65, 0x78, 0x74, 0x22,
	0xff, 0x03, 0x0a, 0x09, 0x41, 0x70, 0x70, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x57, 0x0a,
	0x11, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x64, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x54, 0x65, 0x78, 0x74,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79,
	0x41, 0x64, 0x54, 0x65, 0x78, 0x74, 0x12, 0x49, 0x0a, 0x09, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69,
	0x6e, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x54, 0x65, 0x78,
	0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x09, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65,
	0x73, 0x12, 0x4f, 0x0a, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x54, 0x65, 0x78, 0x74, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x44, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x0e, 0x79, 0x6f, 0x75, 0x74,
	0x75, 0x62, 0x65, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x41, 0x64, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0d,
	0x79, 0x6f, 0x75, 0x74, 0x75, 0x62, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x12, 0x62, 0x0a,
	0x13, 0x68, 0x74, 0x6d, 0x6c, 0x35, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x62, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x11,
	0x68, 0x74, 0x6d, 0x6c, 0x35, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x73, 0x22, 0xbd, 0x02, 0x0a, 0x13, 0x41, 0x70, 0x70, 0x45, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x49, 0x0a, 0x09, 0x68, 0x65, 0x61,
	0x64, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64,
	0x54, 0x65, 0x78, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x09, 0x68, 0x65, 0x61, 0x64, 0x6c,
	0x69, 0x6e, 0x65, 0x73, 0x12, 0x4f, 0x0a, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x54, 0x65,
	0x78, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x44, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x44, 0x0a, 0x06, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x73, 0x22, 0x82, 0x03, 0x0a, 0x16, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x41, 0x70, 0x70, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x06,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x75, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x58, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64,
	0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x41, 0x70, 0x70, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x64, 0x41, 0x70, 0x70, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x45,
	0x6e, 0x75, 0x6d, 0x2e, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x41, 0x64, 0x41, 0x70, 0x70, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x08,
	0x61, 0x70, 0x70, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64,
	0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69,
	0x6e, 0x65, 0x12, 0x40, 0x0a, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x31, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x31, 0x12, 0x40, 0x0a, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x22, 0x99, 0x0a, 0x0a, 0x17, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x69, 0x76, 0x65, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x41, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x57, 0x0a, 0x10, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0f, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x64, 0x0a, 0x17, 0x73,
	0x71, 0x75, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x15, 0x73, 0x71, 0x75, 0x61,
	0x72, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x73, 0x12, 0x4d, 0x0a, 0x0b, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x0a, 0x6c, 0x6f, 0x67, 0x6f, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x12, 0x5a, 0x0a, 0x12, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x10, 0x73, 0x71, 0x75, 0x61,
	0x72, 0x65, 0x4c, 0x6f, 0x67, 0x6f, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x49, 0x0a, 0x09,
	0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x41, 0x64, 0x54, 0x65, 0x78, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x09, 0x68, 0x65,
	0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x50, 0x0a, 0x0d, 0x6c, 0x6f, 0x6e, 0x67, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x41, 0x64, 0x54, 0x65, 0x78, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0c, 0x6c, 0x6f, 0x6e,
	0x67, 0x48, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x4f, 0x0a, 0x0c, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x41, 0x64, 0x54, 0x65, 0x78, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0c, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x53, 0x0a, 0x0e, 0x79, 0x6f,
	0x75, 0x74, 0x75, 0x62, 0x65, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x52, 0x0d, 0x79, 0x6f, 0x75, 0x74, 0x75, 0x62, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x12,
	0x41, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x3f, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x4c, 0x0a, 0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x66, 0x6c, 0x65, 0x78, 0x69, 0x62,
	0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x12, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x46, 0x6c, 0x65, 0x78, 0x69, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x4b,
	0x0a, 0x13, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x63, 0x61, 0x6c, 0x6c, 0x54,
	0x6f, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x12, 0x3f, 0x0a, 0x0c, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0b, 0x70, 0x72, 0x69, 0x63, 0x65, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x3b, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x54, 0x65, 0x78, 0x74, 0x12, 0x77, 0x0a, 0x0e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x50, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x41, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x44, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x41, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x52, 0x0d, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x22, 0x93, 0x02, 0x0a, 0x13, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x93, 0x01, 0x0a, 0x1b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x54, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x44, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x18, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x57, 0x0a, 0x0c, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x42,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x00, 0x52, 0x0b, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x42, 0xeb, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x42,
	0x10, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x50, 0x01, 0x5a, 0x44, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61,
	0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0xa2, 0x02, 0x03, 0x47, 0x41, 0x41, 0xaa,
	0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x41, 0x64, 0x73, 0x2e, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x2e, 0x56, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0xca, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x41, 0x64, 0x73, 0x5c, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x5c, 0x56, 0x31, 0x5c, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0xea, 0x02, 0x22, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x41, 0x64, 0x73, 0x3a,
	0x3a, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x3a, 0x3a, 0x56, 0x31, 0x3a, 0x3a,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescOnce sync.Once
	file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescData = file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDesc
)

func file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescGZIP() []byte {
	file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescOnce.Do(func() {
		file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescData)
	})
	return file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDescData
}

var file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_google_ads_googleads_v1_common_ad_type_infos_proto_goTypes = []interface{}{
	(*TextAdInfo)(nil),                      // 0: google.ads.googleads.v1.common.TextAdInfo
	(*ExpandedTextAdInfo)(nil),              // 1: google.ads.googleads.v1.common.ExpandedTextAdInfo
	(*CallOnlyAdInfo)(nil),                  // 2: google.ads.googleads.v1.common.CallOnlyAdInfo
	(*ExpandedDynamicSearchAdInfo)(nil),     // 3: google.ads.googleads.v1.common.ExpandedDynamicSearchAdInfo
	(*HotelAdInfo)(nil),                     // 4: google.ads.googleads.v1.common.HotelAdInfo
	(*ShoppingSmartAdInfo)(nil),             // 5: google.ads.googleads.v1.common.ShoppingSmartAdInfo
	(*ShoppingProductAdInfo)(nil),           // 6: google.ads.googleads.v1.common.ShoppingProductAdInfo
	(*ShoppingComparisonListingAdInfo)(nil), // 7: google.ads.googleads.v1.common.ShoppingComparisonListingAdInfo
	(*GmailAdInfo)(nil),                     // 8: google.ads.googleads.v1.common.GmailAdInfo
	(*GmailTeaser)(nil),                     // 9: google.ads.googleads.v1.common.GmailTeaser
	(*DisplayCallToAction)(nil),             // 10: google.ads.googleads.v1.common.DisplayCallToAction
	(*ProductImage)(nil),                    // 11: google.ads.googleads.v1.common.ProductImage
	(*ProductVideo)(nil),                    // 12: google.ads.googleads.v1.common.ProductVideo
	(*ImageAdInfo)(nil),                     // 13: google.ads.googleads.v1.common.ImageAdInfo
	(*VideoBumperInStreamAdInfo)(nil),       // 14: google.ads.googleads.v1.common.VideoBumperInStreamAdInfo
	(*VideoNonSkippableInStreamAdInfo)(nil), // 15: google.ads.googleads.v1.common.VideoNonSkippableInStreamAdInfo
	(*VideoTrueViewInStreamAdInfo)(nil),     // 16: google.ads.googleads.v1.common.VideoTrueViewInStreamAdInfo
	(*VideoOutstreamAdInfo)(nil),            // 17: google.ads.googleads.v1.common.VideoOutstreamAdInfo
	(*VideoAdInfo)(nil),                     // 18: google.ads.googleads.v1.common.VideoAdInfo
	(*ResponsiveSearchAdInfo)(nil),          // 19: google.ads.googleads.v1.common.ResponsiveSearchAdInfo
	(*LegacyResponsiveDisplayAdInfo)(nil),   // 20: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo
	(*AppAdInfo)(nil),                       // 21: google.ads.googleads.v1.common.AppAdInfo
	(*AppEngagementAdInfo)(nil),             // 22: google.ads.googleads.v1.common.AppEngagementAdInfo
	(*LegacyAppInstallAdInfo)(nil),          // 23: google.ads.googleads.v1.common.LegacyAppInstallAdInfo
	(*ResponsiveDisplayAdInfo)(nil),         // 24: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo
	(*DisplayUploadAdInfo)(nil),             // 25: google.ads.googleads.v1.common.DisplayUploadAdInfo
	(*wrappers.StringValue)(nil),            // 26: google.protobuf.StringValue
	(*wrappers.BoolValue)(nil),              // 27: google.protobuf.BoolValue
	(enums.CallConversionReportingStateEnum_CallConversionReportingState)(0), // 28: google.ads.googleads.v1.enums.CallConversionReportingStateEnum.CallConversionReportingState
	(*wrappers.Int64Value)(nil),                                  // 29: google.protobuf.Int64Value
	(enums.MimeTypeEnum_MimeType)(0),                             // 30: google.ads.googleads.v1.enums.MimeTypeEnum.MimeType
	(*wrappers.BytesValue)(nil),                                  // 31: google.protobuf.BytesValue
	(*AdTextAsset)(nil),                                          // 32: google.ads.googleads.v1.common.AdTextAsset
	(enums.DisplayAdFormatSettingEnum_DisplayAdFormatSetting)(0), // 33: google.ads.googleads.v1.enums.DisplayAdFormatSettingEnum.DisplayAdFormatSetting
	(*AdImageAsset)(nil),                                         // 34: google.ads.googleads.v1.common.AdImageAsset
	(*AdVideoAsset)(nil),                                         // 35: google.ads.googleads.v1.common.AdVideoAsset
	(*AdMediaBundleAsset)(nil),                                   // 36: google.ads.googleads.v1.common.AdMediaBundleAsset
	(enums.LegacyAppInstallAdAppStoreEnum_LegacyAppInstallAdAppStore)(0), // 37: google.ads.googleads.v1.enums.LegacyAppInstallAdAppStoreEnum.LegacyAppInstallAdAppStore
	(enums.DisplayUploadProductTypeEnum_DisplayUploadProductType)(0),     // 38: google.ads.googleads.v1.enums.DisplayUploadProductTypeEnum.DisplayUploadProductType
}
var file_google_ads_googleads_v1_common_ad_type_infos_proto_depIdxs = []int32{
	26,  // 0: google.ads.googleads.v1.common.TextAdInfo.headline:type_name -> google.protobuf.StringValue
	26,  // 1: google.ads.googleads.v1.common.TextAdInfo.description1:type_name -> google.protobuf.StringValue
	26,  // 2: google.ads.googleads.v1.common.TextAdInfo.description2:type_name -> google.protobuf.StringValue
	26,  // 3: google.ads.googleads.v1.common.ExpandedTextAdInfo.headline_part1:type_name -> google.protobuf.StringValue
	26,  // 4: google.ads.googleads.v1.common.ExpandedTextAdInfo.headline_part2:type_name -> google.protobuf.StringValue
	26,  // 5: google.ads.googleads.v1.common.ExpandedTextAdInfo.headline_part3:type_name -> google.protobuf.StringValue
	26,  // 6: google.ads.googleads.v1.common.ExpandedTextAdInfo.description:type_name -> google.protobuf.StringValue
	26,  // 7: google.ads.googleads.v1.common.ExpandedTextAdInfo.description2:type_name -> google.protobuf.StringValue
	26,  // 8: google.ads.googleads.v1.common.ExpandedTextAdInfo.path1:type_name -> google.protobuf.StringValue
	26,  // 9: google.ads.googleads.v1.common.ExpandedTextAdInfo.path2:type_name -> google.protobuf.StringValue
	26,  // 10: google.ads.googleads.v1.common.CallOnlyAdInfo.country_code:type_name -> google.protobuf.StringValue
	26,  // 11: google.ads.googleads.v1.common.CallOnlyAdInfo.phone_number:type_name -> google.protobuf.StringValue
	26,  // 12: google.ads.googleads.v1.common.CallOnlyAdInfo.business_name:type_name -> google.protobuf.StringValue
	26,  // 13: google.ads.googleads.v1.common.CallOnlyAdInfo.headline1:type_name -> google.protobuf.StringValue
	26,  // 14: google.ads.googleads.v1.common.CallOnlyAdInfo.headline2:type_name -> google.protobuf.StringValue
	26,  // 15: google.ads.googleads.v1.common.CallOnlyAdInfo.description1:type_name -> google.protobuf.StringValue
	26,  // 16: google.ads.googleads.v1.common.CallOnlyAdInfo.description2:type_name -> google.protobuf.StringValue
	27,  // 17: google.ads.googleads.v1.common.CallOnlyAdInfo.call_tracked:type_name -> google.protobuf.BoolValue
	27,  // 18: google.ads.googleads.v1.common.CallOnlyAdInfo.disable_call_conversion:type_name -> google.protobuf.BoolValue
	26,  // 19: google.ads.googleads.v1.common.CallOnlyAdInfo.phone_number_verification_url:type_name -> google.protobuf.StringValue
	26,  // 20: google.ads.googleads.v1.common.CallOnlyAdInfo.conversion_action:type_name -> google.protobuf.StringValue
	28,  // 21: google.ads.googleads.v1.common.CallOnlyAdInfo.conversion_reporting_state:type_name -> google.ads.googleads.v1.enums.CallConversionReportingStateEnum.CallConversionReportingState
	26,  // 22: google.ads.googleads.v1.common.ExpandedDynamicSearchAdInfo.description:type_name -> google.protobuf.StringValue
	26,  // 23: google.ads.googleads.v1.common.ShoppingComparisonListingAdInfo.headline:type_name -> google.protobuf.StringValue
	9,   // 24: google.ads.googleads.v1.common.GmailAdInfo.teaser:type_name -> google.ads.googleads.v1.common.GmailTeaser
	26,  // 25: google.ads.googleads.v1.common.GmailAdInfo.header_image:type_name -> google.protobuf.StringValue
	26,  // 26: google.ads.googleads.v1.common.GmailAdInfo.marketing_image:type_name -> google.protobuf.StringValue
	26,  // 27: google.ads.googleads.v1.common.GmailAdInfo.marketing_image_headline:type_name -> google.protobuf.StringValue
	26,  // 28: google.ads.googleads.v1.common.GmailAdInfo.marketing_image_description:type_name -> google.protobuf.StringValue
	10,  // 29: google.ads.googleads.v1.common.GmailAdInfo.marketing_image_display_call_to_action:type_name -> google.ads.googleads.v1.common.DisplayCallToAction
	11,  // 30: google.ads.googleads.v1.common.GmailAdInfo.product_images:type_name -> google.ads.googleads.v1.common.ProductImage
	12,  // 31: google.ads.googleads.v1.common.GmailAdInfo.product_videos:type_name -> google.ads.googleads.v1.common.ProductVideo
	26,  // 32: google.ads.googleads.v1.common.GmailTeaser.headline:type_name -> google.protobuf.StringValue
	26,  // 33: google.ads.googleads.v1.common.GmailTeaser.description:type_name -> google.protobuf.StringValue
	26,  // 34: google.ads.googleads.v1.common.GmailTeaser.business_name:type_name -> google.protobuf.StringValue
	26,  // 35: google.ads.googleads.v1.common.GmailTeaser.logo_image:type_name -> google.protobuf.StringValue
	26,  // 36: google.ads.googleads.v1.common.DisplayCallToAction.text:type_name -> google.protobuf.StringValue
	26,  // 37: google.ads.googleads.v1.common.DisplayCallToAction.text_color:type_name -> google.protobuf.StringValue
	26,  // 38: google.ads.googleads.v1.common.DisplayCallToAction.url_collection_id:type_name -> google.protobuf.StringValue
	26,  // 39: google.ads.googleads.v1.common.ProductImage.product_image:type_name -> google.protobuf.StringValue
	26,  // 40: google.ads.googleads.v1.common.ProductImage.description:type_name -> google.protobuf.StringValue
	10,  // 41: google.ads.googleads.v1.common.ProductImage.display_call_to_action:type_name -> google.ads.googleads.v1.common.DisplayCallToAction
	26,  // 42: google.ads.googleads.v1.common.ProductVideo.product_video:type_name -> google.protobuf.StringValue
	29,  // 43: google.ads.googleads.v1.common.ImageAdInfo.pixel_width:type_name -> google.protobuf.Int64Value
	29,  // 44: google.ads.googleads.v1.common.ImageAdInfo.pixel_height:type_name -> google.protobuf.Int64Value
	26,  // 45: google.ads.googleads.v1.common.ImageAdInfo.image_url:type_name -> google.protobuf.StringValue
	29,  // 46: google.ads.googleads.v1.common.ImageAdInfo.preview_pixel_width:type_name -> google.protobuf.Int64Value
	29,  // 47: google.ads.googleads.v1.common.ImageAdInfo.preview_pixel_height:type_name -> google.protobuf.Int64Value
	26,  // 48: google.ads.googleads.v1.common.ImageAdInfo.preview_image_url:type_name -> google.protobuf.StringValue
	30,  // 49: google.ads.googleads.v1.common.ImageAdInfo.mime_type:type_name -> google.ads.googleads.v1.enums.MimeTypeEnum.MimeType
	26,  // 50: google.ads.googleads.v1.common.ImageAdInfo.name:type_name -> google.protobuf.StringValue
	26,  // 51: google.ads.googleads.v1.common.ImageAdInfo.media_file:type_name -> google.protobuf.StringValue
	31,  // 52: google.ads.googleads.v1.common.ImageAdInfo.data:type_name -> google.protobuf.BytesValue
	29,  // 53: google.ads.googleads.v1.common.ImageAdInfo.ad_id_to_copy_image_from:type_name -> google.protobuf.Int64Value
	26,  // 54: google.ads.googleads.v1.common.VideoTrueViewInStreamAdInfo.action_button_label:type_name -> google.protobuf.StringValue
	26,  // 55: google.ads.googleads.v1.common.VideoTrueViewInStreamAdInfo.action_headline:type_name -> google.protobuf.StringValue
	26,  // 56: google.ads.googleads.v1.common.VideoTrueViewInStreamAdInfo.companion_banner:type_name -> google.protobuf.StringValue
	26,  // 57: google.ads.googleads.v1.common.VideoOutstreamAdInfo.headline:type_name -> google.protobuf.StringValue
	26,  // 58: google.ads.googleads.v1.common.VideoOutstreamAdInfo.description:type_name -> google.protobuf.StringValue
	26,  // 59: google.ads.googleads.v1.common.VideoAdInfo.media_file:type_name -> google.protobuf.StringValue
	16,  // 60: google.ads.googleads.v1.common.VideoAdInfo.in_stream:type_name -> google.ads.googleads.v1.common.VideoTrueViewInStreamAdInfo
	14,  // 61: google.ads.googleads.v1.common.VideoAdInfo.bumper:type_name -> google.ads.googleads.v1.common.VideoBumperInStreamAdInfo
	17,  // 62: google.ads.googleads.v1.common.VideoAdInfo.out_stream:type_name -> google.ads.googleads.v1.common.VideoOutstreamAdInfo
	15,  // 63: google.ads.googleads.v1.common.VideoAdInfo.non_skippable:type_name -> google.ads.googleads.v1.common.VideoNonSkippableInStreamAdInfo
	32,  // 64: google.ads.googleads.v1.common.ResponsiveSearchAdInfo.headlines:type_name -> google.ads.googleads.v1.common.AdTextAsset
	32,  // 65: google.ads.googleads.v1.common.ResponsiveSearchAdInfo.descriptions:type_name -> google.ads.googleads.v1.common.AdTextAsset
	26,  // 66: google.ads.googleads.v1.common.ResponsiveSearchAdInfo.path1:type_name -> google.protobuf.StringValue
	26,  // 67: google.ads.googleads.v1.common.ResponsiveSearchAdInfo.path2:type_name -> google.protobuf.StringValue
	26,  // 68: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.short_headline:type_name -> google.protobuf.StringValue
	26,  // 69: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.long_headline:type_name -> google.protobuf.StringValue
	26,  // 70: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.description:type_name -> google.protobuf.StringValue
	26,  // 71: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.business_name:type_name -> google.protobuf.StringValue
	27,  // 72: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.allow_flexible_color:type_name -> google.protobuf.BoolValue
	26,  // 73: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.accent_color:type_name -> google.protobuf.StringValue
	26,  // 74: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.main_color:type_name -> google.protobuf.StringValue
	26,  // 75: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.call_to_action_text:type_name -> google.protobuf.StringValue
	26,  // 76: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.logo_image:type_name -> google.protobuf.StringValue
	26,  // 77: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.square_logo_image:type_name -> google.protobuf.StringValue
	26,  // 78: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.marketing_image:type_name -> google.protobuf.StringValue
	26,  // 79: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.square_marketing_image:type_name -> google.protobuf.StringValue
	33,  // 80: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.format_setting:type_name -> google.ads.googleads.v1.enums.DisplayAdFormatSettingEnum.DisplayAdFormatSetting
	26,  // 81: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.price_prefix:type_name -> google.protobuf.StringValue
	26,  // 82: google.ads.googleads.v1.common.LegacyResponsiveDisplayAdInfo.promo_text:type_name -> google.protobuf.StringValue
	32,  // 83: google.ads.googleads.v1.common.AppAdInfo.mandatory_ad_text:type_name -> google.ads.googleads.v1.common.AdTextAsset
	32,  // 84: google.ads.googleads.v1.common.AppAdInfo.headlines:type_name -> google.ads.googleads.v1.common.AdTextAsset
	32,  // 85: google.ads.googleads.v1.common.AppAdInfo.descriptions:type_name -> google.ads.googleads.v1.common.AdTextAsset
	34,  // 86: google.ads.googleads.v1.common.AppAdInfo.images:type_name -> google.ads.googleads.v1.common.AdImageAsset
	35,  // 87: google.ads.googleads.v1.common.AppAdInfo.youtube_videos:type_name -> google.ads.googleads.v1.common.AdVideoAsset
	36,  // 88: google.ads.googleads.v1.common.AppAdInfo.html5_media_bundles:type_name -> google.ads.googleads.v1.common.AdMediaBundleAsset
	32,  // 89: google.ads.googleads.v1.common.AppEngagementAdInfo.headlines:type_name -> google.ads.googleads.v1.common.AdTextAsset
	32,  // 90: google.ads.googleads.v1.common.AppEngagementAdInfo.descriptions:type_name -> google.ads.googleads.v1.common.AdTextAsset
	34,  // 91: google.ads.googleads.v1.common.AppEngagementAdInfo.images:type_name -> google.ads.googleads.v1.common.AdImageAsset
	35,  // 92: google.ads.googleads.v1.common.AppEngagementAdInfo.videos:type_name -> google.ads.googleads.v1.common.AdVideoAsset
	26,  // 93: google.ads.googleads.v1.common.LegacyAppInstallAdInfo.app_id:type_name -> google.protobuf.StringValue
	37,  // 94: google.ads.googleads.v1.common.LegacyAppInstallAdInfo.app_store:type_name -> google.ads.googleads.v1.enums.LegacyAppInstallAdAppStoreEnum.LegacyAppInstallAdAppStore
	26,  // 95: google.ads.googleads.v1.common.LegacyAppInstallAdInfo.headline:type_name -> google.protobuf.StringValue
	26,  // 96: google.ads.googleads.v1.common.LegacyAppInstallAdInfo.description1:type_name -> google.protobuf.StringValue
	26,  // 97: google.ads.googleads.v1.common.LegacyAppInstallAdInfo.description2:type_name -> google.protobuf.StringValue
	34,  // 98: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.marketing_images:type_name -> google.ads.googleads.v1.common.AdImageAsset
	34,  // 99: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.square_marketing_images:type_name -> google.ads.googleads.v1.common.AdImageAsset
	34,  // 100: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.logo_images:type_name -> google.ads.googleads.v1.common.AdImageAsset
	34,  // 101: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.square_logo_images:type_name -> google.ads.googleads.v1.common.AdImageAsset
	32,  // 102: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.headlines:type_name -> google.ads.googleads.v1.common.AdTextAsset
	32,  // 103: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.long_headline:type_name -> google.ads.googleads.v1.common.AdTextAsset
	32,  // 104: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.descriptions:type_name -> google.ads.googleads.v1.common.AdTextAsset
	35,  // 105: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.youtube_videos:type_name -> google.ads.googleads.v1.common.AdVideoAsset
	26,  // 106: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.business_name:type_name -> google.protobuf.StringValue
	26,  // 107: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.main_color:type_name -> google.protobuf.StringValue
	26,  // 108: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.accent_color:type_name -> google.protobuf.StringValue
	27,  // 109: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.allow_flexible_color:type_name -> google.protobuf.BoolValue
	26,  // 110: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.call_to_action_text:type_name -> google.protobuf.StringValue
	26,  // 111: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.price_prefix:type_name -> google.protobuf.StringValue
	26,  // 112: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.promo_text:type_name -> google.protobuf.StringValue
	33,  // 113: google.ads.googleads.v1.common.ResponsiveDisplayAdInfo.format_setting:type_name -> google.ads.googleads.v1.enums.DisplayAdFormatSettingEnum.DisplayAdFormatSetting
	38,  // 114: google.ads.googleads.v1.common.DisplayUploadAdInfo.display_upload_product_type:type_name -> google.ads.googleads.v1.enums.DisplayUploadProductTypeEnum.DisplayUploadProductType
	36,  // 115: google.ads.googleads.v1.common.DisplayUploadAdInfo.media_bundle:type_name -> google.ads.googleads.v1.common.AdMediaBundleAsset
	116, // [116:116] is the sub-list for method output_type
	116, // [116:116] is the sub-list for method input_type
	116, // [116:116] is the sub-list for extension type_name
	116, // [116:116] is the sub-list for extension extendee
	0,   // [0:116] is the sub-list for field type_name
}

func init() { file_google_ads_googleads_v1_common_ad_type_infos_proto_init() }
func file_google_ads_googleads_v1_common_ad_type_infos_proto_init() {
	if File_google_ads_googleads_v1_common_ad_type_infos_proto != nil {
		return
	}
	file_google_ads_googleads_v1_common_ad_asset_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpandedTextAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallOnlyAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpandedDynamicSearchAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotelAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShoppingSmartAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShoppingProductAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShoppingComparisonListingAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmailAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmailTeaser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisplayCallToAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductImage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductVideo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoBumperInStreamAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoNonSkippableInStreamAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoTrueViewInStreamAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoOutstreamAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResponsiveSearchAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LegacyResponsiveDisplayAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppEngagementAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LegacyAppInstallAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResponsiveDisplayAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisplayUploadAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*ImageAdInfo_MediaFile)(nil),
		(*ImageAdInfo_Data)(nil),
		(*ImageAdInfo_AdIdToCopyImageFrom)(nil),
	}
	file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[18].OneofWrappers = []interface{}{
		(*VideoAdInfo_InStream)(nil),
		(*VideoAdInfo_Bumper)(nil),
		(*VideoAdInfo_OutStream)(nil),
		(*VideoAdInfo_NonSkippable)(nil),
	}
	file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes[25].OneofWrappers = []interface{}{
		(*DisplayUploadAdInfo_MediaBundle)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_ads_googleads_v1_common_ad_type_infos_proto_goTypes,
		DependencyIndexes: file_google_ads_googleads_v1_common_ad_type_infos_proto_depIdxs,
		MessageInfos:      file_google_ads_googleads_v1_common_ad_type_infos_proto_msgTypes,
	}.Build()
	File_google_ads_googleads_v1_common_ad_type_infos_proto = out.File
	file_google_ads_googleads_v1_common_ad_type_infos_proto_rawDesc = nil
	file_google_ads_googleads_v1_common_ad_type_infos_proto_goTypes = nil
	file_google_ads_googleads_v1_common_ad_type_infos_proto_depIdxs = nil
}
