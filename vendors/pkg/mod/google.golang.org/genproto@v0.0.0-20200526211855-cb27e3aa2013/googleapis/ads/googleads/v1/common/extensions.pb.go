// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.22.0
// 	protoc        v3.11.2
// source: google/ads/googleads/v1/common/extensions.proto

package common

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	enums "google.golang.org/genproto/googleapis/ads/googleads/v1/enums"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// Represents an App extension.
type AppFeedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The visible text displayed when the link is rendered in an ad.
	// This string must not be empty, and the length of this string should
	// be between 1 and 25, inclusive.
	LinkText *wrappers.StringValue `protobuf:"bytes,1,opt,name=link_text,json=linkText,proto3" json:"link_text,omitempty"`
	// The store-specific ID for the target application.
	// This string must not be empty.
	AppId *wrappers.StringValue `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// The application store that the target application belongs to.
	// This field is required.
	AppStore enums.AppStoreEnum_AppStore `protobuf:"varint,3,opt,name=app_store,json=appStore,proto3,enum=google.ads.googleads.v1.enums.AppStoreEnum_AppStore" json:"app_store,omitempty"`
	// A list of possible final URLs after all cross domain redirects.
	// This list must not be empty.
	FinalUrls []*wrappers.StringValue `protobuf:"bytes,4,rep,name=final_urls,json=finalUrls,proto3" json:"final_urls,omitempty"`
	// A list of possible final mobile URLs after all cross domain redirects.
	FinalMobileUrls []*wrappers.StringValue `protobuf:"bytes,5,rep,name=final_mobile_urls,json=finalMobileUrls,proto3" json:"final_mobile_urls,omitempty"`
	// URL template for constructing a tracking URL. Default value is "{lpurl}".
	TrackingUrlTemplate *wrappers.StringValue `protobuf:"bytes,6,opt,name=tracking_url_template,json=trackingUrlTemplate,proto3" json:"tracking_url_template,omitempty"`
	// A list of mappings to be used for substituting URL custom parameter tags in
	// the tracking_url_template, final_urls, and/or final_mobile_urls.
	UrlCustomParameters []*CustomParameter `protobuf:"bytes,7,rep,name=url_custom_parameters,json=urlCustomParameters,proto3" json:"url_custom_parameters,omitempty"`
	// URL template for appending params to landing page URLs served with parallel
	// tracking.
	FinalUrlSuffix *wrappers.StringValue `protobuf:"bytes,8,opt,name=final_url_suffix,json=finalUrlSuffix,proto3" json:"final_url_suffix,omitempty"`
}

func (x *AppFeedItem) Reset() {
	*x = AppFeedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppFeedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppFeedItem) ProtoMessage() {}

func (x *AppFeedItem) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppFeedItem.ProtoReflect.Descriptor instead.
func (*AppFeedItem) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_extensions_proto_rawDescGZIP(), []int{0}
}

func (x *AppFeedItem) GetLinkText() *wrappers.StringValue {
	if x != nil {
		return x.LinkText
	}
	return nil
}

func (x *AppFeedItem) GetAppId() *wrappers.StringValue {
	if x != nil {
		return x.AppId
	}
	return nil
}

func (x *AppFeedItem) GetAppStore() enums.AppStoreEnum_AppStore {
	if x != nil {
		return x.AppStore
	}
	return enums.AppStoreEnum_UNSPECIFIED
}

func (x *AppFeedItem) GetFinalUrls() []*wrappers.StringValue {
	if x != nil {
		return x.FinalUrls
	}
	return nil
}

func (x *AppFeedItem) GetFinalMobileUrls() []*wrappers.StringValue {
	if x != nil {
		return x.FinalMobileUrls
	}
	return nil
}

func (x *AppFeedItem) GetTrackingUrlTemplate() *wrappers.StringValue {
	if x != nil {
		return x.TrackingUrlTemplate
	}
	return nil
}

func (x *AppFeedItem) GetUrlCustomParameters() []*CustomParameter {
	if x != nil {
		return x.UrlCustomParameters
	}
	return nil
}

func (x *AppFeedItem) GetFinalUrlSuffix() *wrappers.StringValue {
	if x != nil {
		return x.FinalUrlSuffix
	}
	return nil
}

// Represents a Call extension.
type CallFeedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The advertiser's phone number to append to the ad.
	// This string must not be empty.
	PhoneNumber *wrappers.StringValue `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// Uppercase two-letter country code of the advertiser's phone number.
	// This string must not be empty.
	CountryCode *wrappers.StringValue `protobuf:"bytes,2,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// Indicates whether call tracking is enabled. By default, call tracking is
	// not enabled.
	CallTrackingEnabled *wrappers.BoolValue `protobuf:"bytes,3,opt,name=call_tracking_enabled,json=callTrackingEnabled,proto3" json:"call_tracking_enabled,omitempty"`
	// The conversion action to attribute a call conversion to. If not set a
	// default conversion action is used. This field only has effect if
	// call_tracking_enabled is set to true. Otherwise this field is ignored.
	CallConversionAction *wrappers.StringValue `protobuf:"bytes,4,opt,name=call_conversion_action,json=callConversionAction,proto3" json:"call_conversion_action,omitempty"`
	// If true, disable call conversion tracking. call_conversion_action should
	// not be set if this is true. Optional.
	CallConversionTrackingDisabled *wrappers.BoolValue `protobuf:"bytes,5,opt,name=call_conversion_tracking_disabled,json=callConversionTrackingDisabled,proto3" json:"call_conversion_tracking_disabled,omitempty"`
	// Enum value that indicates whether this call extension uses its own call
	// conversion setting (or just have call conversion disabled), or following
	// the account level setting.
	CallConversionReportingState enums.CallConversionReportingStateEnum_CallConversionReportingState `protobuf:"varint,6,opt,name=call_conversion_reporting_state,json=callConversionReportingState,proto3,enum=google.ads.googleads.v1.enums.CallConversionReportingStateEnum_CallConversionReportingState" json:"call_conversion_reporting_state,omitempty"`
}

func (x *CallFeedItem) Reset() {
	*x = CallFeedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallFeedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallFeedItem) ProtoMessage() {}

func (x *CallFeedItem) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallFeedItem.ProtoReflect.Descriptor instead.
func (*CallFeedItem) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_extensions_proto_rawDescGZIP(), []int{1}
}

func (x *CallFeedItem) GetPhoneNumber() *wrappers.StringValue {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *CallFeedItem) GetCountryCode() *wrappers.StringValue {
	if x != nil {
		return x.CountryCode
	}
	return nil
}

func (x *CallFeedItem) GetCallTrackingEnabled() *wrappers.BoolValue {
	if x != nil {
		return x.CallTrackingEnabled
	}
	return nil
}

func (x *CallFeedItem) GetCallConversionAction() *wrappers.StringValue {
	if x != nil {
		return x.CallConversionAction
	}
	return nil
}

func (x *CallFeedItem) GetCallConversionTrackingDisabled() *wrappers.BoolValue {
	if x != nil {
		return x.CallConversionTrackingDisabled
	}
	return nil
}

func (x *CallFeedItem) GetCallConversionReportingState() enums.CallConversionReportingStateEnum_CallConversionReportingState {
	if x != nil {
		return x.CallConversionReportingState
	}
	return enums.CallConversionReportingStateEnum_UNSPECIFIED
}

// Represents a callout extension.
type CalloutFeedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The callout text.
	// The length of this string should be between 1 and 25, inclusive.
	CalloutText *wrappers.StringValue `protobuf:"bytes,1,opt,name=callout_text,json=calloutText,proto3" json:"callout_text,omitempty"`
}

func (x *CalloutFeedItem) Reset() {
	*x = CalloutFeedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalloutFeedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalloutFeedItem) ProtoMessage() {}

func (x *CalloutFeedItem) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalloutFeedItem.ProtoReflect.Descriptor instead.
func (*CalloutFeedItem) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_extensions_proto_rawDescGZIP(), []int{2}
}

func (x *CalloutFeedItem) GetCalloutText() *wrappers.StringValue {
	if x != nil {
		return x.CalloutText
	}
	return nil
}

// Represents a location extension.
type LocationFeedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the business.
	BusinessName *wrappers.StringValue `protobuf:"bytes,1,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// Line 1 of the business address.
	AddressLine_1 *wrappers.StringValue `protobuf:"bytes,2,opt,name=address_line_1,json=addressLine1,proto3" json:"address_line_1,omitempty"`
	// Line 2 of the business address.
	AddressLine_2 *wrappers.StringValue `protobuf:"bytes,3,opt,name=address_line_2,json=addressLine2,proto3" json:"address_line_2,omitempty"`
	// City of the business address.
	City *wrappers.StringValue `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	// Province of the business address.
	Province *wrappers.StringValue `protobuf:"bytes,5,opt,name=province,proto3" json:"province,omitempty"`
	// Postal code of the business address.
	PostalCode *wrappers.StringValue `protobuf:"bytes,6,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	// Country code of the business address.
	CountryCode *wrappers.StringValue `protobuf:"bytes,7,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// Phone number of the business.
	PhoneNumber *wrappers.StringValue `protobuf:"bytes,8,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *LocationFeedItem) Reset() {
	*x = LocationFeedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationFeedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationFeedItem) ProtoMessage() {}

func (x *LocationFeedItem) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationFeedItem.ProtoReflect.Descriptor instead.
func (*LocationFeedItem) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_extensions_proto_rawDescGZIP(), []int{3}
}

func (x *LocationFeedItem) GetBusinessName() *wrappers.StringValue {
	if x != nil {
		return x.BusinessName
	}
	return nil
}

func (x *LocationFeedItem) GetAddressLine_1() *wrappers.StringValue {
	if x != nil {
		return x.AddressLine_1
	}
	return nil
}

func (x *LocationFeedItem) GetAddressLine_2() *wrappers.StringValue {
	if x != nil {
		return x.AddressLine_2
	}
	return nil
}

func (x *LocationFeedItem) GetCity() *wrappers.StringValue {
	if x != nil {
		return x.City
	}
	return nil
}

func (x *LocationFeedItem) GetProvince() *wrappers.StringValue {
	if x != nil {
		return x.Province
	}
	return nil
}

func (x *LocationFeedItem) GetPostalCode() *wrappers.StringValue {
	if x != nil {
		return x.PostalCode
	}
	return nil
}

func (x *LocationFeedItem) GetCountryCode() *wrappers.StringValue {
	if x != nil {
		return x.CountryCode
	}
	return nil
}

func (x *LocationFeedItem) GetPhoneNumber() *wrappers.StringValue {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

// Represents an affiliate location extension.
type AffiliateLocationFeedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the business.
	BusinessName *wrappers.StringValue `protobuf:"bytes,1,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// Line 1 of the business address.
	AddressLine_1 *wrappers.StringValue `protobuf:"bytes,2,opt,name=address_line_1,json=addressLine1,proto3" json:"address_line_1,omitempty"`
	// Line 2 of the business address.
	AddressLine_2 *wrappers.StringValue `protobuf:"bytes,3,opt,name=address_line_2,json=addressLine2,proto3" json:"address_line_2,omitempty"`
	// City of the business address.
	City *wrappers.StringValue `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	// Province of the business address.
	Province *wrappers.StringValue `protobuf:"bytes,5,opt,name=province,proto3" json:"province,omitempty"`
	// Postal code of the business address.
	PostalCode *wrappers.StringValue `protobuf:"bytes,6,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	// Country code of the business address.
	CountryCode *wrappers.StringValue `protobuf:"bytes,7,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// Phone number of the business.
	PhoneNumber *wrappers.StringValue `protobuf:"bytes,8,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// Id of the retail chain that is advertised as a seller of your product.
	ChainId *wrappers.Int64Value `protobuf:"bytes,9,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	// Name of chain.
	ChainName *wrappers.StringValue `protobuf:"bytes,10,opt,name=chain_name,json=chainName,proto3" json:"chain_name,omitempty"`
}

func (x *AffiliateLocationFeedItem) Reset() {
	*x = AffiliateLocationFeedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AffiliateLocationFeedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AffiliateLocationFeedItem) ProtoMessage() {}

func (x *AffiliateLocationFeedItem) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AffiliateLocationFeedItem.ProtoReflect.Descriptor instead.
func (*AffiliateLocationFeedItem) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_extensions_proto_rawDescGZIP(), []int{4}
}

func (x *AffiliateLocationFeedItem) GetBusinessName() *wrappers.StringValue {
	if x != nil {
		return x.BusinessName
	}
	return nil
}

func (x *AffiliateLocationFeedItem) GetAddressLine_1() *wrappers.StringValue {
	if x != nil {
		return x.AddressLine_1
	}
	return nil
}

func (x *AffiliateLocationFeedItem) GetAddressLine_2() *wrappers.StringValue {
	if x != nil {
		return x.AddressLine_2
	}
	return nil
}

func (x *AffiliateLocationFeedItem) GetCity() *wrappers.StringValue {
	if x != nil {
		return x.City
	}
	return nil
}

func (x *AffiliateLocationFeedItem) GetProvince() *wrappers.StringValue {
	if x != nil {
		return x.Province
	}
	return nil
}

func (x *AffiliateLocationFeedItem) GetPostalCode() *wrappers.StringValue {
	if x != nil {
		return x.PostalCode
	}
	return nil
}

func (x *AffiliateLocationFeedItem) GetCountryCode() *wrappers.StringValue {
	if x != nil {
		return x.CountryCode
	}
	return nil
}

func (x *AffiliateLocationFeedItem) GetPhoneNumber() *wrappers.StringValue {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *AffiliateLocationFeedItem) GetChainId() *wrappers.Int64Value {
	if x != nil {
		return x.ChainId
	}
	return nil
}

func (x *AffiliateLocationFeedItem) GetChainName() *wrappers.StringValue {
	if x != nil {
		return x.ChainName
	}
	return nil
}

// An extension that users can click on to send a text message to the
// advertiser.
type TextMessageFeedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The business name to prepend to the message text.
	// This field is required.
	BusinessName *wrappers.StringValue `protobuf:"bytes,1,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// Uppercase two-letter country code of the advertiser's phone number.
	// This field is required.
	CountryCode *wrappers.StringValue `protobuf:"bytes,2,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// The advertiser's phone number the message will be sent to. Required.
	PhoneNumber *wrappers.StringValue `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// The text to show in the ad.
	// This field is required.
	Text *wrappers.StringValue `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	// The message text populated in the messaging app.
	ExtensionText *wrappers.StringValue `protobuf:"bytes,5,opt,name=extension_text,json=extensionText,proto3" json:"extension_text,omitempty"`
}

func (x *TextMessageFeedItem) Reset() {
	*x = TextMessageFeedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextMessageFeedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextMessageFeedItem) ProtoMessage() {}

func (x *TextMessageFeedItem) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextMessageFeedItem.ProtoReflect.Descriptor instead.
func (*TextMessageFeedItem) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_extensions_proto_rawDescGZIP(), []int{5}
}

func (x *TextMessageFeedItem) GetBusinessName() *wrappers.StringValue {
	if x != nil {
		return x.BusinessName
	}
	return nil
}

func (x *TextMessageFeedItem) GetCountryCode() *wrappers.StringValue {
	if x != nil {
		return x.CountryCode
	}
	return nil
}

func (x *TextMessageFeedItem) GetPhoneNumber() *wrappers.StringValue {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *TextMessageFeedItem) GetText() *wrappers.StringValue {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *TextMessageFeedItem) GetExtensionText() *wrappers.StringValue {
	if x != nil {
		return x.ExtensionText
	}
	return nil
}

// Represents a Price extension.
type PriceFeedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Price extension type of this extension.
	Type enums.PriceExtensionTypeEnum_PriceExtensionType `protobuf:"varint,1,opt,name=type,proto3,enum=google.ads.googleads.v1.enums.PriceExtensionTypeEnum_PriceExtensionType" json:"type,omitempty"`
	// Price qualifier for all offers of this price extension.
	PriceQualifier enums.PriceExtensionPriceQualifierEnum_PriceExtensionPriceQualifier `protobuf:"varint,2,opt,name=price_qualifier,json=priceQualifier,proto3,enum=google.ads.googleads.v1.enums.PriceExtensionPriceQualifierEnum_PriceExtensionPriceQualifier" json:"price_qualifier,omitempty"`
	// Tracking URL template for all offers of this price extension.
	TrackingUrlTemplate *wrappers.StringValue `protobuf:"bytes,3,opt,name=tracking_url_template,json=trackingUrlTemplate,proto3" json:"tracking_url_template,omitempty"`
	// The code of the language used for this price extension.
	LanguageCode *wrappers.StringValue `protobuf:"bytes,4,opt,name=language_code,json=languageCode,proto3" json:"language_code,omitempty"`
	// The price offerings in this price extension.
	PriceOfferings []*PriceOffer `protobuf:"bytes,5,rep,name=price_offerings,json=priceOfferings,proto3" json:"price_offerings,omitempty"`
	// URL template for appending params to landing page URLs served with parallel
	// tracking.
	FinalUrlSuffix *wrappers.StringValue `protobuf:"bytes,6,opt,name=final_url_suffix,json=finalUrlSuffix,proto3" json:"final_url_suffix,omitempty"`
}

func (x *PriceFeedItem) Reset() {
	*x = PriceFeedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceFeedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceFeedItem) ProtoMessage() {}

func (x *PriceFeedItem) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceFeedItem.ProtoReflect.Descriptor instead.
func (*PriceFeedItem) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_extensions_proto_rawDescGZIP(), []int{6}
}

func (x *PriceFeedItem) GetType() enums.PriceExtensionTypeEnum_PriceExtensionType {
	if x != nil {
		return x.Type
	}
	return enums.PriceExtensionTypeEnum_UNSPECIFIED
}

func (x *PriceFeedItem) GetPriceQualifier() enums.PriceExtensionPriceQualifierEnum_PriceExtensionPriceQualifier {
	if x != nil {
		return x.PriceQualifier
	}
	return enums.PriceExtensionPriceQualifierEnum_UNSPECIFIED
}

func (x *PriceFeedItem) GetTrackingUrlTemplate() *wrappers.StringValue {
	if x != nil {
		return x.TrackingUrlTemplate
	}
	return nil
}

func (x *PriceFeedItem) GetLanguageCode() *wrappers.StringValue {
	if x != nil {
		return x.LanguageCode
	}
	return nil
}

func (x *PriceFeedItem) GetPriceOfferings() []*PriceOffer {
	if x != nil {
		return x.PriceOfferings
	}
	return nil
}

func (x *PriceFeedItem) GetFinalUrlSuffix() *wrappers.StringValue {
	if x != nil {
		return x.FinalUrlSuffix
	}
	return nil
}

// Represents one price offer in a price extension.
type PriceOffer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Header text of this offer.
	Header *wrappers.StringValue `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Description text of this offer.
	Description *wrappers.StringValue `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// Price value of this offer.
	Price *Money `protobuf:"bytes,3,opt,name=price,proto3" json:"price,omitempty"`
	// Price unit for this offer.
	Unit enums.PriceExtensionPriceUnitEnum_PriceExtensionPriceUnit `protobuf:"varint,4,opt,name=unit,proto3,enum=google.ads.googleads.v1.enums.PriceExtensionPriceUnitEnum_PriceExtensionPriceUnit" json:"unit,omitempty"`
	// A list of possible final URLs after all cross domain redirects.
	FinalUrls []*wrappers.StringValue `protobuf:"bytes,5,rep,name=final_urls,json=finalUrls,proto3" json:"final_urls,omitempty"`
	// A list of possible final mobile URLs after all cross domain redirects.
	FinalMobileUrls []*wrappers.StringValue `protobuf:"bytes,6,rep,name=final_mobile_urls,json=finalMobileUrls,proto3" json:"final_mobile_urls,omitempty"`
}

func (x *PriceOffer) Reset() {
	*x = PriceOffer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceOffer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceOffer) ProtoMessage() {}

func (x *PriceOffer) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceOffer.ProtoReflect.Descriptor instead.
func (*PriceOffer) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_extensions_proto_rawDescGZIP(), []int{7}
}

func (x *PriceOffer) GetHeader() *wrappers.StringValue {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PriceOffer) GetDescription() *wrappers.StringValue {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *PriceOffer) GetPrice() *Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *PriceOffer) GetUnit() enums.PriceExtensionPriceUnitEnum_PriceExtensionPriceUnit {
	if x != nil {
		return x.Unit
	}
	return enums.PriceExtensionPriceUnitEnum_UNSPECIFIED
}

func (x *PriceOffer) GetFinalUrls() []*wrappers.StringValue {
	if x != nil {
		return x.FinalUrls
	}
	return nil
}

func (x *PriceOffer) GetFinalMobileUrls() []*wrappers.StringValue {
	if x != nil {
		return x.FinalMobileUrls
	}
	return nil
}

// Represents a Promotion extension.
type PromotionFeedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A freeform description of what the promotion is targeting.
	// This field is required.
	PromotionTarget *wrappers.StringValue `protobuf:"bytes,1,opt,name=promotion_target,json=promotionTarget,proto3" json:"promotion_target,omitempty"`
	// Enum that modifies the qualification of the discount.
	DiscountModifier enums.PromotionExtensionDiscountModifierEnum_PromotionExtensionDiscountModifier `protobuf:"varint,2,opt,name=discount_modifier,json=discountModifier,proto3,enum=google.ads.googleads.v1.enums.PromotionExtensionDiscountModifierEnum_PromotionExtensionDiscountModifier" json:"discount_modifier,omitempty"`
	// Start date of when the promotion is eligible to be redeemed.
	PromotionStartDate *wrappers.StringValue `protobuf:"bytes,7,opt,name=promotion_start_date,json=promotionStartDate,proto3" json:"promotion_start_date,omitempty"`
	// End date of when the promotion is eligible to be redeemed.
	PromotionEndDate *wrappers.StringValue `protobuf:"bytes,8,opt,name=promotion_end_date,json=promotionEndDate,proto3" json:"promotion_end_date,omitempty"`
	// The occasion the promotion was intended for.
	// If an occasion is set, the redemption window will need to fall within
	// the date range associated with the occasion.
	Occasion enums.PromotionExtensionOccasionEnum_PromotionExtensionOccasion `protobuf:"varint,9,opt,name=occasion,proto3,enum=google.ads.googleads.v1.enums.PromotionExtensionOccasionEnum_PromotionExtensionOccasion" json:"occasion,omitempty"`
	// A list of possible final URLs after all cross domain redirects.
	// This field is required.
	FinalUrls []*wrappers.StringValue `protobuf:"bytes,10,rep,name=final_urls,json=finalUrls,proto3" json:"final_urls,omitempty"`
	// A list of possible final mobile URLs after all cross domain redirects.
	FinalMobileUrls []*wrappers.StringValue `protobuf:"bytes,11,rep,name=final_mobile_urls,json=finalMobileUrls,proto3" json:"final_mobile_urls,omitempty"`
	// URL template for constructing a tracking URL.
	TrackingUrlTemplate *wrappers.StringValue `protobuf:"bytes,12,opt,name=tracking_url_template,json=trackingUrlTemplate,proto3" json:"tracking_url_template,omitempty"`
	// A list of mappings to be used for substituting URL custom parameter tags in
	// the tracking_url_template, final_urls, and/or final_mobile_urls.
	UrlCustomParameters []*CustomParameter `protobuf:"bytes,13,rep,name=url_custom_parameters,json=urlCustomParameters,proto3" json:"url_custom_parameters,omitempty"`
	// URL template for appending params to landing page URLs served with parallel
	// tracking.
	FinalUrlSuffix *wrappers.StringValue `protobuf:"bytes,14,opt,name=final_url_suffix,json=finalUrlSuffix,proto3" json:"final_url_suffix,omitempty"`
	// The language of the promotion.
	// Represented as BCP 47 language tag.
	LanguageCode *wrappers.StringValue `protobuf:"bytes,15,opt,name=language_code,json=languageCode,proto3" json:"language_code,omitempty"`
	// Discount type, can be percentage off or amount off.
	//
	// Types that are assignable to DiscountType:
	//	*PromotionFeedItem_PercentOff
	//	*PromotionFeedItem_MoneyAmountOff
	DiscountType isPromotionFeedItem_DiscountType `protobuf_oneof:"discount_type"`
	// Promotion trigger. Can be by promotion code or promo by eligible order
	// amount.
	//
	// Types that are assignable to PromotionTrigger:
	//	*PromotionFeedItem_PromotionCode
	//	*PromotionFeedItem_OrdersOverAmount
	PromotionTrigger isPromotionFeedItem_PromotionTrigger `protobuf_oneof:"promotion_trigger"`
}

func (x *PromotionFeedItem) Reset() {
	*x = PromotionFeedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PromotionFeedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PromotionFeedItem) ProtoMessage() {}

func (x *PromotionFeedItem) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PromotionFeedItem.ProtoReflect.Descriptor instead.
func (*PromotionFeedItem) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_extensions_proto_rawDescGZIP(), []int{8}
}

func (x *PromotionFeedItem) GetPromotionTarget() *wrappers.StringValue {
	if x != nil {
		return x.PromotionTarget
	}
	return nil
}

func (x *PromotionFeedItem) GetDiscountModifier() enums.PromotionExtensionDiscountModifierEnum_PromotionExtensionDiscountModifier {
	if x != nil {
		return x.DiscountModifier
	}
	return enums.PromotionExtensionDiscountModifierEnum_UNSPECIFIED
}

func (x *PromotionFeedItem) GetPromotionStartDate() *wrappers.StringValue {
	if x != nil {
		return x.PromotionStartDate
	}
	return nil
}

func (x *PromotionFeedItem) GetPromotionEndDate() *wrappers.StringValue {
	if x != nil {
		return x.PromotionEndDate
	}
	return nil
}

func (x *PromotionFeedItem) GetOccasion() enums.PromotionExtensionOccasionEnum_PromotionExtensionOccasion {
	if x != nil {
		return x.Occasion
	}
	return enums.PromotionExtensionOccasionEnum_UNSPECIFIED
}

func (x *PromotionFeedItem) GetFinalUrls() []*wrappers.StringValue {
	if x != nil {
		return x.FinalUrls
	}
	return nil
}

func (x *PromotionFeedItem) GetFinalMobileUrls() []*wrappers.StringValue {
	if x != nil {
		return x.FinalMobileUrls
	}
	return nil
}

func (x *PromotionFeedItem) GetTrackingUrlTemplate() *wrappers.StringValue {
	if x != nil {
		return x.TrackingUrlTemplate
	}
	return nil
}

func (x *PromotionFeedItem) GetUrlCustomParameters() []*CustomParameter {
	if x != nil {
		return x.UrlCustomParameters
	}
	return nil
}

func (x *PromotionFeedItem) GetFinalUrlSuffix() *wrappers.StringValue {
	if x != nil {
		return x.FinalUrlSuffix
	}
	return nil
}

func (x *PromotionFeedItem) GetLanguageCode() *wrappers.StringValue {
	if x != nil {
		return x.LanguageCode
	}
	return nil
}

func (m *PromotionFeedItem) GetDiscountType() isPromotionFeedItem_DiscountType {
	if m != nil {
		return m.DiscountType
	}
	return nil
}

func (x *PromotionFeedItem) GetPercentOff() *wrappers.Int64Value {
	if x, ok := x.GetDiscountType().(*PromotionFeedItem_PercentOff); ok {
		return x.PercentOff
	}
	return nil
}

func (x *PromotionFeedItem) GetMoneyAmountOff() *Money {
	if x, ok := x.GetDiscountType().(*PromotionFeedItem_MoneyAmountOff); ok {
		return x.MoneyAmountOff
	}
	return nil
}

func (m *PromotionFeedItem) GetPromotionTrigger() isPromotionFeedItem_PromotionTrigger {
	if m != nil {
		return m.PromotionTrigger
	}
	return nil
}

func (x *PromotionFeedItem) GetPromotionCode() *wrappers.StringValue {
	if x, ok := x.GetPromotionTrigger().(*PromotionFeedItem_PromotionCode); ok {
		return x.PromotionCode
	}
	return nil
}

func (x *PromotionFeedItem) GetOrdersOverAmount() *Money {
	if x, ok := x.GetPromotionTrigger().(*PromotionFeedItem_OrdersOverAmount); ok {
		return x.OrdersOverAmount
	}
	return nil
}

type isPromotionFeedItem_DiscountType interface {
	isPromotionFeedItem_DiscountType()
}

type PromotionFeedItem_PercentOff struct {
	// Percentage off discount in the promotion in micros.
	// One million is equivalent to one percent.
	// Either this or money_off_amount is required.
	PercentOff *wrappers.Int64Value `protobuf:"bytes,3,opt,name=percent_off,json=percentOff,proto3,oneof"`
}

type PromotionFeedItem_MoneyAmountOff struct {
	// Money amount off for discount in the promotion.
	// Either this or percent_off is required.
	MoneyAmountOff *Money `protobuf:"bytes,4,opt,name=money_amount_off,json=moneyAmountOff,proto3,oneof"`
}

func (*PromotionFeedItem_PercentOff) isPromotionFeedItem_DiscountType() {}

func (*PromotionFeedItem_MoneyAmountOff) isPromotionFeedItem_DiscountType() {}

type isPromotionFeedItem_PromotionTrigger interface {
	isPromotionFeedItem_PromotionTrigger()
}

type PromotionFeedItem_PromotionCode struct {
	// A code the user should use in order to be eligible for the promotion.
	PromotionCode *wrappers.StringValue `protobuf:"bytes,5,opt,name=promotion_code,json=promotionCode,proto3,oneof"`
}

type PromotionFeedItem_OrdersOverAmount struct {
	// The amount the total order needs to be for the user to be eligible for
	// the promotion.
	OrdersOverAmount *Money `protobuf:"bytes,6,opt,name=orders_over_amount,json=ordersOverAmount,proto3,oneof"`
}

func (*PromotionFeedItem_PromotionCode) isPromotionFeedItem_PromotionTrigger() {}

func (*PromotionFeedItem_OrdersOverAmount) isPromotionFeedItem_PromotionTrigger() {}

// Represents a structured snippet extension.
type StructuredSnippetFeedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The header of the snippet.
	// This string must not be empty.
	Header *wrappers.StringValue `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// The values in the snippet.
	// The maximum size of this collection is 10.
	Values []*wrappers.StringValue `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *StructuredSnippetFeedItem) Reset() {
	*x = StructuredSnippetFeedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StructuredSnippetFeedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StructuredSnippetFeedItem) ProtoMessage() {}

func (x *StructuredSnippetFeedItem) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StructuredSnippetFeedItem.ProtoReflect.Descriptor instead.
func (*StructuredSnippetFeedItem) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_extensions_proto_rawDescGZIP(), []int{9}
}

func (x *StructuredSnippetFeedItem) GetHeader() *wrappers.StringValue {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *StructuredSnippetFeedItem) GetValues() []*wrappers.StringValue {
	if x != nil {
		return x.Values
	}
	return nil
}

// Represents a sitelink extension.
type SitelinkFeedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// URL display text for the sitelink.
	// The length of this string should be between 1 and 25, inclusive.
	LinkText *wrappers.StringValue `protobuf:"bytes,1,opt,name=link_text,json=linkText,proto3" json:"link_text,omitempty"`
	// First line of the description for the sitelink.
	// If this value is set, line2 must also be set.
	// The length of this string should be between 0 and 35, inclusive.
	Line1 *wrappers.StringValue `protobuf:"bytes,2,opt,name=line1,proto3" json:"line1,omitempty"`
	// Second line of the description for the sitelink.
	// If this value is set, line1 must also be set.
	// The length of this string should be between 0 and 35, inclusive.
	Line2 *wrappers.StringValue `protobuf:"bytes,3,opt,name=line2,proto3" json:"line2,omitempty"`
	// A list of possible final URLs after all cross domain redirects.
	FinalUrls []*wrappers.StringValue `protobuf:"bytes,4,rep,name=final_urls,json=finalUrls,proto3" json:"final_urls,omitempty"`
	// A list of possible final mobile URLs after all cross domain redirects.
	FinalMobileUrls []*wrappers.StringValue `protobuf:"bytes,5,rep,name=final_mobile_urls,json=finalMobileUrls,proto3" json:"final_mobile_urls,omitempty"`
	// URL template for constructing a tracking URL.
	TrackingUrlTemplate *wrappers.StringValue `protobuf:"bytes,6,opt,name=tracking_url_template,json=trackingUrlTemplate,proto3" json:"tracking_url_template,omitempty"`
	// A list of mappings to be used for substituting URL custom parameter tags in
	// the tracking_url_template, final_urls, and/or final_mobile_urls.
	UrlCustomParameters []*CustomParameter `protobuf:"bytes,7,rep,name=url_custom_parameters,json=urlCustomParameters,proto3" json:"url_custom_parameters,omitempty"`
	// Final URL suffix to be appended to landing page URLs served with
	// parallel tracking.
	FinalUrlSuffix *wrappers.StringValue `protobuf:"bytes,8,opt,name=final_url_suffix,json=finalUrlSuffix,proto3" json:"final_url_suffix,omitempty"`
}

func (x *SitelinkFeedItem) Reset() {
	*x = SitelinkFeedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SitelinkFeedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SitelinkFeedItem) ProtoMessage() {}

func (x *SitelinkFeedItem) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_extensions_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SitelinkFeedItem.ProtoReflect.Descriptor instead.
func (*SitelinkFeedItem) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_extensions_proto_rawDescGZIP(), []int{10}
}

func (x *SitelinkFeedItem) GetLinkText() *wrappers.StringValue {
	if x != nil {
		return x.LinkText
	}
	return nil
}

func (x *SitelinkFeedItem) GetLine1() *wrappers.StringValue {
	if x != nil {
		return x.Line1
	}
	return nil
}

func (x *SitelinkFeedItem) GetLine2() *wrappers.StringValue {
	if x != nil {
		return x.Line2
	}
	return nil
}

func (x *SitelinkFeedItem) GetFinalUrls() []*wrappers.StringValue {
	if x != nil {
		return x.FinalUrls
	}
	return nil
}

func (x *SitelinkFeedItem) GetFinalMobileUrls() []*wrappers.StringValue {
	if x != nil {
		return x.FinalMobileUrls
	}
	return nil
}

func (x *SitelinkFeedItem) GetTrackingUrlTemplate() *wrappers.StringValue {
	if x != nil {
		return x.TrackingUrlTemplate
	}
	return nil
}

func (x *SitelinkFeedItem) GetUrlCustomParameters() []*CustomParameter {
	if x != nil {
		return x.UrlCustomParameters
	}
	return nil
}

func (x *SitelinkFeedItem) GetFinalUrlSuffix() *wrappers.StringValue {
	if x != nil {
		return x.FinalUrlSuffix
	}
	return nil
}

var File_google_ads_googleads_v1_common_extensions_proto protoreflect.FileDescriptor

var file_google_ads_googleads_v1_common_extensions_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x1a, 0x35, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x43, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x43,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x3e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x49, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x63, 0x63, 0x61,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd6, 0x04, 0x0a, 0x0b, 0x41, 0x70, 0x70,
	0x46, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x39, 0x0a, 0x09, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6c, 0x69, 0x6e, 0x6b, 0x54,
	0x65, 0x78, 0x74, 0x12, 0x33, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x51, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x70, 0x70, 0x53,
	0x74, 0x6f, 0x72, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x70, 0x70, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x52, 0x08, 0x61, 0x70, 0x70, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x66,
	0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x66,
	0x69, 0x6e, 0x61, 0x6c, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x48, 0x0a, 0x11, 0x66, 0x69, 0x6e, 0x61,
	0x6c, 0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0f, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x50, 0x0a, 0x15, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x75,
	0x72, 0x6c, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x13, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x63, 0x0a, 0x15, 0x75, 0x72, 0x6c, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x52, 0x13, 0x75, 0x72, 0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x46, 0x0a, 0x10, 0x66, 0x69, 0x6e,
	0x61, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0e, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x55, 0x72, 0x6c, 0x53, 0x75, 0x66, 0x66, 0x69,
	0x78, 0x22, 0xc1, 0x04, 0x0a, 0x0c, 0x43, 0x61, 0x6c, 0x6c, 0x46, 0x65, 0x65, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x3f, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x4e, 0x0a, 0x15, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x13, 0x63, 0x61, 0x6c, 0x6c, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x12, 0x52, 0x0a, 0x16, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x14, 0x63, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x65, 0x0a, 0x21, 0x63, 0x61, 0x6c, 0x6c,
	0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x1e, 0x63, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12,
	0xa3, 0x01, 0x0a, 0x1f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x5c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x1c, 0x63, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x52, 0x0a, 0x0f, 0x43, 0x61, 0x6c, 0x6c, 0x6f, 0x75, 0x74,
	0x46, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x3f, 0x0a, 0x0c, 0x63, 0x61, 0x6c, 0x6c,
	0x6f, 0x75, 0x74, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x63, 0x61,
	0x6c, 0x6c, 0x6f, 0x75, 0x74, 0x54, 0x65, 0x78, 0x74, 0x22, 0x8a, 0x04, 0x0a, 0x10, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x41,
	0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x42, 0x0a, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x31, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x4c, 0x69, 0x6e, 0x65, 0x31, 0x12, 0x42, 0x0a, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x32, 0x12, 0x30, 0x0a, 0x04, 0x63, 0x69, 0x74,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x38, 0x0a, 0x08, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x3d, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x88, 0x05, 0x0a, 0x19, 0x41, 0x66, 0x66, 0x69, 0x6c,
	0x69, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x41, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x31, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x31, 0x12, 0x42, 0x0a, 0x0e, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x32, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x32, 0x12,
	0x30, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x63, 0x69, 0x74,
	0x79, 0x12, 0x38, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x3d, 0x0a, 0x0b, 0x70,
	0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a,
	0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x08,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0xd1, 0x02, 0x0a, 0x13, 0x54, 0x65, 0x78, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x46, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x41, 0x0a, 0x0d, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x0c,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3f, 0x0a,
	0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x30,
	0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74,
	0x12, 0x43, 0x0a, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x22, 0xa7, 0x04, 0x0a, 0x0d, 0x50, 0x72, 0x69, 0x63, 0x65, 0x46,
	0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x5c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x48, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x5c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x45, 0x6e, 0x75, 0x6d,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x0e, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x50, 0x0a,
	0x15, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x13, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12,
	0x41, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x53, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x46, 0x0a, 0x10, 0x66, 0x69, 0x6e, 0x61, 0x6c,
	0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0e, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x55, 0x72, 0x6c, 0x53, 0x75, 0x66, 0x66, 0x69, 0x78, 0x22,
	0xae, 0x03, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x63, 0x65, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x12, 0x34,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x3e, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x66, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x52, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55,
	0x6e, 0x69, 0x74, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x3b, 0x0a, 0x0a, 0x66, 0x69, 0x6e,
	0x61, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x66, 0x69, 0x6e,
	0x61, 0x6c, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x48, 0x0a, 0x11, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f,
	0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0f, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x73,
	0x22, 0xa6, 0x0a, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65,
	0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x47, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12,
	0x95, 0x01, 0x0a, 0x11, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x68, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72, 0x45, 0x6e,
	0x75, 0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x10, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x4a, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x74, 0x0a, 0x08, 0x6f, 0x63, 0x63, 0x61, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x58, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x4f, 0x63, 0x63, 0x61, 0x73, 0x69, 0x6f, 0x6e,
	0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x4f, 0x63, 0x63, 0x61, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x08, 0x6f, 0x63, 0x63, 0x61, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0a, 0x66, 0x69, 0x6e,
	0x61, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x66, 0x69, 0x6e,
	0x61, 0x6c, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x48, 0x0a, 0x11, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f,
	0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0f, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x73,
	0x12, 0x50, 0x0a, 0x15, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c,
	0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x13, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x12, 0x63, 0x0a, 0x15, 0x75, 0x72, 0x6c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x52, 0x13, 0x75, 0x72, 0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x46, 0x0a, 0x10, 0x66, 0x69, 0x6e, 0x61, 0x6c,
	0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0e, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x55, 0x72, 0x6c, 0x53, 0x75, 0x66, 0x66, 0x69, 0x78, 0x12,
	0x41, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x3e, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x66,
	0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x4f,
	0x66, 0x66, 0x12, 0x51, 0x0a, 0x10, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x0e, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x4f, 0x66, 0x66, 0x12, 0x45, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x01, 0x52, 0x0d, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x55, 0x0a, 0x12,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48,
	0x01, 0x52, 0x10, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x22, 0x87, 0x01, 0x0a, 0x19, 0x53, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x64, 0x53, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x46,
	0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x34, 0x0a,
	0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x22, 0xbb, 0x04, 0x0a, 0x10, 0x53, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x6e, 0x6b,
	0x46, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x39, 0x0a, 0x09, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6c, 0x69, 0x6e, 0x6b, 0x54,
	0x65, 0x78, 0x74, 0x12, 0x32, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x12, 0x32, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x32,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x12, 0x3b, 0x0a, 0x0a, 0x66,
	0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x66,
	0x69, 0x6e, 0x61, 0x6c, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x48, 0x0a, 0x11, 0x66, 0x69, 0x6e, 0x61,
	0x6c, 0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0f, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x50, 0x0a, 0x15, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x75,
	0x72, 0x6c, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x13, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x63, 0x0a, 0x15, 0x75, 0x72, 0x6c, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x52, 0x13, 0x75, 0x72, 0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x46, 0x0a, 0x10, 0x66, 0x69, 0x6e,
	0x61, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0e, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x55, 0x72, 0x6c, 0x53, 0x75, 0x66, 0x66, 0x69,
	0x78, 0x42, 0xea, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x42, 0x0f, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x44, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67,
	0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0xa2, 0x02, 0x03, 0x47, 0x41, 0x41, 0xaa, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x41, 0x64, 0x73, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x2e, 0x56,
	0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0xca, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x5c, 0x41, 0x64, 0x73, 0x5c, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x5c,
	0x56, 0x31, 0x5c, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0xea, 0x02, 0x22, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x3a, 0x3a, 0x41, 0x64, 0x73, 0x3a, 0x3a, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41,
	0x64, 0x73, 0x3a, 0x3a, 0x56, 0x31, 0x3a, 0x3a, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_ads_googleads_v1_common_extensions_proto_rawDescOnce sync.Once
	file_google_ads_googleads_v1_common_extensions_proto_rawDescData = file_google_ads_googleads_v1_common_extensions_proto_rawDesc
)

func file_google_ads_googleads_v1_common_extensions_proto_rawDescGZIP() []byte {
	file_google_ads_googleads_v1_common_extensions_proto_rawDescOnce.Do(func() {
		file_google_ads_googleads_v1_common_extensions_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_ads_googleads_v1_common_extensions_proto_rawDescData)
	})
	return file_google_ads_googleads_v1_common_extensions_proto_rawDescData
}

var file_google_ads_googleads_v1_common_extensions_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_google_ads_googleads_v1_common_extensions_proto_goTypes = []interface{}{
	(*AppFeedItem)(nil),               // 0: google.ads.googleads.v1.common.AppFeedItem
	(*CallFeedItem)(nil),              // 1: google.ads.googleads.v1.common.CallFeedItem
	(*CalloutFeedItem)(nil),           // 2: google.ads.googleads.v1.common.CalloutFeedItem
	(*LocationFeedItem)(nil),          // 3: google.ads.googleads.v1.common.LocationFeedItem
	(*AffiliateLocationFeedItem)(nil), // 4: google.ads.googleads.v1.common.AffiliateLocationFeedItem
	(*TextMessageFeedItem)(nil),       // 5: google.ads.googleads.v1.common.TextMessageFeedItem
	(*PriceFeedItem)(nil),             // 6: google.ads.googleads.v1.common.PriceFeedItem
	(*PriceOffer)(nil),                // 7: google.ads.googleads.v1.common.PriceOffer
	(*PromotionFeedItem)(nil),         // 8: google.ads.googleads.v1.common.PromotionFeedItem
	(*StructuredSnippetFeedItem)(nil), // 9: google.ads.googleads.v1.common.StructuredSnippetFeedItem
	(*SitelinkFeedItem)(nil),          // 10: google.ads.googleads.v1.common.SitelinkFeedItem
	(*wrappers.StringValue)(nil),      // 11: google.protobuf.StringValue
	(enums.AppStoreEnum_AppStore)(0),  // 12: google.ads.googleads.v1.enums.AppStoreEnum.AppStore
	(*CustomParameter)(nil),           // 13: google.ads.googleads.v1.common.CustomParameter
	(*wrappers.BoolValue)(nil),        // 14: google.protobuf.BoolValue
	(enums.CallConversionReportingStateEnum_CallConversionReportingState)(0), // 15: google.ads.googleads.v1.enums.CallConversionReportingStateEnum.CallConversionReportingState
	(*wrappers.Int64Value)(nil),                                              // 16: google.protobuf.Int64Value
	(enums.PriceExtensionTypeEnum_PriceExtensionType)(0),                     // 17: google.ads.googleads.v1.enums.PriceExtensionTypeEnum.PriceExtensionType
	(enums.PriceExtensionPriceQualifierEnum_PriceExtensionPriceQualifier)(0), // 18: google.ads.googleads.v1.enums.PriceExtensionPriceQualifierEnum.PriceExtensionPriceQualifier
	(*Money)(nil), // 19: google.ads.googleads.v1.common.Money
	(enums.PriceExtensionPriceUnitEnum_PriceExtensionPriceUnit)(0),                       // 20: google.ads.googleads.v1.enums.PriceExtensionPriceUnitEnum.PriceExtensionPriceUnit
	(enums.PromotionExtensionDiscountModifierEnum_PromotionExtensionDiscountModifier)(0), // 21: google.ads.googleads.v1.enums.PromotionExtensionDiscountModifierEnum.PromotionExtensionDiscountModifier
	(enums.PromotionExtensionOccasionEnum_PromotionExtensionOccasion)(0),                 // 22: google.ads.googleads.v1.enums.PromotionExtensionOccasionEnum.PromotionExtensionOccasion
}
var file_google_ads_googleads_v1_common_extensions_proto_depIdxs = []int32{
	11, // 0: google.ads.googleads.v1.common.AppFeedItem.link_text:type_name -> google.protobuf.StringValue
	11, // 1: google.ads.googleads.v1.common.AppFeedItem.app_id:type_name -> google.protobuf.StringValue
	12, // 2: google.ads.googleads.v1.common.AppFeedItem.app_store:type_name -> google.ads.googleads.v1.enums.AppStoreEnum.AppStore
	11, // 3: google.ads.googleads.v1.common.AppFeedItem.final_urls:type_name -> google.protobuf.StringValue
	11, // 4: google.ads.googleads.v1.common.AppFeedItem.final_mobile_urls:type_name -> google.protobuf.StringValue
	11, // 5: google.ads.googleads.v1.common.AppFeedItem.tracking_url_template:type_name -> google.protobuf.StringValue
	13, // 6: google.ads.googleads.v1.common.AppFeedItem.url_custom_parameters:type_name -> google.ads.googleads.v1.common.CustomParameter
	11, // 7: google.ads.googleads.v1.common.AppFeedItem.final_url_suffix:type_name -> google.protobuf.StringValue
	11, // 8: google.ads.googleads.v1.common.CallFeedItem.phone_number:type_name -> google.protobuf.StringValue
	11, // 9: google.ads.googleads.v1.common.CallFeedItem.country_code:type_name -> google.protobuf.StringValue
	14, // 10: google.ads.googleads.v1.common.CallFeedItem.call_tracking_enabled:type_name -> google.protobuf.BoolValue
	11, // 11: google.ads.googleads.v1.common.CallFeedItem.call_conversion_action:type_name -> google.protobuf.StringValue
	14, // 12: google.ads.googleads.v1.common.CallFeedItem.call_conversion_tracking_disabled:type_name -> google.protobuf.BoolValue
	15, // 13: google.ads.googleads.v1.common.CallFeedItem.call_conversion_reporting_state:type_name -> google.ads.googleads.v1.enums.CallConversionReportingStateEnum.CallConversionReportingState
	11, // 14: google.ads.googleads.v1.common.CalloutFeedItem.callout_text:type_name -> google.protobuf.StringValue
	11, // 15: google.ads.googleads.v1.common.LocationFeedItem.business_name:type_name -> google.protobuf.StringValue
	11, // 16: google.ads.googleads.v1.common.LocationFeedItem.address_line_1:type_name -> google.protobuf.StringValue
	11, // 17: google.ads.googleads.v1.common.LocationFeedItem.address_line_2:type_name -> google.protobuf.StringValue
	11, // 18: google.ads.googleads.v1.common.LocationFeedItem.city:type_name -> google.protobuf.StringValue
	11, // 19: google.ads.googleads.v1.common.LocationFeedItem.province:type_name -> google.protobuf.StringValue
	11, // 20: google.ads.googleads.v1.common.LocationFeedItem.postal_code:type_name -> google.protobuf.StringValue
	11, // 21: google.ads.googleads.v1.common.LocationFeedItem.country_code:type_name -> google.protobuf.StringValue
	11, // 22: google.ads.googleads.v1.common.LocationFeedItem.phone_number:type_name -> google.protobuf.StringValue
	11, // 23: google.ads.googleads.v1.common.AffiliateLocationFeedItem.business_name:type_name -> google.protobuf.StringValue
	11, // 24: google.ads.googleads.v1.common.AffiliateLocationFeedItem.address_line_1:type_name -> google.protobuf.StringValue
	11, // 25: google.ads.googleads.v1.common.AffiliateLocationFeedItem.address_line_2:type_name -> google.protobuf.StringValue
	11, // 26: google.ads.googleads.v1.common.AffiliateLocationFeedItem.city:type_name -> google.protobuf.StringValue
	11, // 27: google.ads.googleads.v1.common.AffiliateLocationFeedItem.province:type_name -> google.protobuf.StringValue
	11, // 28: google.ads.googleads.v1.common.AffiliateLocationFeedItem.postal_code:type_name -> google.protobuf.StringValue
	11, // 29: google.ads.googleads.v1.common.AffiliateLocationFeedItem.country_code:type_name -> google.protobuf.StringValue
	11, // 30: google.ads.googleads.v1.common.AffiliateLocationFeedItem.phone_number:type_name -> google.protobuf.StringValue
	16, // 31: google.ads.googleads.v1.common.AffiliateLocationFeedItem.chain_id:type_name -> google.protobuf.Int64Value
	11, // 32: google.ads.googleads.v1.common.AffiliateLocationFeedItem.chain_name:type_name -> google.protobuf.StringValue
	11, // 33: google.ads.googleads.v1.common.TextMessageFeedItem.business_name:type_name -> google.protobuf.StringValue
	11, // 34: google.ads.googleads.v1.common.TextMessageFeedItem.country_code:type_name -> google.protobuf.StringValue
	11, // 35: google.ads.googleads.v1.common.TextMessageFeedItem.phone_number:type_name -> google.protobuf.StringValue
	11, // 36: google.ads.googleads.v1.common.TextMessageFeedItem.text:type_name -> google.protobuf.StringValue
	11, // 37: google.ads.googleads.v1.common.TextMessageFeedItem.extension_text:type_name -> google.protobuf.StringValue
	17, // 38: google.ads.googleads.v1.common.PriceFeedItem.type:type_name -> google.ads.googleads.v1.enums.PriceExtensionTypeEnum.PriceExtensionType
	18, // 39: google.ads.googleads.v1.common.PriceFeedItem.price_qualifier:type_name -> google.ads.googleads.v1.enums.PriceExtensionPriceQualifierEnum.PriceExtensionPriceQualifier
	11, // 40: google.ads.googleads.v1.common.PriceFeedItem.tracking_url_template:type_name -> google.protobuf.StringValue
	11, // 41: google.ads.googleads.v1.common.PriceFeedItem.language_code:type_name -> google.protobuf.StringValue
	7,  // 42: google.ads.googleads.v1.common.PriceFeedItem.price_offerings:type_name -> google.ads.googleads.v1.common.PriceOffer
	11, // 43: google.ads.googleads.v1.common.PriceFeedItem.final_url_suffix:type_name -> google.protobuf.StringValue
	11, // 44: google.ads.googleads.v1.common.PriceOffer.header:type_name -> google.protobuf.StringValue
	11, // 45: google.ads.googleads.v1.common.PriceOffer.description:type_name -> google.protobuf.StringValue
	19, // 46: google.ads.googleads.v1.common.PriceOffer.price:type_name -> google.ads.googleads.v1.common.Money
	20, // 47: google.ads.googleads.v1.common.PriceOffer.unit:type_name -> google.ads.googleads.v1.enums.PriceExtensionPriceUnitEnum.PriceExtensionPriceUnit
	11, // 48: google.ads.googleads.v1.common.PriceOffer.final_urls:type_name -> google.protobuf.StringValue
	11, // 49: google.ads.googleads.v1.common.PriceOffer.final_mobile_urls:type_name -> google.protobuf.StringValue
	11, // 50: google.ads.googleads.v1.common.PromotionFeedItem.promotion_target:type_name -> google.protobuf.StringValue
	21, // 51: google.ads.googleads.v1.common.PromotionFeedItem.discount_modifier:type_name -> google.ads.googleads.v1.enums.PromotionExtensionDiscountModifierEnum.PromotionExtensionDiscountModifier
	11, // 52: google.ads.googleads.v1.common.PromotionFeedItem.promotion_start_date:type_name -> google.protobuf.StringValue
	11, // 53: google.ads.googleads.v1.common.PromotionFeedItem.promotion_end_date:type_name -> google.protobuf.StringValue
	22, // 54: google.ads.googleads.v1.common.PromotionFeedItem.occasion:type_name -> google.ads.googleads.v1.enums.PromotionExtensionOccasionEnum.PromotionExtensionOccasion
	11, // 55: google.ads.googleads.v1.common.PromotionFeedItem.final_urls:type_name -> google.protobuf.StringValue
	11, // 56: google.ads.googleads.v1.common.PromotionFeedItem.final_mobile_urls:type_name -> google.protobuf.StringValue
	11, // 57: google.ads.googleads.v1.common.PromotionFeedItem.tracking_url_template:type_name -> google.protobuf.StringValue
	13, // 58: google.ads.googleads.v1.common.PromotionFeedItem.url_custom_parameters:type_name -> google.ads.googleads.v1.common.CustomParameter
	11, // 59: google.ads.googleads.v1.common.PromotionFeedItem.final_url_suffix:type_name -> google.protobuf.StringValue
	11, // 60: google.ads.googleads.v1.common.PromotionFeedItem.language_code:type_name -> google.protobuf.StringValue
	16, // 61: google.ads.googleads.v1.common.PromotionFeedItem.percent_off:type_name -> google.protobuf.Int64Value
	19, // 62: google.ads.googleads.v1.common.PromotionFeedItem.money_amount_off:type_name -> google.ads.googleads.v1.common.Money
	11, // 63: google.ads.googleads.v1.common.PromotionFeedItem.promotion_code:type_name -> google.protobuf.StringValue
	19, // 64: google.ads.googleads.v1.common.PromotionFeedItem.orders_over_amount:type_name -> google.ads.googleads.v1.common.Money
	11, // 65: google.ads.googleads.v1.common.StructuredSnippetFeedItem.header:type_name -> google.protobuf.StringValue
	11, // 66: google.ads.googleads.v1.common.StructuredSnippetFeedItem.values:type_name -> google.protobuf.StringValue
	11, // 67: google.ads.googleads.v1.common.SitelinkFeedItem.link_text:type_name -> google.protobuf.StringValue
	11, // 68: google.ads.googleads.v1.common.SitelinkFeedItem.line1:type_name -> google.protobuf.StringValue
	11, // 69: google.ads.googleads.v1.common.SitelinkFeedItem.line2:type_name -> google.protobuf.StringValue
	11, // 70: google.ads.googleads.v1.common.SitelinkFeedItem.final_urls:type_name -> google.protobuf.StringValue
	11, // 71: google.ads.googleads.v1.common.SitelinkFeedItem.final_mobile_urls:type_name -> google.protobuf.StringValue
	11, // 72: google.ads.googleads.v1.common.SitelinkFeedItem.tracking_url_template:type_name -> google.protobuf.StringValue
	13, // 73: google.ads.googleads.v1.common.SitelinkFeedItem.url_custom_parameters:type_name -> google.ads.googleads.v1.common.CustomParameter
	11, // 74: google.ads.googleads.v1.common.SitelinkFeedItem.final_url_suffix:type_name -> google.protobuf.StringValue
	75, // [75:75] is the sub-list for method output_type
	75, // [75:75] is the sub-list for method input_type
	75, // [75:75] is the sub-list for extension type_name
	75, // [75:75] is the sub-list for extension extendee
	0,  // [0:75] is the sub-list for field type_name
}

func init() { file_google_ads_googleads_v1_common_extensions_proto_init() }
func file_google_ads_googleads_v1_common_extensions_proto_init() {
	if File_google_ads_googleads_v1_common_extensions_proto != nil {
		return
	}
	file_google_ads_googleads_v1_common_custom_parameter_proto_init()
	file_google_ads_googleads_v1_common_feed_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_ads_googleads_v1_common_extensions_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppFeedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_extensions_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallFeedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_extensions_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalloutFeedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_extensions_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationFeedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_extensions_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AffiliateLocationFeedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_extensions_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextMessageFeedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_extensions_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceFeedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_extensions_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceOffer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_extensions_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PromotionFeedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_extensions_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StructuredSnippetFeedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_extensions_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SitelinkFeedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_ads_googleads_v1_common_extensions_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*PromotionFeedItem_PercentOff)(nil),
		(*PromotionFeedItem_MoneyAmountOff)(nil),
		(*PromotionFeedItem_PromotionCode)(nil),
		(*PromotionFeedItem_OrdersOverAmount)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_ads_googleads_v1_common_extensions_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_ads_googleads_v1_common_extensions_proto_goTypes,
		DependencyIndexes: file_google_ads_googleads_v1_common_extensions_proto_depIdxs,
		MessageInfos:      file_google_ads_googleads_v1_common_extensions_proto_msgTypes,
	}.Build()
	File_google_ads_googleads_v1_common_extensions_proto = out.File
	file_google_ads_googleads_v1_common_extensions_proto_rawDesc = nil
	file_google_ads_googleads_v1_common_extensions_proto_goTypes = nil
	file_google_ads_googleads_v1_common_extensions_proto_depIdxs = nil
}
