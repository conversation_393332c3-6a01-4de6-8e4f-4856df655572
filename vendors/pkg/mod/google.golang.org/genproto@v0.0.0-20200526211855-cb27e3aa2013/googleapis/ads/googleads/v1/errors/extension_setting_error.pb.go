// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.22.0
// 	protoc        v3.11.2
// source: google/ads/googleads/v1/errors/extension_setting_error.proto

package errors

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// Enum describing possible extension setting errors.
type ExtensionSettingErrorEnum_ExtensionSettingError int32

const (
	// Enum unspecified.
	ExtensionSettingErrorEnum_UNSPECIFIED ExtensionSettingErrorEnum_ExtensionSettingError = 0
	// The received error code is not known in this version.
	ExtensionSettingErrorEnum_UNKNOWN ExtensionSettingErrorEnum_ExtensionSettingError = 1
	// A platform restriction was provided without input extensions or existing
	// extensions.
	ExtensionSettingErrorEnum_EXTENSIONS_REQUIRED ExtensionSettingErrorEnum_ExtensionSettingError = 2
	// The provided feed type does not correspond to the provided extensions.
	ExtensionSettingErrorEnum_FEED_TYPE_EXTENSION_TYPE_MISMATCH ExtensionSettingErrorEnum_ExtensionSettingError = 3
	// The provided feed type cannot be used.
	ExtensionSettingErrorEnum_INVALID_FEED_TYPE ExtensionSettingErrorEnum_ExtensionSettingError = 4
	// The provided feed type cannot be used at the customer level.
	ExtensionSettingErrorEnum_INVALID_FEED_TYPE_FOR_CUSTOMER_EXTENSION_SETTING ExtensionSettingErrorEnum_ExtensionSettingError = 5
	// Cannot change a feed item field on a CREATE operation.
	ExtensionSettingErrorEnum_CANNOT_CHANGE_FEED_ITEM_ON_CREATE ExtensionSettingErrorEnum_ExtensionSettingError = 6
	// Cannot update an extension that is not already in this setting.
	ExtensionSettingErrorEnum_CANNOT_UPDATE_NEWLY_CREATED_EXTENSION ExtensionSettingErrorEnum_ExtensionSettingError = 7
	// There is no existing AdGroupExtensionSetting for this type.
	ExtensionSettingErrorEnum_NO_EXISTING_AD_GROUP_EXTENSION_SETTING_FOR_TYPE ExtensionSettingErrorEnum_ExtensionSettingError = 8
	// There is no existing CampaignExtensionSetting for this type.
	ExtensionSettingErrorEnum_NO_EXISTING_CAMPAIGN_EXTENSION_SETTING_FOR_TYPE ExtensionSettingErrorEnum_ExtensionSettingError = 9
	// There is no existing CustomerExtensionSetting for this type.
	ExtensionSettingErrorEnum_NO_EXISTING_CUSTOMER_EXTENSION_SETTING_FOR_TYPE ExtensionSettingErrorEnum_ExtensionSettingError = 10
	// The AdGroupExtensionSetting already exists. UPDATE should be used to
	// modify the existing AdGroupExtensionSetting.
	ExtensionSettingErrorEnum_AD_GROUP_EXTENSION_SETTING_ALREADY_EXISTS ExtensionSettingErrorEnum_ExtensionSettingError = 11
	// The CampaignExtensionSetting already exists. UPDATE should be used to
	// modify the existing CampaignExtensionSetting.
	ExtensionSettingErrorEnum_CAMPAIGN_EXTENSION_SETTING_ALREADY_EXISTS ExtensionSettingErrorEnum_ExtensionSettingError = 12
	// The CustomerExtensionSetting already exists. UPDATE should be used to
	// modify the existing CustomerExtensionSetting.
	ExtensionSettingErrorEnum_CUSTOMER_EXTENSION_SETTING_ALREADY_EXISTS ExtensionSettingErrorEnum_ExtensionSettingError = 13
	// An active ad group feed already exists for this place holder type.
	ExtensionSettingErrorEnum_AD_GROUP_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE ExtensionSettingErrorEnum_ExtensionSettingError = 14
	// An active campaign feed already exists for this place holder type.
	ExtensionSettingErrorEnum_CAMPAIGN_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE ExtensionSettingErrorEnum_ExtensionSettingError = 15
	// An active customer feed already exists for this place holder type.
	ExtensionSettingErrorEnum_CUSTOMER_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE ExtensionSettingErrorEnum_ExtensionSettingError = 16
	// Value is not within the accepted range.
	ExtensionSettingErrorEnum_VALUE_OUT_OF_RANGE ExtensionSettingErrorEnum_ExtensionSettingError = 17
	// Cannot simultaneously set specified field with final urls.
	ExtensionSettingErrorEnum_CANNOT_SET_FIELD_WITH_FINAL_URLS ExtensionSettingErrorEnum_ExtensionSettingError = 18
	// Must set field with final urls.
	ExtensionSettingErrorEnum_FINAL_URLS_NOT_SET ExtensionSettingErrorEnum_ExtensionSettingError = 19
	// Phone number for a call extension is invalid.
	ExtensionSettingErrorEnum_INVALID_PHONE_NUMBER ExtensionSettingErrorEnum_ExtensionSettingError = 20
	// Phone number for a call extension is not supported for the given country
	// code.
	ExtensionSettingErrorEnum_PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRY ExtensionSettingErrorEnum_ExtensionSettingError = 21
	// A carrier specific number in short format is not allowed for call
	// extensions.
	ExtensionSettingErrorEnum_CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWED ExtensionSettingErrorEnum_ExtensionSettingError = 22
	// Premium rate numbers are not allowed for call extensions.
	ExtensionSettingErrorEnum_PREMIUM_RATE_NUMBER_NOT_ALLOWED ExtensionSettingErrorEnum_ExtensionSettingError = 23
	// Phone number type for a call extension is not allowed.
	ExtensionSettingErrorEnum_DISALLOWED_NUMBER_TYPE ExtensionSettingErrorEnum_ExtensionSettingError = 24
	// Phone number for a call extension does not meet domestic format
	// requirements.
	ExtensionSettingErrorEnum_INVALID_DOMESTIC_PHONE_NUMBER_FORMAT ExtensionSettingErrorEnum_ExtensionSettingError = 25
	// Vanity phone numbers (i.e. those including letters) are not allowed for
	// call extensions.
	ExtensionSettingErrorEnum_VANITY_PHONE_NUMBER_NOT_ALLOWED ExtensionSettingErrorEnum_ExtensionSettingError = 26
	// Country code provided for a call extension is invalid.
	ExtensionSettingErrorEnum_INVALID_COUNTRY_CODE ExtensionSettingErrorEnum_ExtensionSettingError = 27
	// Call conversion type id provided for a call extension is invalid.
	ExtensionSettingErrorEnum_INVALID_CALL_CONVERSION_TYPE_ID ExtensionSettingErrorEnum_ExtensionSettingError = 28
	// For a call extension, the customer is not whitelisted for call tracking.
	ExtensionSettingErrorEnum_CUSTOMER_NOT_WHITELISTED_FOR_CALLTRACKING ExtensionSettingErrorEnum_ExtensionSettingError = 29
	// Call tracking is not supported for the given country for a call
	// extension.
	ExtensionSettingErrorEnum_CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY ExtensionSettingErrorEnum_ExtensionSettingError = 30
	// App id provided for an app extension is invalid.
	ExtensionSettingErrorEnum_INVALID_APP_ID ExtensionSettingErrorEnum_ExtensionSettingError = 31
	// Quotation marks present in the review text for a review extension.
	ExtensionSettingErrorEnum_QUOTES_IN_REVIEW_EXTENSION_SNIPPET ExtensionSettingErrorEnum_ExtensionSettingError = 32
	// Hyphen character present in the review text for a review extension.
	ExtensionSettingErrorEnum_HYPHENS_IN_REVIEW_EXTENSION_SNIPPET ExtensionSettingErrorEnum_ExtensionSettingError = 33
	// A blacklisted review source name or url was provided for a review
	// extension.
	ExtensionSettingErrorEnum_REVIEW_EXTENSION_SOURCE_NOT_ELIGIBLE ExtensionSettingErrorEnum_ExtensionSettingError = 34
	// Review source name should not be found in the review text.
	ExtensionSettingErrorEnum_SOURCE_NAME_IN_REVIEW_EXTENSION_TEXT ExtensionSettingErrorEnum_ExtensionSettingError = 35
	// Field must be set.
	ExtensionSettingErrorEnum_MISSING_FIELD ExtensionSettingErrorEnum_ExtensionSettingError = 36
	// Inconsistent currency codes.
	ExtensionSettingErrorEnum_INCONSISTENT_CURRENCY_CODES ExtensionSettingErrorEnum_ExtensionSettingError = 37
	// Price extension cannot have duplicated headers.
	ExtensionSettingErrorEnum_PRICE_EXTENSION_HAS_DUPLICATED_HEADERS ExtensionSettingErrorEnum_ExtensionSettingError = 38
	// Price item cannot have duplicated header and description.
	ExtensionSettingErrorEnum_PRICE_ITEM_HAS_DUPLICATED_HEADER_AND_DESCRIPTION ExtensionSettingErrorEnum_ExtensionSettingError = 39
	// Price extension has too few items
	ExtensionSettingErrorEnum_PRICE_EXTENSION_HAS_TOO_FEW_ITEMS ExtensionSettingErrorEnum_ExtensionSettingError = 40
	// Price extension has too many items
	ExtensionSettingErrorEnum_PRICE_EXTENSION_HAS_TOO_MANY_ITEMS ExtensionSettingErrorEnum_ExtensionSettingError = 41
	// The input value is not currently supported.
	ExtensionSettingErrorEnum_UNSUPPORTED_VALUE ExtensionSettingErrorEnum_ExtensionSettingError = 42
	// Unknown or unsupported device preference.
	ExtensionSettingErrorEnum_INVALID_DEVICE_PREFERENCE ExtensionSettingErrorEnum_ExtensionSettingError = 43
	// Invalid feed item schedule end time (i.e., endHour = 24 and
	// endMinute != 0).
	ExtensionSettingErrorEnum_INVALID_SCHEDULE_END ExtensionSettingErrorEnum_ExtensionSettingError = 45
	// Date time zone does not match the account's time zone.
	ExtensionSettingErrorEnum_DATE_TIME_MUST_BE_IN_ACCOUNT_TIME_ZONE ExtensionSettingErrorEnum_ExtensionSettingError = 47
	// Overlapping feed item schedule times (e.g., 7-10AM and 8-11AM) are not
	// allowed.
	ExtensionSettingErrorEnum_OVERLAPPING_SCHEDULES_NOT_ALLOWED ExtensionSettingErrorEnum_ExtensionSettingError = 48
	// Feed item schedule end time must be after start time.
	ExtensionSettingErrorEnum_SCHEDULE_END_NOT_AFTER_START ExtensionSettingErrorEnum_ExtensionSettingError = 49
	// There are too many feed item schedules per day.
	ExtensionSettingErrorEnum_TOO_MANY_SCHEDULES_PER_DAY ExtensionSettingErrorEnum_ExtensionSettingError = 50
	// Cannot edit the same extension feed item more than once in the same
	// request.
	ExtensionSettingErrorEnum_DUPLICATE_EXTENSION_FEED_ITEM_EDIT ExtensionSettingErrorEnum_ExtensionSettingError = 51
	// Invalid structured snippet header.
	ExtensionSettingErrorEnum_INVALID_SNIPPETS_HEADER ExtensionSettingErrorEnum_ExtensionSettingError = 52
	// Phone number with call tracking enabled is not supported for the
	// specified country.
	ExtensionSettingErrorEnum_PHONE_NUMBER_NOT_SUPPORTED_WITH_CALLTRACKING_FOR_COUNTRY ExtensionSettingErrorEnum_ExtensionSettingError = 53
	// The targeted adgroup must belong to the targeted campaign.
	ExtensionSettingErrorEnum_CAMPAIGN_TARGETING_MISMATCH ExtensionSettingErrorEnum_ExtensionSettingError = 54
	// The feed used by the ExtensionSetting is removed and cannot be operated
	// on. Remove the ExtensionSetting to allow a new one to be created using
	// an active feed.
	ExtensionSettingErrorEnum_CANNOT_OPERATE_ON_REMOVED_FEED ExtensionSettingErrorEnum_ExtensionSettingError = 55
	// The ExtensionFeedItem type is required for this operation.
	ExtensionSettingErrorEnum_EXTENSION_TYPE_REQUIRED ExtensionSettingErrorEnum_ExtensionSettingError = 56
	// The matching function that links the extension feed to the customer,
	// campaign, or ad group is not compatible with the ExtensionSetting
	// services.
	ExtensionSettingErrorEnum_INCOMPATIBLE_UNDERLYING_MATCHING_FUNCTION ExtensionSettingErrorEnum_ExtensionSettingError = 57
	// Start date must be before end date.
	ExtensionSettingErrorEnum_START_DATE_AFTER_END_DATE ExtensionSettingErrorEnum_ExtensionSettingError = 58
	// Input price is not in a valid format.
	ExtensionSettingErrorEnum_INVALID_PRICE_FORMAT ExtensionSettingErrorEnum_ExtensionSettingError = 59
	// The promotion time is invalid.
	ExtensionSettingErrorEnum_PROMOTION_INVALID_TIME ExtensionSettingErrorEnum_ExtensionSettingError = 60
	// Cannot set both percent discount and money discount fields.
	ExtensionSettingErrorEnum_PROMOTION_CANNOT_SET_PERCENT_DISCOUNT_AND_MONEY_DISCOUNT ExtensionSettingErrorEnum_ExtensionSettingError = 61
	// Cannot set both promotion code and orders over amount fields.
	ExtensionSettingErrorEnum_PROMOTION_CANNOT_SET_PROMOTION_CODE_AND_ORDERS_OVER_AMOUNT ExtensionSettingErrorEnum_ExtensionSettingError = 62
	// This field has too many decimal places specified.
	ExtensionSettingErrorEnum_TOO_MANY_DECIMAL_PLACES_SPECIFIED ExtensionSettingErrorEnum_ExtensionSettingError = 63
	// The language code is not valid.
	ExtensionSettingErrorEnum_INVALID_LANGUAGE_CODE ExtensionSettingErrorEnum_ExtensionSettingError = 64
	// The language is not supported.
	ExtensionSettingErrorEnum_UNSUPPORTED_LANGUAGE ExtensionSettingErrorEnum_ExtensionSettingError = 65
	// Customer hasn't consented for call recording, which is required for
	// adding/updating call extensions. Please see
	// https://support.google.com/google-ads/answer/7412639.
	ExtensionSettingErrorEnum_CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIRED ExtensionSettingErrorEnum_ExtensionSettingError = 66
	// The UPDATE operation does not specify any fields other than the resource
	// name in the update mask.
	ExtensionSettingErrorEnum_EXTENSION_SETTING_UPDATE_IS_A_NOOP ExtensionSettingErrorEnum_ExtensionSettingError = 67
)

// Enum value maps for ExtensionSettingErrorEnum_ExtensionSettingError.
var (
	ExtensionSettingErrorEnum_ExtensionSettingError_name = map[int32]string{
		0:  "UNSPECIFIED",
		1:  "UNKNOWN",
		2:  "EXTENSIONS_REQUIRED",
		3:  "FEED_TYPE_EXTENSION_TYPE_MISMATCH",
		4:  "INVALID_FEED_TYPE",
		5:  "INVALID_FEED_TYPE_FOR_CUSTOMER_EXTENSION_SETTING",
		6:  "CANNOT_CHANGE_FEED_ITEM_ON_CREATE",
		7:  "CANNOT_UPDATE_NEWLY_CREATED_EXTENSION",
		8:  "NO_EXISTING_AD_GROUP_EXTENSION_SETTING_FOR_TYPE",
		9:  "NO_EXISTING_CAMPAIGN_EXTENSION_SETTING_FOR_TYPE",
		10: "NO_EXISTING_CUSTOMER_EXTENSION_SETTING_FOR_TYPE",
		11: "AD_GROUP_EXTENSION_SETTING_ALREADY_EXISTS",
		12: "CAMPAIGN_EXTENSION_SETTING_ALREADY_EXISTS",
		13: "CUSTOMER_EXTENSION_SETTING_ALREADY_EXISTS",
		14: "AD_GROUP_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE",
		15: "CAMPAIGN_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE",
		16: "CUSTOMER_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE",
		17: "VALUE_OUT_OF_RANGE",
		18: "CANNOT_SET_FIELD_WITH_FINAL_URLS",
		19: "FINAL_URLS_NOT_SET",
		20: "INVALID_PHONE_NUMBER",
		21: "PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRY",
		22: "CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWED",
		23: "PREMIUM_RATE_NUMBER_NOT_ALLOWED",
		24: "DISALLOWED_NUMBER_TYPE",
		25: "INVALID_DOMESTIC_PHONE_NUMBER_FORMAT",
		26: "VANITY_PHONE_NUMBER_NOT_ALLOWED",
		27: "INVALID_COUNTRY_CODE",
		28: "INVALID_CALL_CONVERSION_TYPE_ID",
		29: "CUSTOMER_NOT_WHITELISTED_FOR_CALLTRACKING",
		30: "CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY",
		31: "INVALID_APP_ID",
		32: "QUOTES_IN_REVIEW_EXTENSION_SNIPPET",
		33: "HYPHENS_IN_REVIEW_EXTENSION_SNIPPET",
		34: "REVIEW_EXTENSION_SOURCE_NOT_ELIGIBLE",
		35: "SOURCE_NAME_IN_REVIEW_EXTENSION_TEXT",
		36: "MISSING_FIELD",
		37: "INCONSISTENT_CURRENCY_CODES",
		38: "PRICE_EXTENSION_HAS_DUPLICATED_HEADERS",
		39: "PRICE_ITEM_HAS_DUPLICATED_HEADER_AND_DESCRIPTION",
		40: "PRICE_EXTENSION_HAS_TOO_FEW_ITEMS",
		41: "PRICE_EXTENSION_HAS_TOO_MANY_ITEMS",
		42: "UNSUPPORTED_VALUE",
		43: "INVALID_DEVICE_PREFERENCE",
		45: "INVALID_SCHEDULE_END",
		47: "DATE_TIME_MUST_BE_IN_ACCOUNT_TIME_ZONE",
		48: "OVERLAPPING_SCHEDULES_NOT_ALLOWED",
		49: "SCHEDULE_END_NOT_AFTER_START",
		50: "TOO_MANY_SCHEDULES_PER_DAY",
		51: "DUPLICATE_EXTENSION_FEED_ITEM_EDIT",
		52: "INVALID_SNIPPETS_HEADER",
		53: "PHONE_NUMBER_NOT_SUPPORTED_WITH_CALLTRACKING_FOR_COUNTRY",
		54: "CAMPAIGN_TARGETING_MISMATCH",
		55: "CANNOT_OPERATE_ON_REMOVED_FEED",
		56: "EXTENSION_TYPE_REQUIRED",
		57: "INCOMPATIBLE_UNDERLYING_MATCHING_FUNCTION",
		58: "START_DATE_AFTER_END_DATE",
		59: "INVALID_PRICE_FORMAT",
		60: "PROMOTION_INVALID_TIME",
		61: "PROMOTION_CANNOT_SET_PERCENT_DISCOUNT_AND_MONEY_DISCOUNT",
		62: "PROMOTION_CANNOT_SET_PROMOTION_CODE_AND_ORDERS_OVER_AMOUNT",
		63: "TOO_MANY_DECIMAL_PLACES_SPECIFIED",
		64: "INVALID_LANGUAGE_CODE",
		65: "UNSUPPORTED_LANGUAGE",
		66: "CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIRED",
		67: "EXTENSION_SETTING_UPDATE_IS_A_NOOP",
	}
	ExtensionSettingErrorEnum_ExtensionSettingError_value = map[string]int32{
		"UNSPECIFIED":                       0,
		"UNKNOWN":                           1,
		"EXTENSIONS_REQUIRED":               2,
		"FEED_TYPE_EXTENSION_TYPE_MISMATCH": 3,
		"INVALID_FEED_TYPE":                 4,
		"INVALID_FEED_TYPE_FOR_CUSTOMER_EXTENSION_SETTING":           5,
		"CANNOT_CHANGE_FEED_ITEM_ON_CREATE":                          6,
		"CANNOT_UPDATE_NEWLY_CREATED_EXTENSION":                      7,
		"NO_EXISTING_AD_GROUP_EXTENSION_SETTING_FOR_TYPE":            8,
		"NO_EXISTING_CAMPAIGN_EXTENSION_SETTING_FOR_TYPE":            9,
		"NO_EXISTING_CUSTOMER_EXTENSION_SETTING_FOR_TYPE":            10,
		"AD_GROUP_EXTENSION_SETTING_ALREADY_EXISTS":                  11,
		"CAMPAIGN_EXTENSION_SETTING_ALREADY_EXISTS":                  12,
		"CUSTOMER_EXTENSION_SETTING_ALREADY_EXISTS":                  13,
		"AD_GROUP_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE":          14,
		"CAMPAIGN_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE":          15,
		"CUSTOMER_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE":          16,
		"VALUE_OUT_OF_RANGE":                                         17,
		"CANNOT_SET_FIELD_WITH_FINAL_URLS":                           18,
		"FINAL_URLS_NOT_SET":                                         19,
		"INVALID_PHONE_NUMBER":                                       20,
		"PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRY":                     21,
		"CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWED":                  22,
		"PREMIUM_RATE_NUMBER_NOT_ALLOWED":                            23,
		"DISALLOWED_NUMBER_TYPE":                                     24,
		"INVALID_DOMESTIC_PHONE_NUMBER_FORMAT":                       25,
		"VANITY_PHONE_NUMBER_NOT_ALLOWED":                            26,
		"INVALID_COUNTRY_CODE":                                       27,
		"INVALID_CALL_CONVERSION_TYPE_ID":                            28,
		"CUSTOMER_NOT_WHITELISTED_FOR_CALLTRACKING":                  29,
		"CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY":                     30,
		"INVALID_APP_ID":                                             31,
		"QUOTES_IN_REVIEW_EXTENSION_SNIPPET":                         32,
		"HYPHENS_IN_REVIEW_EXTENSION_SNIPPET":                        33,
		"REVIEW_EXTENSION_SOURCE_NOT_ELIGIBLE":                       34,
		"SOURCE_NAME_IN_REVIEW_EXTENSION_TEXT":                       35,
		"MISSING_FIELD":                                              36,
		"INCONSISTENT_CURRENCY_CODES":                                37,
		"PRICE_EXTENSION_HAS_DUPLICATED_HEADERS":                     38,
		"PRICE_ITEM_HAS_DUPLICATED_HEADER_AND_DESCRIPTION":           39,
		"PRICE_EXTENSION_HAS_TOO_FEW_ITEMS":                          40,
		"PRICE_EXTENSION_HAS_TOO_MANY_ITEMS":                         41,
		"UNSUPPORTED_VALUE":                                          42,
		"INVALID_DEVICE_PREFERENCE":                                  43,
		"INVALID_SCHEDULE_END":                                       45,
		"DATE_TIME_MUST_BE_IN_ACCOUNT_TIME_ZONE":                     47,
		"OVERLAPPING_SCHEDULES_NOT_ALLOWED":                          48,
		"SCHEDULE_END_NOT_AFTER_START":                               49,
		"TOO_MANY_SCHEDULES_PER_DAY":                                 50,
		"DUPLICATE_EXTENSION_FEED_ITEM_EDIT":                         51,
		"INVALID_SNIPPETS_HEADER":                                    52,
		"PHONE_NUMBER_NOT_SUPPORTED_WITH_CALLTRACKING_FOR_COUNTRY":   53,
		"CAMPAIGN_TARGETING_MISMATCH":                                54,
		"CANNOT_OPERATE_ON_REMOVED_FEED":                             55,
		"EXTENSION_TYPE_REQUIRED":                                    56,
		"INCOMPATIBLE_UNDERLYING_MATCHING_FUNCTION":                  57,
		"START_DATE_AFTER_END_DATE":                                  58,
		"INVALID_PRICE_FORMAT":                                       59,
		"PROMOTION_INVALID_TIME":                                     60,
		"PROMOTION_CANNOT_SET_PERCENT_DISCOUNT_AND_MONEY_DISCOUNT":   61,
		"PROMOTION_CANNOT_SET_PROMOTION_CODE_AND_ORDERS_OVER_AMOUNT": 62,
		"TOO_MANY_DECIMAL_PLACES_SPECIFIED":                          63,
		"INVALID_LANGUAGE_CODE":                                      64,
		"UNSUPPORTED_LANGUAGE":                                       65,
		"CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIRED":               66,
		"EXTENSION_SETTING_UPDATE_IS_A_NOOP":                         67,
	}
)

func (x ExtensionSettingErrorEnum_ExtensionSettingError) Enum() *ExtensionSettingErrorEnum_ExtensionSettingError {
	p := new(ExtensionSettingErrorEnum_ExtensionSettingError)
	*p = x
	return p
}

func (x ExtensionSettingErrorEnum_ExtensionSettingError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExtensionSettingErrorEnum_ExtensionSettingError) Descriptor() protoreflect.EnumDescriptor {
	return file_google_ads_googleads_v1_errors_extension_setting_error_proto_enumTypes[0].Descriptor()
}

func (ExtensionSettingErrorEnum_ExtensionSettingError) Type() protoreflect.EnumType {
	return &file_google_ads_googleads_v1_errors_extension_setting_error_proto_enumTypes[0]
}

func (x ExtensionSettingErrorEnum_ExtensionSettingError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExtensionSettingErrorEnum_ExtensionSettingError.Descriptor instead.
func (ExtensionSettingErrorEnum_ExtensionSettingError) EnumDescriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_extension_setting_error_proto_rawDescGZIP(), []int{0, 0}
}

// Container for enum describing validation errors of extension settings.
type ExtensionSettingErrorEnum struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ExtensionSettingErrorEnum) Reset() {
	*x = ExtensionSettingErrorEnum{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_errors_extension_setting_error_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtensionSettingErrorEnum) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtensionSettingErrorEnum) ProtoMessage() {}

func (x *ExtensionSettingErrorEnum) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_errors_extension_setting_error_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtensionSettingErrorEnum.ProtoReflect.Descriptor instead.
func (*ExtensionSettingErrorEnum) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_extension_setting_error_proto_rawDescGZIP(), []int{0}
}

var File_google_ads_googleads_v1_errors_extension_setting_error_proto protoreflect.FileDescriptor

var file_google_ads_googleads_v1_errors_extension_setting_error_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x98, 0x14, 0x0a,
	0x19, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x22, 0xfa, 0x13, 0x0a, 0x15, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x53,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21, 0x46,
	0x45, 0x45, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48,
	0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x46, 0x45,
	0x45, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x04, 0x12, 0x34, 0x0a, 0x30, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46,
	0x4f, 0x52, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x54, 0x45,
	0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12,
	0x25, 0x0a, 0x21, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45,
	0x5f, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x4f, 0x4e, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x10, 0x06, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x45, 0x57, 0x4c, 0x59, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x10,
	0x07, 0x12, 0x33, 0x0a, 0x2f, 0x4e, 0x4f, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47,
	0x5f, 0x41, 0x44, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x4f, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x10, 0x08, 0x12, 0x33, 0x0a, 0x2f, 0x4e, 0x4f, 0x5f, 0x45, 0x58, 0x49,
	0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x5f, 0x45,
	0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x09, 0x12, 0x33, 0x0a, 0x2f, 0x4e,
	0x4f, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f,
	0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45,
	0x54, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x0a,
	0x12, 0x2d, 0x0a, 0x29, 0x41, 0x44, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x45, 0x58, 0x54,
	0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x41,
	0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x0b, 0x12,
	0x2d, 0x0a, 0x29, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x5f, 0x45, 0x58, 0x54, 0x45,
	0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x4c,
	0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x0c, 0x12, 0x2d,
	0x0a, 0x29, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e,
	0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x4c, 0x52,
	0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x0d, 0x12, 0x35, 0x0a,
	0x31, 0x41, 0x44, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x41,
	0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x48, 0x4f, 0x4c, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x10, 0x0e, 0x12, 0x35, 0x0a, 0x31, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e,
	0x5f, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58,
	0x49, 0x53, 0x54, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x48, 0x4f,
	0x4c, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x0f, 0x12, 0x35, 0x0a, 0x31, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x41, 0x4c, 0x52,
	0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x5f,
	0x50, 0x4c, 0x41, 0x43, 0x45, 0x48, 0x4f, 0x4c, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x10, 0x10, 0x12, 0x16, 0x0a, 0x12, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x4f, 0x55, 0x54, 0x5f,
	0x4f, 0x46, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x11, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x41,
	0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x57,
	0x49, 0x54, 0x48, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x52, 0x4c, 0x53, 0x10, 0x12,
	0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x52, 0x4c, 0x53, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x13, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52,
	0x10, 0x14, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42,
	0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x10, 0x15, 0x12, 0x2d,
	0x0a, 0x29, 0x43, 0x41, 0x52, 0x52, 0x49, 0x45, 0x52, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x43, 0x5f, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x16, 0x12, 0x23, 0x0a,
	0x1f, 0x50, 0x52, 0x45, 0x4d, 0x49, 0x55, 0x4d, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x55,
	0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44,
	0x10, 0x17, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x49, 0x53, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44,
	0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x18, 0x12, 0x28,
	0x0a, 0x24, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x44, 0x4f, 0x4d, 0x45, 0x53, 0x54,
	0x49, 0x43, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f,
	0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x10, 0x19, 0x12, 0x23, 0x0a, 0x1f, 0x56, 0x41, 0x4e, 0x49,
	0x54, 0x59, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x1a, 0x12, 0x18, 0x0a,
	0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59,
	0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x1b, 0x12, 0x23, 0x0a, 0x1f, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x44, 0x10, 0x1c, 0x12, 0x2d, 0x0a, 0x29,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x57, 0x48, 0x49,
	0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4c,
	0x4c, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x1d, 0x12, 0x2a, 0x0a, 0x26, 0x43,
	0x41, 0x4c, 0x4c, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x52, 0x59, 0x10, 0x1e, 0x12, 0x12, 0x0a, 0x0e, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x49, 0x44, 0x10, 0x1f, 0x12, 0x26, 0x0a, 0x22, 0x51,
	0x55, 0x4f, 0x54, 0x45, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4e, 0x49, 0x50, 0x50, 0x45,
	0x54, 0x10, 0x20, 0x12, 0x27, 0x0a, 0x23, 0x48, 0x59, 0x50, 0x48, 0x45, 0x4e, 0x53, 0x5f, 0x49,
	0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x4e, 0x49, 0x50, 0x50, 0x45, 0x54, 0x10, 0x21, 0x12, 0x28, 0x0a, 0x24,
	0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4c, 0x49, 0x47,
	0x49, 0x42, 0x4c, 0x45, 0x10, 0x22, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x23,
	0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x10, 0x24, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x4e, 0x43, 0x4f, 0x4e, 0x53, 0x49, 0x53, 0x54,
	0x45, 0x4e, 0x54, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x53, 0x10, 0x25, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x52, 0x49, 0x43, 0x45, 0x5f, 0x45, 0x58,
	0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x48, 0x41, 0x53, 0x5f, 0x44, 0x55, 0x50, 0x4c,
	0x49, 0x43, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x53, 0x10, 0x26,
	0x12, 0x34, 0x0a, 0x30, 0x50, 0x52, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x48,
	0x41, 0x53, 0x5f, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x48, 0x45,
	0x41, 0x44, 0x45, 0x52, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x27, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x52, 0x49, 0x43, 0x45, 0x5f,
	0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x48, 0x41, 0x53, 0x5f, 0x54, 0x4f,
	0x4f, 0x5f, 0x46, 0x45, 0x57, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x53, 0x10, 0x28, 0x12, 0x26, 0x0a,
	0x22, 0x50, 0x52, 0x49, 0x43, 0x45, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x48, 0x41, 0x53, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x59, 0x5f, 0x49, 0x54,
	0x45, 0x4d, 0x53, 0x10, 0x29, 0x12, 0x15, 0x0a, 0x11, 0x55, 0x4e, 0x53, 0x55, 0x50, 0x50, 0x4f,
	0x52, 0x54, 0x45, 0x44, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x2a, 0x12, 0x1d, 0x0a, 0x19,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50,
	0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x43, 0x45, 0x10, 0x2b, 0x12, 0x18, 0x0a, 0x14, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f,
	0x45, 0x4e, 0x44, 0x10, 0x2d, 0x12, 0x2a, 0x0a, 0x26, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x5f, 0x4d, 0x55, 0x53, 0x54, 0x5f, 0x42, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x5a, 0x4f, 0x4e, 0x45, 0x10,
	0x2f, 0x12, 0x25, 0x0a, 0x21, 0x4f, 0x56, 0x45, 0x52, 0x4c, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47,
	0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41,
	0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x30, 0x12, 0x20, 0x0a, 0x1c, 0x53, 0x43, 0x48, 0x45,
	0x44, 0x55, 0x4c, 0x45, 0x5f, 0x45, 0x4e, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x46, 0x54,
	0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x31, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x4f,
	0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x59, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x53,
	0x5f, 0x50, 0x45, 0x52, 0x5f, 0x44, 0x41, 0x59, 0x10, 0x32, 0x12, 0x26, 0x0a, 0x22, 0x44, 0x55,
	0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f,
	0x4e, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x45, 0x44, 0x49, 0x54,
	0x10, 0x33, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x4e,
	0x49, 0x50, 0x50, 0x45, 0x54, 0x53, 0x5f, 0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x34, 0x12,
	0x3c, 0x0a, 0x38, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x5f, 0x57, 0x49,
	0x54, 0x48, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x10, 0x35, 0x12, 0x1f, 0x0a,
	0x1b, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54,
	0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x36, 0x12, 0x22,
	0x0a, 0x1e, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x45,
	0x5f, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x4d, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x46, 0x45, 0x45, 0x44,
	0x10, 0x37, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x38, 0x12,
	0x2d, 0x0a, 0x29, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x54, 0x49, 0x42, 0x4c, 0x45, 0x5f,
	0x55, 0x4e, 0x44, 0x45, 0x52, 0x4c, 0x59, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48,
	0x49, 0x4e, 0x47, 0x5f, 0x46, 0x55, 0x4e, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x39, 0x12, 0x1d,
	0x0a, 0x19, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x46, 0x54,
	0x45, 0x52, 0x5f, 0x45, 0x4e, 0x44, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x3a, 0x12, 0x18, 0x0a,
	0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x45, 0x5f, 0x46,
	0x4f, 0x52, 0x4d, 0x41, 0x54, 0x10, 0x3b, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f, 0x4d, 0x4f,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x49, 0x4d,
	0x45, 0x10, 0x3c, 0x12, 0x3c, 0x0a, 0x38, 0x50, 0x52, 0x4f, 0x4d, 0x4f, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x50, 0x45, 0x52, 0x43,
	0x45, 0x4e, 0x54, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10,
	0x3d, 0x12, 0x3e, 0x0a, 0x3a, 0x50, 0x52, 0x4f, 0x4d, 0x4f, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43,
	0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x4f, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x53, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10,
	0x3e, 0x12, 0x25, 0x0a, 0x21, 0x54, 0x4f, 0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x59, 0x5f, 0x44, 0x45,
	0x43, 0x49, 0x4d, 0x41, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x53, 0x5f, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x3f, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x10, 0x40, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x4e, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54,
	0x45, 0x44, 0x5f, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45, 0x10, 0x41, 0x12, 0x30, 0x0a,
	0x2c, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e,
	0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x42, 0x12,
	0x26, 0x0a, 0x22, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x54,
	0x54, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x41,
	0x5f, 0x4e, 0x4f, 0x4f, 0x50, 0x10, 0x43, 0x42, 0xf5, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x42, 0x1a,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x44, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x3b, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0xa2, 0x02, 0x03, 0x47, 0x41, 0x41, 0xaa, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x41, 0x64, 0x73, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x2e,
	0x56, 0x31, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0xca, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x5c, 0x41, 0x64, 0x73, 0x5c, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73,
	0x5c, 0x56, 0x31, 0x5c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0xea, 0x02, 0x22, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x41, 0x64, 0x73, 0x3a, 0x3a, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x41, 0x64, 0x73, 0x3a, 0x3a, 0x56, 0x31, 0x3a, 0x3a, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_ads_googleads_v1_errors_extension_setting_error_proto_rawDescOnce sync.Once
	file_google_ads_googleads_v1_errors_extension_setting_error_proto_rawDescData = file_google_ads_googleads_v1_errors_extension_setting_error_proto_rawDesc
)

func file_google_ads_googleads_v1_errors_extension_setting_error_proto_rawDescGZIP() []byte {
	file_google_ads_googleads_v1_errors_extension_setting_error_proto_rawDescOnce.Do(func() {
		file_google_ads_googleads_v1_errors_extension_setting_error_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_ads_googleads_v1_errors_extension_setting_error_proto_rawDescData)
	})
	return file_google_ads_googleads_v1_errors_extension_setting_error_proto_rawDescData
}

var file_google_ads_googleads_v1_errors_extension_setting_error_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_google_ads_googleads_v1_errors_extension_setting_error_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_google_ads_googleads_v1_errors_extension_setting_error_proto_goTypes = []interface{}{
	(ExtensionSettingErrorEnum_ExtensionSettingError)(0), // 0: google.ads.googleads.v1.errors.ExtensionSettingErrorEnum.ExtensionSettingError
	(*ExtensionSettingErrorEnum)(nil),                    // 1: google.ads.googleads.v1.errors.ExtensionSettingErrorEnum
}
var file_google_ads_googleads_v1_errors_extension_setting_error_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_google_ads_googleads_v1_errors_extension_setting_error_proto_init() }
func file_google_ads_googleads_v1_errors_extension_setting_error_proto_init() {
	if File_google_ads_googleads_v1_errors_extension_setting_error_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_ads_googleads_v1_errors_extension_setting_error_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtensionSettingErrorEnum); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_ads_googleads_v1_errors_extension_setting_error_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_ads_googleads_v1_errors_extension_setting_error_proto_goTypes,
		DependencyIndexes: file_google_ads_googleads_v1_errors_extension_setting_error_proto_depIdxs,
		EnumInfos:         file_google_ads_googleads_v1_errors_extension_setting_error_proto_enumTypes,
		MessageInfos:      file_google_ads_googleads_v1_errors_extension_setting_error_proto_msgTypes,
	}.Build()
	File_google_ads_googleads_v1_errors_extension_setting_error_proto = out.File
	file_google_ads_googleads_v1_errors_extension_setting_error_proto_rawDesc = nil
	file_google_ads_googleads_v1_errors_extension_setting_error_proto_goTypes = nil
	file_google_ads_googleads_v1_errors_extension_setting_error_proto_depIdxs = nil
}
