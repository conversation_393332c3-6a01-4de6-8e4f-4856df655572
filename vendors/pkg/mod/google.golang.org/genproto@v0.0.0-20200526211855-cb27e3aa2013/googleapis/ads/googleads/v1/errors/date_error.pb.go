// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.22.0
// 	protoc        v3.11.2
// source: google/ads/googleads/v1/errors/date_error.proto

package errors

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// Enum describing possible date errors.
type DateErrorEnum_DateError int32

const (
	// Enum unspecified.
	DateErrorEnum_UNSPECIFIED DateErrorEnum_DateError = 0
	// The received error code is not known in this version.
	DateErrorEnum_UNKNOWN DateErrorEnum_DateError = 1
	// Given field values do not correspond to a valid date.
	DateErrorEnum_INVALID_FIELD_VALUES_IN_DATE DateErrorEnum_DateError = 2
	// Given field values do not correspond to a valid date time.
	DateErrorEnum_INVALID_FIELD_VALUES_IN_DATE_TIME DateErrorEnum_DateError = 3
	// The string date's format should be yyyy-mm-dd.
	DateErrorEnum_INVALID_STRING_DATE DateErrorEnum_DateError = 4
	// The string date time's format should be yyyy-mm-dd hh:mm:ss.ssssss.
	DateErrorEnum_INVALID_STRING_DATE_TIME_MICROS DateErrorEnum_DateError = 6
	// The string date time's format should be yyyy-mm-dd hh:mm:ss.
	DateErrorEnum_INVALID_STRING_DATE_TIME_SECONDS DateErrorEnum_DateError = 11
	// The string date time's format should be yyyy-mm-dd hh:mm:ss+|-hh:mm.
	DateErrorEnum_INVALID_STRING_DATE_TIME_SECONDS_WITH_OFFSET DateErrorEnum_DateError = 12
	// Date is before allowed minimum.
	DateErrorEnum_EARLIER_THAN_MINIMUM_DATE DateErrorEnum_DateError = 7
	// Date is after allowed maximum.
	DateErrorEnum_LATER_THAN_MAXIMUM_DATE DateErrorEnum_DateError = 8
	// Date range bounds are not in order.
	DateErrorEnum_DATE_RANGE_MINIMUM_DATE_LATER_THAN_MAXIMUM_DATE DateErrorEnum_DateError = 9
	// Both dates in range are null.
	DateErrorEnum_DATE_RANGE_MINIMUM_AND_MAXIMUM_DATES_BOTH_NULL DateErrorEnum_DateError = 10
)

// Enum value maps for DateErrorEnum_DateError.
var (
	DateErrorEnum_DateError_name = map[int32]string{
		0:  "UNSPECIFIED",
		1:  "UNKNOWN",
		2:  "INVALID_FIELD_VALUES_IN_DATE",
		3:  "INVALID_FIELD_VALUES_IN_DATE_TIME",
		4:  "INVALID_STRING_DATE",
		6:  "INVALID_STRING_DATE_TIME_MICROS",
		11: "INVALID_STRING_DATE_TIME_SECONDS",
		12: "INVALID_STRING_DATE_TIME_SECONDS_WITH_OFFSET",
		7:  "EARLIER_THAN_MINIMUM_DATE",
		8:  "LATER_THAN_MAXIMUM_DATE",
		9:  "DATE_RANGE_MINIMUM_DATE_LATER_THAN_MAXIMUM_DATE",
		10: "DATE_RANGE_MINIMUM_AND_MAXIMUM_DATES_BOTH_NULL",
	}
	DateErrorEnum_DateError_value = map[string]int32{
		"UNSPECIFIED":                                     0,
		"UNKNOWN":                                         1,
		"INVALID_FIELD_VALUES_IN_DATE":                    2,
		"INVALID_FIELD_VALUES_IN_DATE_TIME":               3,
		"INVALID_STRING_DATE":                             4,
		"INVALID_STRING_DATE_TIME_MICROS":                 6,
		"INVALID_STRING_DATE_TIME_SECONDS":                11,
		"INVALID_STRING_DATE_TIME_SECONDS_WITH_OFFSET":    12,
		"EARLIER_THAN_MINIMUM_DATE":                       7,
		"LATER_THAN_MAXIMUM_DATE":                         8,
		"DATE_RANGE_MINIMUM_DATE_LATER_THAN_MAXIMUM_DATE": 9,
		"DATE_RANGE_MINIMUM_AND_MAXIMUM_DATES_BOTH_NULL":  10,
	}
)

func (x DateErrorEnum_DateError) Enum() *DateErrorEnum_DateError {
	p := new(DateErrorEnum_DateError)
	*p = x
	return p
}

func (x DateErrorEnum_DateError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DateErrorEnum_DateError) Descriptor() protoreflect.EnumDescriptor {
	return file_google_ads_googleads_v1_errors_date_error_proto_enumTypes[0].Descriptor()
}

func (DateErrorEnum_DateError) Type() protoreflect.EnumType {
	return &file_google_ads_googleads_v1_errors_date_error_proto_enumTypes[0]
}

func (x DateErrorEnum_DateError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DateErrorEnum_DateError.Descriptor instead.
func (DateErrorEnum_DateError) EnumDescriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_date_error_proto_rawDescGZIP(), []int{0, 0}
}

// Container for enum describing possible date errors.
type DateErrorEnum struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DateErrorEnum) Reset() {
	*x = DateErrorEnum{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_errors_date_error_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DateErrorEnum) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateErrorEnum) ProtoMessage() {}

func (x *DateErrorEnum) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_errors_date_error_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateErrorEnum.ProtoReflect.Descriptor instead.
func (*DateErrorEnum) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_date_error_proto_rawDescGZIP(), []int{0}
}

var File_google_ads_googleads_v1_errors_date_error_proto protoreflect.FileDescriptor

var file_google_ads_googleads_v1_errors_date_error_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xbf, 0x03, 0x0a, 0x0d, 0x44, 0x61, 0x74, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75,
	0x6d, 0x22, 0xad, 0x03, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12,
	0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x01, 0x12, 0x20, 0x0a,
	0x1c, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x56,
	0x41, 0x4c, 0x55, 0x45, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x02, 0x12,
	0x25, 0x0a, 0x21, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f,
	0x54, 0x49, 0x4d, 0x45, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x04, 0x12,
	0x23, 0x0a, 0x1f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x4e,
	0x47, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x4d, 0x49, 0x43, 0x52,
	0x4f, 0x53, 0x10, 0x06, 0x12, 0x24, 0x0a, 0x20, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45,
	0x5f, 0x53, 0x45, 0x43, 0x4f, 0x4e, 0x44, 0x53, 0x10, 0x0b, 0x12, 0x30, 0x0a, 0x2c, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x53, 0x45, 0x43, 0x4f, 0x4e, 0x44, 0x53, 0x5f, 0x57,
	0x49, 0x54, 0x48, 0x5f, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x10, 0x0c, 0x12, 0x1d, 0x0a, 0x19,
	0x45, 0x41, 0x52, 0x4c, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x5f, 0x4d, 0x49, 0x4e,
	0x49, 0x4d, 0x55, 0x4d, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x07, 0x12, 0x1b, 0x0a, 0x17, 0x4c,
	0x41, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x5f, 0x4d, 0x41, 0x58, 0x49, 0x4d, 0x55,
	0x4d, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x08, 0x12, 0x33, 0x0a, 0x2f, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x4d, 0x49, 0x4e, 0x49, 0x4d, 0x55, 0x4d, 0x5f, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x4c, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x5f, 0x4d,
	0x41, 0x58, 0x49, 0x4d, 0x55, 0x4d, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x09, 0x12, 0x32, 0x0a,
	0x2e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x4d, 0x49, 0x4e, 0x49,
	0x4d, 0x55, 0x4d, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4d, 0x41, 0x58, 0x49, 0x4d, 0x55, 0x4d, 0x5f,
	0x44, 0x41, 0x54, 0x45, 0x53, 0x5f, 0x42, 0x4f, 0x54, 0x48, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10,
	0x0a, 0x42, 0xe9, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x42, 0x0e, 0x44, 0x61, 0x74, 0x65, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x44, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65,
	0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x3b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0xa2, 0x02, 0x03, 0x47, 0x41, 0x41, 0xaa, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x41, 0x64, 0x73, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x2e, 0x56, 0x31,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0xca, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x5c, 0x41, 0x64, 0x73, 0x5c, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x5c, 0x56,
	0x31, 0x5c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0xea, 0x02, 0x22, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x3a, 0x3a, 0x41, 0x64, 0x73, 0x3a, 0x3a, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64,
	0x73, 0x3a, 0x3a, 0x56, 0x31, 0x3a, 0x3a, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_ads_googleads_v1_errors_date_error_proto_rawDescOnce sync.Once
	file_google_ads_googleads_v1_errors_date_error_proto_rawDescData = file_google_ads_googleads_v1_errors_date_error_proto_rawDesc
)

func file_google_ads_googleads_v1_errors_date_error_proto_rawDescGZIP() []byte {
	file_google_ads_googleads_v1_errors_date_error_proto_rawDescOnce.Do(func() {
		file_google_ads_googleads_v1_errors_date_error_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_ads_googleads_v1_errors_date_error_proto_rawDescData)
	})
	return file_google_ads_googleads_v1_errors_date_error_proto_rawDescData
}

var file_google_ads_googleads_v1_errors_date_error_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_google_ads_googleads_v1_errors_date_error_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_google_ads_googleads_v1_errors_date_error_proto_goTypes = []interface{}{
	(DateErrorEnum_DateError)(0), // 0: google.ads.googleads.v1.errors.DateErrorEnum.DateError
	(*DateErrorEnum)(nil),        // 1: google.ads.googleads.v1.errors.DateErrorEnum
}
var file_google_ads_googleads_v1_errors_date_error_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_google_ads_googleads_v1_errors_date_error_proto_init() }
func file_google_ads_googleads_v1_errors_date_error_proto_init() {
	if File_google_ads_googleads_v1_errors_date_error_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_ads_googleads_v1_errors_date_error_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DateErrorEnum); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_ads_googleads_v1_errors_date_error_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_ads_googleads_v1_errors_date_error_proto_goTypes,
		DependencyIndexes: file_google_ads_googleads_v1_errors_date_error_proto_depIdxs,
		EnumInfos:         file_google_ads_googleads_v1_errors_date_error_proto_enumTypes,
		MessageInfos:      file_google_ads_googleads_v1_errors_date_error_proto_msgTypes,
	}.Build()
	File_google_ads_googleads_v1_errors_date_error_proto = out.File
	file_google_ads_googleads_v1_errors_date_error_proto_rawDesc = nil
	file_google_ads_googleads_v1_errors_date_error_proto_goTypes = nil
	file_google_ads_googleads_v1_errors_date_error_proto_depIdxs = nil
}
