// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.22.0
// 	protoc        v3.11.2
// source: google/ads/googleads/v1/common/bidding.proto

package common

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	enums "google.golang.org/genproto/googleapis/ads/googleads/v1/enums"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// Commission is an automatic bidding strategy in which the advertiser pays a
// certain portion of the conversion value.
type Commission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Commission rate defines the portion of the conversion value that the
	// advertiser will be billed. A commission rate of x should be passed into
	// this field as (x * 1,000,000). For example, 106,000 represents a commission
	// rate of 0.106 (10.6%).
	CommissionRateMicros *wrappers.Int64Value `protobuf:"bytes,1,opt,name=commission_rate_micros,json=commissionRateMicros,proto3" json:"commission_rate_micros,omitempty"`
}

func (x *Commission) Reset() {
	*x = Commission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Commission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Commission) ProtoMessage() {}

func (x *Commission) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Commission.ProtoReflect.Descriptor instead.
func (*Commission) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{0}
}

func (x *Commission) GetCommissionRateMicros() *wrappers.Int64Value {
	if x != nil {
		return x.CommissionRateMicros
	}
	return nil
}

// An automated bidding strategy that raises bids for clicks
// that seem more likely to lead to a conversion and lowers
// them for clicks where they seem less likely.
type EnhancedCpc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EnhancedCpc) Reset() {
	*x = EnhancedCpc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnhancedCpc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnhancedCpc) ProtoMessage() {}

func (x *EnhancedCpc) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnhancedCpc.ProtoReflect.Descriptor instead.
func (*EnhancedCpc) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{1}
}

// Manual click-based bidding where user pays per click.
type ManualCpc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether bids are to be enhanced based on conversion optimizer data.
	EnhancedCpcEnabled *wrappers.BoolValue `protobuf:"bytes,1,opt,name=enhanced_cpc_enabled,json=enhancedCpcEnabled,proto3" json:"enhanced_cpc_enabled,omitempty"`
}

func (x *ManualCpc) Reset() {
	*x = ManualCpc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualCpc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualCpc) ProtoMessage() {}

func (x *ManualCpc) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualCpc.ProtoReflect.Descriptor instead.
func (*ManualCpc) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{2}
}

func (x *ManualCpc) GetEnhancedCpcEnabled() *wrappers.BoolValue {
	if x != nil {
		return x.EnhancedCpcEnabled
	}
	return nil
}

// Manual impression-based bidding where user pays per thousand impressions.
type ManualCpm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ManualCpm) Reset() {
	*x = ManualCpm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualCpm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualCpm) ProtoMessage() {}

func (x *ManualCpm) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualCpm.ProtoReflect.Descriptor instead.
func (*ManualCpm) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{3}
}

// View based bidding where user pays per video view.
type ManualCpv struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ManualCpv) Reset() {
	*x = ManualCpv{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualCpv) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualCpv) ProtoMessage() {}

func (x *ManualCpv) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualCpv.ProtoReflect.Descriptor instead.
func (*ManualCpv) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{4}
}

// An automated bidding strategy that sets bids to help get the most conversions
// for your campaign while spending your budget.
type MaximizeConversions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MaximizeConversions) Reset() {
	*x = MaximizeConversions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaximizeConversions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaximizeConversions) ProtoMessage() {}

func (x *MaximizeConversions) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaximizeConversions.ProtoReflect.Descriptor instead.
func (*MaximizeConversions) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{5}
}

// An automated bidding strategy which tries to maximize conversion value
// given a daily budget.
type MaximizeConversionValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The target return on ad spend (ROAS) option. If set, the bid strategy will
	// maximize revenue while averaging the target return on ad spend. If the
	// target ROAS is high, the bid strategy may not be able to spend the full
	// budget. If the target ROAS is not set, the bid strategy will aim to
	// achieve the highest possible ROAS for the budget.
	TargetRoas *wrappers.DoubleValue `protobuf:"bytes,1,opt,name=target_roas,json=targetRoas,proto3" json:"target_roas,omitempty"`
}

func (x *MaximizeConversionValue) Reset() {
	*x = MaximizeConversionValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaximizeConversionValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaximizeConversionValue) ProtoMessage() {}

func (x *MaximizeConversionValue) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaximizeConversionValue.ProtoReflect.Descriptor instead.
func (*MaximizeConversionValue) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{6}
}

func (x *MaximizeConversionValue) GetTargetRoas() *wrappers.DoubleValue {
	if x != nil {
		return x.TargetRoas
	}
	return nil
}

// An automated bidding strategy which sets CPC bids to target impressions on
// page one, or page one promoted slots on google.com.
// This strategy is deprecated.
type PageOnePromoted struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The strategy goal of where impressions are desired to be shown on
	// search result pages.
	StrategyGoal enums.PageOnePromotedStrategyGoalEnum_PageOnePromotedStrategyGoal `protobuf:"varint,1,opt,name=strategy_goal,json=strategyGoal,proto3,enum=google.ads.googleads.v1.enums.PageOnePromotedStrategyGoalEnum_PageOnePromotedStrategyGoal" json:"strategy_goal,omitempty"`
	// Maximum bid limit that can be set by the bid strategy.
	// The limit applies to all keywords managed by the strategy.
	CpcBidCeilingMicros *wrappers.Int64Value `protobuf:"bytes,2,opt,name=cpc_bid_ceiling_micros,json=cpcBidCeilingMicros,proto3" json:"cpc_bid_ceiling_micros,omitempty"`
	// Bid multiplier to be applied to the relevant bid estimate (depending on
	// the `strategy_goal`) in determining a keyword's new CPC bid.
	BidModifier *wrappers.DoubleValue `protobuf:"bytes,3,opt,name=bid_modifier,json=bidModifier,proto3" json:"bid_modifier,omitempty"`
	// Whether the strategy should always follow bid estimate changes, or only
	// increase.
	// If false, always sets a keyword's new bid to the current bid estimate.
	// If true, only updates a keyword's bid if the current bid estimate is
	// greater than the current bid.
	OnlyRaiseCpcBids *wrappers.BoolValue `protobuf:"bytes,4,opt,name=only_raise_cpc_bids,json=onlyRaiseCpcBids,proto3" json:"only_raise_cpc_bids,omitempty"`
	// Whether the strategy is allowed to raise bids when the throttling
	// rate of the budget it is serving out of rises above a threshold.
	RaiseCpcBidWhenBudgetConstrained *wrappers.BoolValue `protobuf:"bytes,5,opt,name=raise_cpc_bid_when_budget_constrained,json=raiseCpcBidWhenBudgetConstrained,proto3" json:"raise_cpc_bid_when_budget_constrained,omitempty"`
	// Whether the strategy is allowed to raise bids on keywords with
	// lower-range quality scores.
	RaiseCpcBidWhenQualityScoreIsLow *wrappers.BoolValue `protobuf:"bytes,6,opt,name=raise_cpc_bid_when_quality_score_is_low,json=raiseCpcBidWhenQualityScoreIsLow,proto3" json:"raise_cpc_bid_when_quality_score_is_low,omitempty"`
}

func (x *PageOnePromoted) Reset() {
	*x = PageOnePromoted{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageOnePromoted) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageOnePromoted) ProtoMessage() {}

func (x *PageOnePromoted) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageOnePromoted.ProtoReflect.Descriptor instead.
func (*PageOnePromoted) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{7}
}

func (x *PageOnePromoted) GetStrategyGoal() enums.PageOnePromotedStrategyGoalEnum_PageOnePromotedStrategyGoal {
	if x != nil {
		return x.StrategyGoal
	}
	return enums.PageOnePromotedStrategyGoalEnum_UNSPECIFIED
}

func (x *PageOnePromoted) GetCpcBidCeilingMicros() *wrappers.Int64Value {
	if x != nil {
		return x.CpcBidCeilingMicros
	}
	return nil
}

func (x *PageOnePromoted) GetBidModifier() *wrappers.DoubleValue {
	if x != nil {
		return x.BidModifier
	}
	return nil
}

func (x *PageOnePromoted) GetOnlyRaiseCpcBids() *wrappers.BoolValue {
	if x != nil {
		return x.OnlyRaiseCpcBids
	}
	return nil
}

func (x *PageOnePromoted) GetRaiseCpcBidWhenBudgetConstrained() *wrappers.BoolValue {
	if x != nil {
		return x.RaiseCpcBidWhenBudgetConstrained
	}
	return nil
}

func (x *PageOnePromoted) GetRaiseCpcBidWhenQualityScoreIsLow() *wrappers.BoolValue {
	if x != nil {
		return x.RaiseCpcBidWhenQualityScoreIsLow
	}
	return nil
}

// An automated bid strategy that sets bids to help get as many conversions as
// possible at the target cost-per-acquisition (CPA) you set.
type TargetCpa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Average CPA target.
	// This target should be greater than or equal to minimum billable unit based
	// on the currency for the account.
	TargetCpaMicros *wrappers.Int64Value `protobuf:"bytes,1,opt,name=target_cpa_micros,json=targetCpaMicros,proto3" json:"target_cpa_micros,omitempty"`
	// Maximum bid limit that can be set by the bid strategy.
	// The limit applies to all keywords managed by the strategy.
	CpcBidCeilingMicros *wrappers.Int64Value `protobuf:"bytes,2,opt,name=cpc_bid_ceiling_micros,json=cpcBidCeilingMicros,proto3" json:"cpc_bid_ceiling_micros,omitempty"`
	// Minimum bid limit that can be set by the bid strategy.
	// The limit applies to all keywords managed by the strategy.
	CpcBidFloorMicros *wrappers.Int64Value `protobuf:"bytes,3,opt,name=cpc_bid_floor_micros,json=cpcBidFloorMicros,proto3" json:"cpc_bid_floor_micros,omitempty"`
}

func (x *TargetCpa) Reset() {
	*x = TargetCpa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetCpa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetCpa) ProtoMessage() {}

func (x *TargetCpa) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetCpa.ProtoReflect.Descriptor instead.
func (*TargetCpa) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{8}
}

func (x *TargetCpa) GetTargetCpaMicros() *wrappers.Int64Value {
	if x != nil {
		return x.TargetCpaMicros
	}
	return nil
}

func (x *TargetCpa) GetCpcBidCeilingMicros() *wrappers.Int64Value {
	if x != nil {
		return x.CpcBidCeilingMicros
	}
	return nil
}

func (x *TargetCpa) GetCpcBidFloorMicros() *wrappers.Int64Value {
	if x != nil {
		return x.CpcBidFloorMicros
	}
	return nil
}

// Target CPM (cost per thousand impressions) is an automated bidding strategy
// that sets bids to optimize performance given the target CPM you set.
type TargetCpm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TargetCpm) Reset() {
	*x = TargetCpm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetCpm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetCpm) ProtoMessage() {}

func (x *TargetCpm) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetCpm.ProtoReflect.Descriptor instead.
func (*TargetCpm) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{9}
}

// An automated bidding strategy that sets bids so that a certain percentage of
// search ads are shown at the top of the first page (or other targeted
// location).
// next tag = 4
type TargetImpressionShare struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The targeted location on the search results page.
	Location enums.TargetImpressionShareLocationEnum_TargetImpressionShareLocation `protobuf:"varint,1,opt,name=location,proto3,enum=google.ads.googleads.v1.enums.TargetImpressionShareLocationEnum_TargetImpressionShareLocation" json:"location,omitempty"`
	// The desired fraction of ads to be shown in the targeted location in micros.
	// E.g. 1% equals 10,000.
	LocationFractionMicros *wrappers.Int64Value `protobuf:"bytes,2,opt,name=location_fraction_micros,json=locationFractionMicros,proto3" json:"location_fraction_micros,omitempty"`
	// The highest CPC bid the automated bidding system is permitted to specify.
	// This is a required field entered by the advertiser that sets the ceiling
	// and specified in local micros.
	CpcBidCeilingMicros *wrappers.Int64Value `protobuf:"bytes,3,opt,name=cpc_bid_ceiling_micros,json=cpcBidCeilingMicros,proto3" json:"cpc_bid_ceiling_micros,omitempty"`
}

func (x *TargetImpressionShare) Reset() {
	*x = TargetImpressionShare{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetImpressionShare) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetImpressionShare) ProtoMessage() {}

func (x *TargetImpressionShare) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetImpressionShare.ProtoReflect.Descriptor instead.
func (*TargetImpressionShare) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{10}
}

func (x *TargetImpressionShare) GetLocation() enums.TargetImpressionShareLocationEnum_TargetImpressionShareLocation {
	if x != nil {
		return x.Location
	}
	return enums.TargetImpressionShareLocationEnum_UNSPECIFIED
}

func (x *TargetImpressionShare) GetLocationFractionMicros() *wrappers.Int64Value {
	if x != nil {
		return x.LocationFractionMicros
	}
	return nil
}

func (x *TargetImpressionShare) GetCpcBidCeilingMicros() *wrappers.Int64Value {
	if x != nil {
		return x.CpcBidCeilingMicros
	}
	return nil
}

// An automated bidding strategy that sets bids based on the target fraction of
// auctions where the advertiser should outrank a specific competitor.
// This strategy is deprecated.
type TargetOutrankShare struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The target fraction of auctions where the advertiser should outrank the
	// competitor.
	// The advertiser outranks the competitor in an auction if either the
	// advertiser appears above the competitor in the search results, or appears
	// in the search results when the competitor does not.
	// Value must be between 1 and 1000000, inclusive.
	TargetOutrankShareMicros *wrappers.Int32Value `protobuf:"bytes,1,opt,name=target_outrank_share_micros,json=targetOutrankShareMicros,proto3" json:"target_outrank_share_micros,omitempty"`
	// Competitor's visible domain URL.
	CompetitorDomain *wrappers.StringValue `protobuf:"bytes,2,opt,name=competitor_domain,json=competitorDomain,proto3" json:"competitor_domain,omitempty"`
	// Maximum bid limit that can be set by the bid strategy.
	// The limit applies to all keywords managed by the strategy.
	CpcBidCeilingMicros *wrappers.Int64Value `protobuf:"bytes,3,opt,name=cpc_bid_ceiling_micros,json=cpcBidCeilingMicros,proto3" json:"cpc_bid_ceiling_micros,omitempty"`
	// Whether the strategy should always follow bid estimate changes,
	// or only increase.
	// If false, always set a keyword's new bid to the current bid estimate.
	// If true, only updates a keyword's bid if the current bid estimate is
	// greater than the current bid.
	OnlyRaiseCpcBids *wrappers.BoolValue `protobuf:"bytes,4,opt,name=only_raise_cpc_bids,json=onlyRaiseCpcBids,proto3" json:"only_raise_cpc_bids,omitempty"`
	// Whether the strategy is allowed to raise bids on keywords with
	// lower-range quality scores.
	RaiseCpcBidWhenQualityScoreIsLow *wrappers.BoolValue `protobuf:"bytes,5,opt,name=raise_cpc_bid_when_quality_score_is_low,json=raiseCpcBidWhenQualityScoreIsLow,proto3" json:"raise_cpc_bid_when_quality_score_is_low,omitempty"`
}

func (x *TargetOutrankShare) Reset() {
	*x = TargetOutrankShare{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetOutrankShare) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetOutrankShare) ProtoMessage() {}

func (x *TargetOutrankShare) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetOutrankShare.ProtoReflect.Descriptor instead.
func (*TargetOutrankShare) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{11}
}

func (x *TargetOutrankShare) GetTargetOutrankShareMicros() *wrappers.Int32Value {
	if x != nil {
		return x.TargetOutrankShareMicros
	}
	return nil
}

func (x *TargetOutrankShare) GetCompetitorDomain() *wrappers.StringValue {
	if x != nil {
		return x.CompetitorDomain
	}
	return nil
}

func (x *TargetOutrankShare) GetCpcBidCeilingMicros() *wrappers.Int64Value {
	if x != nil {
		return x.CpcBidCeilingMicros
	}
	return nil
}

func (x *TargetOutrankShare) GetOnlyRaiseCpcBids() *wrappers.BoolValue {
	if x != nil {
		return x.OnlyRaiseCpcBids
	}
	return nil
}

func (x *TargetOutrankShare) GetRaiseCpcBidWhenQualityScoreIsLow() *wrappers.BoolValue {
	if x != nil {
		return x.RaiseCpcBidWhenQualityScoreIsLow
	}
	return nil
}

// An automated bidding strategy that helps you maximize revenue while
// averaging a specific target return on ad spend (ROAS).
type TargetRoas struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The desired revenue (based on conversion data) per unit of spend.
	// Value must be between 0.01 and 1000.0, inclusive.
	TargetRoas *wrappers.DoubleValue `protobuf:"bytes,1,opt,name=target_roas,json=targetRoas,proto3" json:"target_roas,omitempty"`
	// Maximum bid limit that can be set by the bid strategy.
	// The limit applies to all keywords managed by the strategy.
	CpcBidCeilingMicros *wrappers.Int64Value `protobuf:"bytes,2,opt,name=cpc_bid_ceiling_micros,json=cpcBidCeilingMicros,proto3" json:"cpc_bid_ceiling_micros,omitempty"`
	// Minimum bid limit that can be set by the bid strategy.
	// The limit applies to all keywords managed by the strategy.
	CpcBidFloorMicros *wrappers.Int64Value `protobuf:"bytes,3,opt,name=cpc_bid_floor_micros,json=cpcBidFloorMicros,proto3" json:"cpc_bid_floor_micros,omitempty"`
}

func (x *TargetRoas) Reset() {
	*x = TargetRoas{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetRoas) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetRoas) ProtoMessage() {}

func (x *TargetRoas) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetRoas.ProtoReflect.Descriptor instead.
func (*TargetRoas) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{12}
}

func (x *TargetRoas) GetTargetRoas() *wrappers.DoubleValue {
	if x != nil {
		return x.TargetRoas
	}
	return nil
}

func (x *TargetRoas) GetCpcBidCeilingMicros() *wrappers.Int64Value {
	if x != nil {
		return x.CpcBidCeilingMicros
	}
	return nil
}

func (x *TargetRoas) GetCpcBidFloorMicros() *wrappers.Int64Value {
	if x != nil {
		return x.CpcBidFloorMicros
	}
	return nil
}

// An automated bid strategy that sets your bids to help get as many clicks
// as possible within your budget.
type TargetSpend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The spend target under which to maximize clicks.
	// A TargetSpend bidder will attempt to spend the smaller of this value
	// or the natural throttling spend amount.
	// If not specified, the budget is used as the spend target.
	TargetSpendMicros *wrappers.Int64Value `protobuf:"bytes,1,opt,name=target_spend_micros,json=targetSpendMicros,proto3" json:"target_spend_micros,omitempty"`
	// Maximum bid limit that can be set by the bid strategy.
	// The limit applies to all keywords managed by the strategy.
	CpcBidCeilingMicros *wrappers.Int64Value `protobuf:"bytes,2,opt,name=cpc_bid_ceiling_micros,json=cpcBidCeilingMicros,proto3" json:"cpc_bid_ceiling_micros,omitempty"`
}

func (x *TargetSpend) Reset() {
	*x = TargetSpend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetSpend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetSpend) ProtoMessage() {}

func (x *TargetSpend) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetSpend.ProtoReflect.Descriptor instead.
func (*TargetSpend) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{13}
}

func (x *TargetSpend) GetTargetSpendMicros() *wrappers.Int64Value {
	if x != nil {
		return x.TargetSpendMicros
	}
	return nil
}

func (x *TargetSpend) GetCpcBidCeilingMicros() *wrappers.Int64Value {
	if x != nil {
		return x.CpcBidCeilingMicros
	}
	return nil
}

// A bidding strategy where bids are a fraction of the advertised price for
// some good or service.
type PercentCpc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Maximum bid limit that can be set by the bid strategy. This is
	// an optional field entered by the advertiser and specified in local micros.
	// Note: A zero value is interpreted in the same way as having bid_ceiling
	// undefined.
	CpcBidCeilingMicros *wrappers.Int64Value `protobuf:"bytes,1,opt,name=cpc_bid_ceiling_micros,json=cpcBidCeilingMicros,proto3" json:"cpc_bid_ceiling_micros,omitempty"`
	// Adjusts the bid for each auction upward or downward, depending on the
	// likelihood of a conversion. Individual bids may exceed
	// cpc_bid_ceiling_micros, but the average bid amount for a campaign should
	// not.
	EnhancedCpcEnabled *wrappers.BoolValue `protobuf:"bytes,2,opt,name=enhanced_cpc_enabled,json=enhancedCpcEnabled,proto3" json:"enhanced_cpc_enabled,omitempty"`
}

func (x *PercentCpc) Reset() {
	*x = PercentCpc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PercentCpc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PercentCpc) ProtoMessage() {}

func (x *PercentCpc) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_bidding_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PercentCpc.ProtoReflect.Descriptor instead.
func (*PercentCpc) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP(), []int{14}
}

func (x *PercentCpc) GetCpcBidCeilingMicros() *wrappers.Int64Value {
	if x != nil {
		return x.CpcBidCeilingMicros
	}
	return nil
}

func (x *PercentCpc) GetEnhancedCpcEnabled() *wrappers.BoolValue {
	if x != nil {
		return x.EnhancedCpcEnabled
	}
	return nil
}

var File_google_ads_googleads_v1_common_bidding_proto protoreflect.FileDescriptor

var file_google_ads_googleads_v1_common_bidding_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x43,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x65, 0x64, 0x5f,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x67, 0x6f, 0x61, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x44, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5f, 0x0a, 0x0a, 0x43, 0x6f, 0x6d, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x51, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x14, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61,
	0x74, 0x65, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x22, 0x0d, 0x0a, 0x0b, 0x45, 0x6e, 0x68, 0x61,
	0x6e, 0x63, 0x65, 0x64, 0x43, 0x70, 0x63, 0x22, 0x59, 0x0a, 0x09, 0x4d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x43, 0x70, 0x63, 0x12, 0x4c, 0x0a, 0x14, 0x65, 0x6e, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x64,
	0x5f, 0x63, 0x70, 0x63, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x12,
	0x65, 0x6e, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x43, 0x70, 0x63, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x22, 0x0b, 0x0a, 0x09, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x70, 0x6d, 0x22,
	0x0b, 0x0a, 0x09, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x70, 0x76, 0x22, 0x15, 0x0a, 0x13,
	0x4d, 0x61, 0x78, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0x58, 0x0a, 0x17, 0x4d, 0x61, 0x78, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3d,
	0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x72, 0x6f, 0x61, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x6f, 0x61, 0x73, 0x22, 0xcc, 0x04,
	0x0a, 0x0f, 0x50, 0x61, 0x67, 0x65, 0x4f, 0x6e, 0x65, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x65,
	0x64, 0x12, 0x7f, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x67, 0x6f,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x5a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x4f, 0x6e, 0x65,
	0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x65, 0x64, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x47, 0x6f, 0x61, 0x6c, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x4f, 0x6e, 0x65,
	0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x65, 0x64, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x47, 0x6f, 0x61, 0x6c, 0x52, 0x0c, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x47, 0x6f,
	0x61, 0x6c, 0x12, 0x50, 0x0a, 0x16, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x63, 0x65,
	0x69, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x13, 0x63, 0x70, 0x63, 0x42, 0x69, 0x64, 0x43, 0x65, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x69,
	0x63, 0x72, 0x6f, 0x73, 0x12, 0x3f, 0x0a, 0x0c, 0x62, 0x69, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x62, 0x69, 0x64, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x13, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x72, 0x61,
	0x69, 0x73, 0x65, 0x5f, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10,
	0x6f, 0x6e, 0x6c, 0x79, 0x52, 0x61, 0x69, 0x73, 0x65, 0x43, 0x70, 0x63, 0x42, 0x69, 0x64, 0x73,
	0x12, 0x6b, 0x0a, 0x25, 0x72, 0x61, 0x69, 0x73, 0x65, 0x5f, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69,
	0x64, 0x5f, 0x77, 0x68, 0x65, 0x6e, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x20, 0x72, 0x61, 0x69,
	0x73, 0x65, 0x43, 0x70, 0x63, 0x42, 0x69, 0x64, 0x57, 0x68, 0x65, 0x6e, 0x42, 0x75, 0x64, 0x67,
	0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x12, 0x6d, 0x0a,
	0x27, 0x72, 0x61, 0x69, 0x73, 0x65, 0x5f, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x77,
	0x68, 0x65, 0x6e, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x5f, 0x69, 0x73, 0x5f, 0x6c, 0x6f, 0x77, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x20, 0x72, 0x61, 0x69, 0x73,
	0x65, 0x43, 0x70, 0x63, 0x42, 0x69, 0x64, 0x57, 0x68, 0x65, 0x6e, 0x51, 0x75, 0x61, 0x6c, 0x69,
	0x74, 0x79, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x49, 0x73, 0x4c, 0x6f, 0x77, 0x22, 0xf4, 0x01, 0x0a,
	0x09, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x70, 0x61, 0x12, 0x47, 0x0a, 0x11, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x70, 0x61, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x70, 0x61, 0x4d, 0x69, 0x63,
	0x72, 0x6f, 0x73, 0x12, 0x50, 0x0a, 0x16, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x63,
	0x65, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x13, 0x63, 0x70, 0x63, 0x42, 0x69, 0x64, 0x43, 0x65, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x4d,
	0x69, 0x63, 0x72, 0x6f, 0x73, 0x12, 0x4c, 0x0a, 0x14, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69, 0x64,
	0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x11, 0x63, 0x70, 0x63, 0x42, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x4d, 0x69, 0x63,
	0x72, 0x6f, 0x73, 0x22, 0x0b, 0x0a, 0x09, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x70, 0x6d,
	0x22, 0xbc, 0x02, 0x0a, 0x15, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x7a, 0x0a, 0x08, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x5e, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53,
	0x68, 0x61, 0x72, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x55, 0x0a, 0x18, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x66, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x63, 0x72,
	0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x16, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x12, 0x50, 0x0a,
	0x16, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x63, 0x65, 0x69, 0x6c, 0x69, 0x6e, 0x67,
	0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x13, 0x63, 0x70, 0x63, 0x42,
	0x69, 0x64, 0x43, 0x65, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x22,
	0xc7, 0x03, 0x0a, 0x12, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x61, 0x6e,
	0x6b, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x5a, 0x0a, 0x1b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x6f, 0x75, 0x74, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x18, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x4f, 0x75, 0x74, 0x72, 0x61, 0x6e, 0x6b, 0x53, 0x68, 0x61, 0x72, 0x65, 0x4d, 0x69, 0x63, 0x72,
	0x6f, 0x73, 0x12, 0x49, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69, 0x74, 0x6f, 0x72,
	0x5f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x63, 0x6f, 0x6d,
	0x70, 0x65, 0x74, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x50, 0x0a,
	0x16, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x63, 0x65, 0x69, 0x6c, 0x69, 0x6e, 0x67,
	0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x13, 0x63, 0x70, 0x63, 0x42,
	0x69, 0x64, 0x43, 0x65, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x12,
	0x49, 0x0a, 0x13, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x72, 0x61, 0x69, 0x73, 0x65, 0x5f, 0x63, 0x70,
	0x63, 0x5f, 0x62, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42,
	0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x6f, 0x6e, 0x6c, 0x79, 0x52, 0x61,
	0x69, 0x73, 0x65, 0x43, 0x70, 0x63, 0x42, 0x69, 0x64, 0x73, 0x12, 0x6d, 0x0a, 0x27, 0x72, 0x61,
	0x69, 0x73, 0x65, 0x5f, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x77, 0x68, 0x65, 0x6e,
	0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x69,
	0x73, 0x5f, 0x6c, 0x6f, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x20, 0x72, 0x61, 0x69, 0x73, 0x65, 0x43, 0x70,
	0x63, 0x42, 0x69, 0x64, 0x57, 0x68, 0x65, 0x6e, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x49, 0x73, 0x4c, 0x6f, 0x77, 0x22, 0xeb, 0x01, 0x0a, 0x0a, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x52, 0x6f, 0x61, 0x73, 0x12, 0x3d, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x72, 0x6f, 0x61, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x52, 0x6f, 0x61, 0x73, 0x12, 0x50, 0x0a, 0x16, 0x63, 0x70, 0x63, 0x5f, 0x62,
	0x69, 0x64, 0x5f, 0x63, 0x65, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x13, 0x63, 0x70, 0x63, 0x42, 0x69, 0x64, 0x43, 0x65, 0x69, 0x6c,
	0x69, 0x6e, 0x67, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x12, 0x4c, 0x0a, 0x14, 0x63, 0x70, 0x63,
	0x5f, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x11, 0x63, 0x70, 0x63, 0x42, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f,
	0x72, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x22, 0xac, 0x01, 0x0a, 0x0b, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x53, 0x70, 0x65, 0x6e, 0x64, 0x12, 0x4b, 0x0a, 0x13, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x70, 0x65, 0x6e, 0x64, 0x4d, 0x69,
	0x63, 0x72, 0x6f, 0x73, 0x12, 0x50, 0x0a, 0x16, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69, 0x64, 0x5f,
	0x63, 0x65, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x13, 0x63, 0x70, 0x63, 0x42, 0x69, 0x64, 0x43, 0x65, 0x69, 0x6c, 0x69, 0x6e, 0x67,
	0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x22, 0xac, 0x01, 0x0a, 0x0a, 0x50, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x43, 0x70, 0x63, 0x12, 0x50, 0x0a, 0x16, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69, 0x64,
	0x5f, 0x63, 0x65, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x13, 0x63, 0x70, 0x63, 0x42, 0x69, 0x64, 0x43, 0x65, 0x69, 0x6c, 0x69, 0x6e,
	0x67, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x12, 0x4c, 0x0a, 0x14, 0x65, 0x6e, 0x68, 0x61, 0x6e,
	0x63, 0x65, 0x64, 0x5f, 0x63, 0x70, 0x63, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x12, 0x65, 0x6e, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x43, 0x70, 0x63, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x42, 0xe7, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x42, 0x0c, 0x42, 0x69,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x44, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x3b, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0xa2, 0x02, 0x03, 0x47, 0x41, 0x41, 0xaa, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x41, 0x64, 0x73, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x2e,
	0x56, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0xca, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x5c, 0x41, 0x64, 0x73, 0x5c, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73,
	0x5c, 0x56, 0x31, 0x5c, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0xea, 0x02, 0x22, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x41, 0x64, 0x73, 0x3a, 0x3a, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x41, 0x64, 0x73, 0x3a, 0x3a, 0x56, 0x31, 0x3a, 0x3a, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_ads_googleads_v1_common_bidding_proto_rawDescOnce sync.Once
	file_google_ads_googleads_v1_common_bidding_proto_rawDescData = file_google_ads_googleads_v1_common_bidding_proto_rawDesc
)

func file_google_ads_googleads_v1_common_bidding_proto_rawDescGZIP() []byte {
	file_google_ads_googleads_v1_common_bidding_proto_rawDescOnce.Do(func() {
		file_google_ads_googleads_v1_common_bidding_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_ads_googleads_v1_common_bidding_proto_rawDescData)
	})
	return file_google_ads_googleads_v1_common_bidding_proto_rawDescData
}

var file_google_ads_googleads_v1_common_bidding_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_google_ads_googleads_v1_common_bidding_proto_goTypes = []interface{}{
	(*Commission)(nil),              // 0: google.ads.googleads.v1.common.Commission
	(*EnhancedCpc)(nil),             // 1: google.ads.googleads.v1.common.EnhancedCpc
	(*ManualCpc)(nil),               // 2: google.ads.googleads.v1.common.ManualCpc
	(*ManualCpm)(nil),               // 3: google.ads.googleads.v1.common.ManualCpm
	(*ManualCpv)(nil),               // 4: google.ads.googleads.v1.common.ManualCpv
	(*MaximizeConversions)(nil),     // 5: google.ads.googleads.v1.common.MaximizeConversions
	(*MaximizeConversionValue)(nil), // 6: google.ads.googleads.v1.common.MaximizeConversionValue
	(*PageOnePromoted)(nil),         // 7: google.ads.googleads.v1.common.PageOnePromoted
	(*TargetCpa)(nil),               // 8: google.ads.googleads.v1.common.TargetCpa
	(*TargetCpm)(nil),               // 9: google.ads.googleads.v1.common.TargetCpm
	(*TargetImpressionShare)(nil),   // 10: google.ads.googleads.v1.common.TargetImpressionShare
	(*TargetOutrankShare)(nil),      // 11: google.ads.googleads.v1.common.TargetOutrankShare
	(*TargetRoas)(nil),              // 12: google.ads.googleads.v1.common.TargetRoas
	(*TargetSpend)(nil),             // 13: google.ads.googleads.v1.common.TargetSpend
	(*PercentCpc)(nil),              // 14: google.ads.googleads.v1.common.PercentCpc
	(*wrappers.Int64Value)(nil),     // 15: google.protobuf.Int64Value
	(*wrappers.BoolValue)(nil),      // 16: google.protobuf.BoolValue
	(*wrappers.DoubleValue)(nil),    // 17: google.protobuf.DoubleValue
	(enums.PageOnePromotedStrategyGoalEnum_PageOnePromotedStrategyGoal)(0),     // 18: google.ads.googleads.v1.enums.PageOnePromotedStrategyGoalEnum.PageOnePromotedStrategyGoal
	(enums.TargetImpressionShareLocationEnum_TargetImpressionShareLocation)(0), // 19: google.ads.googleads.v1.enums.TargetImpressionShareLocationEnum.TargetImpressionShareLocation
	(*wrappers.Int32Value)(nil),  // 20: google.protobuf.Int32Value
	(*wrappers.StringValue)(nil), // 21: google.protobuf.StringValue
}
var file_google_ads_googleads_v1_common_bidding_proto_depIdxs = []int32{
	15, // 0: google.ads.googleads.v1.common.Commission.commission_rate_micros:type_name -> google.protobuf.Int64Value
	16, // 1: google.ads.googleads.v1.common.ManualCpc.enhanced_cpc_enabled:type_name -> google.protobuf.BoolValue
	17, // 2: google.ads.googleads.v1.common.MaximizeConversionValue.target_roas:type_name -> google.protobuf.DoubleValue
	18, // 3: google.ads.googleads.v1.common.PageOnePromoted.strategy_goal:type_name -> google.ads.googleads.v1.enums.PageOnePromotedStrategyGoalEnum.PageOnePromotedStrategyGoal
	15, // 4: google.ads.googleads.v1.common.PageOnePromoted.cpc_bid_ceiling_micros:type_name -> google.protobuf.Int64Value
	17, // 5: google.ads.googleads.v1.common.PageOnePromoted.bid_modifier:type_name -> google.protobuf.DoubleValue
	16, // 6: google.ads.googleads.v1.common.PageOnePromoted.only_raise_cpc_bids:type_name -> google.protobuf.BoolValue
	16, // 7: google.ads.googleads.v1.common.PageOnePromoted.raise_cpc_bid_when_budget_constrained:type_name -> google.protobuf.BoolValue
	16, // 8: google.ads.googleads.v1.common.PageOnePromoted.raise_cpc_bid_when_quality_score_is_low:type_name -> google.protobuf.BoolValue
	15, // 9: google.ads.googleads.v1.common.TargetCpa.target_cpa_micros:type_name -> google.protobuf.Int64Value
	15, // 10: google.ads.googleads.v1.common.TargetCpa.cpc_bid_ceiling_micros:type_name -> google.protobuf.Int64Value
	15, // 11: google.ads.googleads.v1.common.TargetCpa.cpc_bid_floor_micros:type_name -> google.protobuf.Int64Value
	19, // 12: google.ads.googleads.v1.common.TargetImpressionShare.location:type_name -> google.ads.googleads.v1.enums.TargetImpressionShareLocationEnum.TargetImpressionShareLocation
	15, // 13: google.ads.googleads.v1.common.TargetImpressionShare.location_fraction_micros:type_name -> google.protobuf.Int64Value
	15, // 14: google.ads.googleads.v1.common.TargetImpressionShare.cpc_bid_ceiling_micros:type_name -> google.protobuf.Int64Value
	20, // 15: google.ads.googleads.v1.common.TargetOutrankShare.target_outrank_share_micros:type_name -> google.protobuf.Int32Value
	21, // 16: google.ads.googleads.v1.common.TargetOutrankShare.competitor_domain:type_name -> google.protobuf.StringValue
	15, // 17: google.ads.googleads.v1.common.TargetOutrankShare.cpc_bid_ceiling_micros:type_name -> google.protobuf.Int64Value
	16, // 18: google.ads.googleads.v1.common.TargetOutrankShare.only_raise_cpc_bids:type_name -> google.protobuf.BoolValue
	16, // 19: google.ads.googleads.v1.common.TargetOutrankShare.raise_cpc_bid_when_quality_score_is_low:type_name -> google.protobuf.BoolValue
	17, // 20: google.ads.googleads.v1.common.TargetRoas.target_roas:type_name -> google.protobuf.DoubleValue
	15, // 21: google.ads.googleads.v1.common.TargetRoas.cpc_bid_ceiling_micros:type_name -> google.protobuf.Int64Value
	15, // 22: google.ads.googleads.v1.common.TargetRoas.cpc_bid_floor_micros:type_name -> google.protobuf.Int64Value
	15, // 23: google.ads.googleads.v1.common.TargetSpend.target_spend_micros:type_name -> google.protobuf.Int64Value
	15, // 24: google.ads.googleads.v1.common.TargetSpend.cpc_bid_ceiling_micros:type_name -> google.protobuf.Int64Value
	15, // 25: google.ads.googleads.v1.common.PercentCpc.cpc_bid_ceiling_micros:type_name -> google.protobuf.Int64Value
	16, // 26: google.ads.googleads.v1.common.PercentCpc.enhanced_cpc_enabled:type_name -> google.protobuf.BoolValue
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_google_ads_googleads_v1_common_bidding_proto_init() }
func file_google_ads_googleads_v1_common_bidding_proto_init() {
	if File_google_ads_googleads_v1_common_bidding_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Commission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnhancedCpc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualCpc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualCpm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualCpv); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaximizeConversions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaximizeConversionValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageOnePromoted); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetCpa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetCpm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetImpressionShare); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetOutrankShare); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetRoas); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetSpend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_bidding_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PercentCpc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_ads_googleads_v1_common_bidding_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_ads_googleads_v1_common_bidding_proto_goTypes,
		DependencyIndexes: file_google_ads_googleads_v1_common_bidding_proto_depIdxs,
		MessageInfos:      file_google_ads_googleads_v1_common_bidding_proto_msgTypes,
	}.Build()
	File_google_ads_googleads_v1_common_bidding_proto = out.File
	file_google_ads_googleads_v1_common_bidding_proto_rawDesc = nil
	file_google_ads_googleads_v1_common_bidding_proto_goTypes = nil
	file_google_ads_googleads_v1_common_bidding_proto_depIdxs = nil
}
