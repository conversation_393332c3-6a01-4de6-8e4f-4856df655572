// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.22.0
// 	protoc        v3.11.2
// source: google/ads/googleads/v1/errors/conversion_adjustment_upload_error.proto

package errors

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// Enum describing possible conversion adjustment upload errors.
type ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError int32

const (
	// Not specified.
	ConversionAdjustmentUploadErrorEnum_UNSPECIFIED ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError = 0
	// The received error code is not known in this version.
	ConversionAdjustmentUploadErrorEnum_UNKNOWN ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError = 1
	// The specified conversion action was created too recently.
	// Please try the upload again after 4-6 hours have passed since the
	// conversion action was created.
	ConversionAdjustmentUploadErrorEnum_TOO_RECENT_CONVERSION_ACTION ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError = 2
	// No conversion action of a supported ConversionActionType that matches the
	// provided information can be found for the customer.
	ConversionAdjustmentUploadErrorEnum_INVALID_CONVERSION_ACTION ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError = 3
	// A retraction was already reported for this conversion.
	ConversionAdjustmentUploadErrorEnum_CONVERSION_ALREADY_RETRACTED ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError = 4
	// A conversion for the supplied combination of conversion
	// action and conversion identifier could not be found.
	ConversionAdjustmentUploadErrorEnum_CONVERSION_NOT_FOUND ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError = 5
	// The specified conversion has already expired. Conversions expire after 55
	// days, after which adjustments cannot be reported against them.
	ConversionAdjustmentUploadErrorEnum_CONVERSION_EXPIRED ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError = 6
	// The supplied adjustment date time precedes that of the original
	// conversion.
	ConversionAdjustmentUploadErrorEnum_ADJUSTMENT_PRECEDES_CONVERSION ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError = 7
	// A restatement with a more recent adjustment date time was already
	// reported for this conversion.
	ConversionAdjustmentUploadErrorEnum_MORE_RECENT_RESTATEMENT_FOUND ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError = 8
	// The conversion was created too recently.
	ConversionAdjustmentUploadErrorEnum_TOO_RECENT_CONVERSION ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError = 9
	// Restatements cannot be reported for a conversion action that always uses
	// the default value.
	ConversionAdjustmentUploadErrorEnum_CANNOT_RESTATE_CONVERSION_ACTION_THAT_ALWAYS_USES_DEFAULT_CONVERSION_VALUE ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError = 10
)

// Enum value maps for ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError.
var (
	ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError_name = map[int32]string{
		0:  "UNSPECIFIED",
		1:  "UNKNOWN",
		2:  "TOO_RECENT_CONVERSION_ACTION",
		3:  "INVALID_CONVERSION_ACTION",
		4:  "CONVERSION_ALREADY_RETRACTED",
		5:  "CONVERSION_NOT_FOUND",
		6:  "CONVERSION_EXPIRED",
		7:  "ADJUSTMENT_PRECEDES_CONVERSION",
		8:  "MORE_RECENT_RESTATEMENT_FOUND",
		9:  "TOO_RECENT_CONVERSION",
		10: "CANNOT_RESTATE_CONVERSION_ACTION_THAT_ALWAYS_USES_DEFAULT_CONVERSION_VALUE",
	}
	ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError_value = map[string]int32{
		"UNSPECIFIED":                    0,
		"UNKNOWN":                        1,
		"TOO_RECENT_CONVERSION_ACTION":   2,
		"INVALID_CONVERSION_ACTION":      3,
		"CONVERSION_ALREADY_RETRACTED":   4,
		"CONVERSION_NOT_FOUND":           5,
		"CONVERSION_EXPIRED":             6,
		"ADJUSTMENT_PRECEDES_CONVERSION": 7,
		"MORE_RECENT_RESTATEMENT_FOUND":  8,
		"TOO_RECENT_CONVERSION":          9,
		"CANNOT_RESTATE_CONVERSION_ACTION_THAT_ALWAYS_USES_DEFAULT_CONVERSION_VALUE": 10,
	}
)

func (x ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError) Enum() *ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError {
	p := new(ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError)
	*p = x
	return p
}

func (x ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError) Descriptor() protoreflect.EnumDescriptor {
	return file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_enumTypes[0].Descriptor()
}

func (ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError) Type() protoreflect.EnumType {
	return &file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_enumTypes[0]
}

func (x ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError.Descriptor instead.
func (ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError) EnumDescriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_rawDescGZIP(), []int{0, 0}
}

// Container for enum describing possible conversion adjustment upload errors.
type ConversionAdjustmentUploadErrorEnum struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ConversionAdjustmentUploadErrorEnum) Reset() {
	*x = ConversionAdjustmentUploadErrorEnum{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConversionAdjustmentUploadErrorEnum) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConversionAdjustmentUploadErrorEnum) ProtoMessage() {}

func (x *ConversionAdjustmentUploadErrorEnum) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConversionAdjustmentUploadErrorEnum.ProtoReflect.Descriptor instead.
func (*ConversionAdjustmentUploadErrorEnum) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_rawDescGZIP(), []int{0}
}

var File_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto protoreflect.FileDescriptor

var file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_rawDesc = []byte{
	0x0a, 0x47, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x6a, 0x75,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xae, 0x03, 0x0a, 0x23, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x22,
	0x86, 0x03, 0x0a, 0x1f, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x01, 0x12, 0x20, 0x0a, 0x1c, 0x54, 0x4f, 0x4f, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x4e, 0x54, 0x5f,
	0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43,
	0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x41, 0x43, 0x54,
	0x45, 0x44, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x16,
	0x0a, 0x12, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x58, 0x50,
	0x49, 0x52, 0x45, 0x44, 0x10, 0x06, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x44, 0x4a, 0x55, 0x53, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x52, 0x45, 0x43, 0x45, 0x44, 0x45, 0x53, 0x5f, 0x43, 0x4f,
	0x4e, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x21, 0x0a, 0x1d, 0x4d, 0x4f,
	0x52, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x08, 0x12, 0x19, 0x0a,
	0x15, 0x54, 0x4f, 0x4f, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x56,
	0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x4e, 0x0a, 0x4a, 0x43, 0x41, 0x4e, 0x4e,
	0x4f, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45,
	0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x48, 0x41,
	0x54, 0x5f, 0x41, 0x4c, 0x57, 0x41, 0x59, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x53, 0x5f, 0x44, 0x45,
	0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x0a, 0x42, 0xff, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x42,
	0x24, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x44, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61,
	0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x3b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0xa2, 0x02, 0x03,
	0x47, 0x41, 0x41, 0xaa, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x41, 0x64, 0x73,
	0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x2e, 0x56, 0x31, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0xca, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x41, 0x64,
	0x73, 0x5c, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x5c, 0x56, 0x31, 0x5c, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0xea, 0x02, 0x22, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a,
	0x41, 0x64, 0x73, 0x3a, 0x3a, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x3a, 0x3a,
	0x56, 0x31, 0x3a, 0x3a, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_rawDescOnce sync.Once
	file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_rawDescData = file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_rawDesc
)

func file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_rawDescGZIP() []byte {
	file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_rawDescOnce.Do(func() {
		file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_rawDescData)
	})
	return file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_rawDescData
}

var file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_goTypes = []interface{}{
	(ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError)(0), // 0: google.ads.googleads.v1.errors.ConversionAdjustmentUploadErrorEnum.ConversionAdjustmentUploadError
	(*ConversionAdjustmentUploadErrorEnum)(nil),                              // 1: google.ads.googleads.v1.errors.ConversionAdjustmentUploadErrorEnum
}
var file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_init() }
func file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_init() {
	if File_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConversionAdjustmentUploadErrorEnum); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_goTypes,
		DependencyIndexes: file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_depIdxs,
		EnumInfos:         file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_enumTypes,
		MessageInfos:      file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_msgTypes,
	}.Build()
	File_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto = out.File
	file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_rawDesc = nil
	file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_goTypes = nil
	file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_depIdxs = nil
}
