// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.22.0
// 	protoc        v3.11.2
// source: google/ads/googleads/v1/errors/criterion_error.proto

package errors

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// Enum describing possible criterion errors.
type CriterionErrorEnum_CriterionError int32

const (
	// Enum unspecified.
	CriterionErrorEnum_UNSPECIFIED CriterionErrorEnum_CriterionError = 0
	// The received error code is not known in this version.
	CriterionErrorEnum_UNKNOWN CriterionErrorEnum_CriterionError = 1
	// Concrete type of criterion is required for CREATE and UPDATE operations.
	CriterionErrorEnum_CONCRETE_TYPE_REQUIRED CriterionErrorEnum_CriterionError = 2
	// The category requested for exclusion is invalid.
	CriterionErrorEnum_INVALID_EXCLUDED_CATEGORY CriterionErrorEnum_CriterionError = 3
	// Invalid keyword criteria text.
	CriterionErrorEnum_INVALID_KEYWORD_TEXT CriterionErrorEnum_CriterionError = 4
	// Keyword text should be less than 80 chars.
	CriterionErrorEnum_KEYWORD_TEXT_TOO_LONG CriterionErrorEnum_CriterionError = 5
	// Keyword text has too many words.
	CriterionErrorEnum_KEYWORD_HAS_TOO_MANY_WORDS CriterionErrorEnum_CriterionError = 6
	// Keyword text has invalid characters or symbols.
	CriterionErrorEnum_KEYWORD_HAS_INVALID_CHARS CriterionErrorEnum_CriterionError = 7
	// Invalid placement URL.
	CriterionErrorEnum_INVALID_PLACEMENT_URL CriterionErrorEnum_CriterionError = 8
	// Invalid user list criterion.
	CriterionErrorEnum_INVALID_USER_LIST CriterionErrorEnum_CriterionError = 9
	// Invalid user interest criterion.
	CriterionErrorEnum_INVALID_USER_INTEREST CriterionErrorEnum_CriterionError = 10
	// Placement URL has wrong format.
	CriterionErrorEnum_INVALID_FORMAT_FOR_PLACEMENT_URL CriterionErrorEnum_CriterionError = 11
	// Placement URL is too long.
	CriterionErrorEnum_PLACEMENT_URL_IS_TOO_LONG CriterionErrorEnum_CriterionError = 12
	// Indicates the URL contains an illegal character.
	CriterionErrorEnum_PLACEMENT_URL_HAS_ILLEGAL_CHAR CriterionErrorEnum_CriterionError = 13
	// Indicates the URL contains multiple comma separated URLs.
	CriterionErrorEnum_PLACEMENT_URL_HAS_MULTIPLE_SITES_IN_LINE CriterionErrorEnum_CriterionError = 14
	// Indicates the domain is blacklisted.
	CriterionErrorEnum_PLACEMENT_IS_NOT_AVAILABLE_FOR_TARGETING_OR_EXCLUSION CriterionErrorEnum_CriterionError = 15
	// Invalid topic path.
	CriterionErrorEnum_INVALID_TOPIC_PATH CriterionErrorEnum_CriterionError = 16
	// The YouTube Channel Id is invalid.
	CriterionErrorEnum_INVALID_YOUTUBE_CHANNEL_ID CriterionErrorEnum_CriterionError = 17
	// The YouTube Video Id is invalid.
	CriterionErrorEnum_INVALID_YOUTUBE_VIDEO_ID CriterionErrorEnum_CriterionError = 18
	// Indicates the placement is a YouTube vertical channel, which is no longer
	// supported.
	CriterionErrorEnum_YOUTUBE_VERTICAL_CHANNEL_DEPRECATED CriterionErrorEnum_CriterionError = 19
	// Indicates the placement is a YouTube demographic channel, which is no
	// longer supported.
	CriterionErrorEnum_YOUTUBE_DEMOGRAPHIC_CHANNEL_DEPRECATED CriterionErrorEnum_CriterionError = 20
	// YouTube urls are not supported in Placement criterion. Use YouTubeChannel
	// and YouTubeVideo criterion instead.
	CriterionErrorEnum_YOUTUBE_URL_UNSUPPORTED CriterionErrorEnum_CriterionError = 21
	// Criteria type can not be excluded by the customer, like AOL account type
	// cannot target site type criteria.
	CriterionErrorEnum_CANNOT_EXCLUDE_CRITERIA_TYPE CriterionErrorEnum_CriterionError = 22
	// Criteria type can not be targeted.
	CriterionErrorEnum_CANNOT_ADD_CRITERIA_TYPE CriterionErrorEnum_CriterionError = 23
	// Product filter in the product criteria has invalid characters. Operand
	// and the argument in the filter can not have "==" or "&+".
	CriterionErrorEnum_INVALID_PRODUCT_FILTER CriterionErrorEnum_CriterionError = 24
	// Product filter in the product criteria is translated to a string as
	// operand1==argument1&+operand2==argument2, maximum allowed length for the
	// string is 255 chars.
	CriterionErrorEnum_PRODUCT_FILTER_TOO_LONG CriterionErrorEnum_CriterionError = 25
	// Not allowed to exclude similar user list.
	CriterionErrorEnum_CANNOT_EXCLUDE_SIMILAR_USER_LIST CriterionErrorEnum_CriterionError = 26
	// Not allowed to target a closed user list.
	CriterionErrorEnum_CANNOT_ADD_CLOSED_USER_LIST CriterionErrorEnum_CriterionError = 27
	// Not allowed to add display only UserLists to search only campaigns.
	CriterionErrorEnum_CANNOT_ADD_DISPLAY_ONLY_LISTS_TO_SEARCH_ONLY_CAMPAIGNS CriterionErrorEnum_CriterionError = 28
	// Not allowed to add display only UserLists to search plus campaigns.
	CriterionErrorEnum_CANNOT_ADD_DISPLAY_ONLY_LISTS_TO_SEARCH_CAMPAIGNS CriterionErrorEnum_CriterionError = 29
	// Not allowed to add display only UserLists to shopping campaigns.
	CriterionErrorEnum_CANNOT_ADD_DISPLAY_ONLY_LISTS_TO_SHOPPING_CAMPAIGNS CriterionErrorEnum_CriterionError = 30
	// Not allowed to add User interests to search only campaigns.
	CriterionErrorEnum_CANNOT_ADD_USER_INTERESTS_TO_SEARCH_CAMPAIGNS CriterionErrorEnum_CriterionError = 31
	// Not allowed to set bids for this criterion type in search campaigns
	CriterionErrorEnum_CANNOT_SET_BIDS_ON_CRITERION_TYPE_IN_SEARCH_CAMPAIGNS CriterionErrorEnum_CriterionError = 32
	// Final URLs, URL Templates and CustomParameters cannot be set for the
	// criterion types of Gender, AgeRange, UserList, Placement, MobileApp, and
	// MobileAppCategory in search campaigns and shopping campaigns.
	CriterionErrorEnum_CANNOT_ADD_URLS_TO_CRITERION_TYPE_FOR_CAMPAIGN_TYPE CriterionErrorEnum_CriterionError = 33
	// Invalid custom affinity criterion.
	CriterionErrorEnum_INVALID_CUSTOM_AFFINITY CriterionErrorEnum_CriterionError = 96
	// Invalid custom intent criterion.
	CriterionErrorEnum_INVALID_CUSTOM_INTENT CriterionErrorEnum_CriterionError = 97
	// IP address is not valid.
	CriterionErrorEnum_INVALID_IP_ADDRESS CriterionErrorEnum_CriterionError = 34
	// IP format is not valid.
	CriterionErrorEnum_INVALID_IP_FORMAT CriterionErrorEnum_CriterionError = 35
	// Mobile application is not valid.
	CriterionErrorEnum_INVALID_MOBILE_APP CriterionErrorEnum_CriterionError = 36
	// Mobile application category is not valid.
	CriterionErrorEnum_INVALID_MOBILE_APP_CATEGORY CriterionErrorEnum_CriterionError = 37
	// The CriterionId does not exist or is of the incorrect type.
	CriterionErrorEnum_INVALID_CRITERION_ID CriterionErrorEnum_CriterionError = 38
	// The Criterion is not allowed to be targeted.
	CriterionErrorEnum_CANNOT_TARGET_CRITERION CriterionErrorEnum_CriterionError = 39
	// The criterion is not allowed to be targeted as it is deprecated.
	CriterionErrorEnum_CANNOT_TARGET_OBSOLETE_CRITERION CriterionErrorEnum_CriterionError = 40
	// The CriterionId is not valid for the type.
	CriterionErrorEnum_CRITERION_ID_AND_TYPE_MISMATCH CriterionErrorEnum_CriterionError = 41
	// Distance for the radius for the proximity criterion is invalid.
	CriterionErrorEnum_INVALID_PROXIMITY_RADIUS CriterionErrorEnum_CriterionError = 42
	// Units for the distance for the radius for the proximity criterion is
	// invalid.
	CriterionErrorEnum_INVALID_PROXIMITY_RADIUS_UNITS CriterionErrorEnum_CriterionError = 43
	// Street address in the address is not valid.
	CriterionErrorEnum_INVALID_STREETADDRESS_LENGTH CriterionErrorEnum_CriterionError = 44
	// City name in the address is not valid.
	CriterionErrorEnum_INVALID_CITYNAME_LENGTH CriterionErrorEnum_CriterionError = 45
	// Region code in the address is not valid.
	CriterionErrorEnum_INVALID_REGIONCODE_LENGTH CriterionErrorEnum_CriterionError = 46
	// Region name in the address is not valid.
	CriterionErrorEnum_INVALID_REGIONNAME_LENGTH CriterionErrorEnum_CriterionError = 47
	// Postal code in the address is not valid.
	CriterionErrorEnum_INVALID_POSTALCODE_LENGTH CriterionErrorEnum_CriterionError = 48
	// Country code in the address is not valid.
	CriterionErrorEnum_INVALID_COUNTRY_CODE CriterionErrorEnum_CriterionError = 49
	// Latitude for the GeoPoint is not valid.
	CriterionErrorEnum_INVALID_LATITUDE CriterionErrorEnum_CriterionError = 50
	// Longitude for the GeoPoint is not valid.
	CriterionErrorEnum_INVALID_LONGITUDE CriterionErrorEnum_CriterionError = 51
	// The Proximity input is not valid. Both address and geoPoint cannot be
	// null.
	CriterionErrorEnum_PROXIMITY_GEOPOINT_AND_ADDRESS_BOTH_CANNOT_BE_NULL CriterionErrorEnum_CriterionError = 52
	// The Proximity address cannot be geocoded to a valid lat/long.
	CriterionErrorEnum_INVALID_PROXIMITY_ADDRESS CriterionErrorEnum_CriterionError = 53
	// User domain name is not valid.
	CriterionErrorEnum_INVALID_USER_DOMAIN_NAME CriterionErrorEnum_CriterionError = 54
	// Length of serialized criterion parameter exceeded size limit.
	CriterionErrorEnum_CRITERION_PARAMETER_TOO_LONG CriterionErrorEnum_CriterionError = 55
	// Time interval in the AdSchedule overlaps with another AdSchedule.
	CriterionErrorEnum_AD_SCHEDULE_TIME_INTERVALS_OVERLAP CriterionErrorEnum_CriterionError = 56
	// AdSchedule time interval cannot span multiple days.
	CriterionErrorEnum_AD_SCHEDULE_INTERVAL_CANNOT_SPAN_MULTIPLE_DAYS CriterionErrorEnum_CriterionError = 57
	// AdSchedule time interval specified is invalid, endTime cannot be earlier
	// than startTime.
	CriterionErrorEnum_AD_SCHEDULE_INVALID_TIME_INTERVAL CriterionErrorEnum_CriterionError = 58
	// The number of AdSchedule entries in a day exceeds the limit.
	CriterionErrorEnum_AD_SCHEDULE_EXCEEDED_INTERVALS_PER_DAY_LIMIT CriterionErrorEnum_CriterionError = 59
	// CriteriaId does not match the interval of the AdSchedule specified.
	CriterionErrorEnum_AD_SCHEDULE_CRITERION_ID_MISMATCHING_FIELDS CriterionErrorEnum_CriterionError = 60
	// Cannot set bid modifier for this criterion type.
	CriterionErrorEnum_CANNOT_BID_MODIFY_CRITERION_TYPE CriterionErrorEnum_CriterionError = 61
	// Cannot bid modify criterion, since it is opted out of the campaign.
	CriterionErrorEnum_CANNOT_BID_MODIFY_CRITERION_CAMPAIGN_OPTED_OUT CriterionErrorEnum_CriterionError = 62
	// Cannot set bid modifier for a negative criterion.
	CriterionErrorEnum_CANNOT_BID_MODIFY_NEGATIVE_CRITERION CriterionErrorEnum_CriterionError = 63
	// Bid Modifier already exists. Use SET operation to update.
	CriterionErrorEnum_BID_MODIFIER_ALREADY_EXISTS CriterionErrorEnum_CriterionError = 64
	// Feed Id is not allowed in these Location Groups.
	CriterionErrorEnum_FEED_ID_NOT_ALLOWED CriterionErrorEnum_CriterionError = 65
	// The account may not use the requested criteria type. For example, some
	// accounts are restricted to keywords only.
	CriterionErrorEnum_ACCOUNT_INELIGIBLE_FOR_CRITERIA_TYPE CriterionErrorEnum_CriterionError = 66
	// The requested criteria type cannot be used with campaign or ad group
	// bidding strategy.
	CriterionErrorEnum_CRITERIA_TYPE_INVALID_FOR_BIDDING_STRATEGY CriterionErrorEnum_CriterionError = 67
	// The Criterion is not allowed to be excluded.
	CriterionErrorEnum_CANNOT_EXCLUDE_CRITERION CriterionErrorEnum_CriterionError = 68
	// The criterion is not allowed to be removed. For example, we cannot remove
	// any of the device criterion.
	CriterionErrorEnum_CANNOT_REMOVE_CRITERION CriterionErrorEnum_CriterionError = 69
	// The combined length of product dimension values of the product scope
	// criterion is too long.
	CriterionErrorEnum_PRODUCT_SCOPE_TOO_LONG CriterionErrorEnum_CriterionError = 70
	// Product scope contains too many dimensions.
	CriterionErrorEnum_PRODUCT_SCOPE_TOO_MANY_DIMENSIONS CriterionErrorEnum_CriterionError = 71
	// The combined length of product dimension values of the product partition
	// criterion is too long.
	CriterionErrorEnum_PRODUCT_PARTITION_TOO_LONG CriterionErrorEnum_CriterionError = 72
	// Product partition contains too many dimensions.
	CriterionErrorEnum_PRODUCT_PARTITION_TOO_MANY_DIMENSIONS CriterionErrorEnum_CriterionError = 73
	// The product dimension is invalid (e.g. dimension contains illegal value,
	// dimension type is represented with wrong class, etc). Product dimension
	// value can not contain "==" or "&+".
	CriterionErrorEnum_INVALID_PRODUCT_DIMENSION CriterionErrorEnum_CriterionError = 74
	// Product dimension type is either invalid for campaigns of this type or
	// cannot be used in the current context. BIDDING_CATEGORY_Lx and
	// PRODUCT_TYPE_Lx product dimensions must be used in ascending order of
	// their levels: L1, L2, L3, L4, L5... The levels must be specified
	// sequentially and start from L1. Furthermore, an "others" product
	// partition cannot be subdivided with a dimension of the same type but of a
	// higher level ("others" BIDDING_CATEGORY_L3 can be subdivided with BRAND
	// but not with BIDDING_CATEGORY_L4).
	CriterionErrorEnum_INVALID_PRODUCT_DIMENSION_TYPE CriterionErrorEnum_CriterionError = 75
	// Bidding categories do not form a valid path in the Shopping bidding
	// category taxonomy.
	CriterionErrorEnum_INVALID_PRODUCT_BIDDING_CATEGORY CriterionErrorEnum_CriterionError = 76
	// ShoppingSetting must be added to the campaign before ProductScope
	// criteria can be added.
	CriterionErrorEnum_MISSING_SHOPPING_SETTING CriterionErrorEnum_CriterionError = 77
	// Matching function is invalid.
	CriterionErrorEnum_INVALID_MATCHING_FUNCTION CriterionErrorEnum_CriterionError = 78
	// Filter parameters not allowed for location groups targeting.
	CriterionErrorEnum_LOCATION_FILTER_NOT_ALLOWED CriterionErrorEnum_CriterionError = 79
	// Feed not found, or the feed is not an enabled location feed.
	CriterionErrorEnum_INVALID_FEED_FOR_LOCATION_FILTER CriterionErrorEnum_CriterionError = 98
	// Given location filter parameter is invalid for location groups targeting.
	CriterionErrorEnum_LOCATION_FILTER_INVALID CriterionErrorEnum_CriterionError = 80
	// Criteria type cannot be associated with a campaign and its ad group(s)
	// simultaneously.
	CriterionErrorEnum_CANNOT_ATTACH_CRITERIA_AT_CAMPAIGN_AND_ADGROUP CriterionErrorEnum_CriterionError = 81
	// Range represented by hotel length of stay's min nights and max nights
	// overlaps with an existing criterion.
	CriterionErrorEnum_HOTEL_LENGTH_OF_STAY_OVERLAPS_WITH_EXISTING_CRITERION CriterionErrorEnum_CriterionError = 82
	// Range represented by hotel advance booking window's min days and max days
	// overlaps with an existing criterion.
	CriterionErrorEnum_HOTEL_ADVANCE_BOOKING_WINDOW_OVERLAPS_WITH_EXISTING_CRITERION CriterionErrorEnum_CriterionError = 83
	// The field is not allowed to be set when the negative field is set to
	// true, e.g. we don't allow bids in negative ad group or campaign criteria.
	CriterionErrorEnum_FIELD_INCOMPATIBLE_WITH_NEGATIVE_TARGETING CriterionErrorEnum_CriterionError = 84
	// The combination of operand and operator in webpage condition is invalid.
	CriterionErrorEnum_INVALID_WEBPAGE_CONDITION CriterionErrorEnum_CriterionError = 85
	// The URL of webpage condition is invalid.
	CriterionErrorEnum_INVALID_WEBPAGE_CONDITION_URL CriterionErrorEnum_CriterionError = 86
	// The URL of webpage condition cannot be empty or contain white space.
	CriterionErrorEnum_WEBPAGE_CONDITION_URL_CANNOT_BE_EMPTY CriterionErrorEnum_CriterionError = 87
	// The URL of webpage condition contains an unsupported protocol.
	CriterionErrorEnum_WEBPAGE_CONDITION_URL_UNSUPPORTED_PROTOCOL CriterionErrorEnum_CriterionError = 88
	// The URL of webpage condition cannot be an IP address.
	CriterionErrorEnum_WEBPAGE_CONDITION_URL_CANNOT_BE_IP_ADDRESS CriterionErrorEnum_CriterionError = 89
	// The domain of the URL is not consistent with the domain in campaign
	// setting.
	CriterionErrorEnum_WEBPAGE_CONDITION_URL_DOMAIN_NOT_CONSISTENT_WITH_CAMPAIGN_SETTING CriterionErrorEnum_CriterionError = 90
	// The URL of webpage condition cannot be a public suffix itself.
	CriterionErrorEnum_WEBPAGE_CONDITION_URL_CANNOT_BE_PUBLIC_SUFFIX CriterionErrorEnum_CriterionError = 91
	// The URL of webpage condition has an invalid public suffix.
	CriterionErrorEnum_WEBPAGE_CONDITION_URL_INVALID_PUBLIC_SUFFIX CriterionErrorEnum_CriterionError = 92
	// Value track parameter is not supported in webpage condition URL.
	CriterionErrorEnum_WEBPAGE_CONDITION_URL_VALUE_TRACK_VALUE_NOT_SUPPORTED CriterionErrorEnum_CriterionError = 93
	// Only one URL-EQUALS webpage condition is allowed in a webpage
	// criterion and it cannot be combined with other conditions.
	CriterionErrorEnum_WEBPAGE_CRITERION_URL_EQUALS_CAN_HAVE_ONLY_ONE_CONDITION CriterionErrorEnum_CriterionError = 94
	// A webpage criterion cannot be added to a non-DSA ad group.
	CriterionErrorEnum_WEBPAGE_CRITERION_NOT_SUPPORTED_ON_NON_DSA_AD_GROUP CriterionErrorEnum_CriterionError = 95
)

// Enum value maps for CriterionErrorEnum_CriterionError.
var (
	CriterionErrorEnum_CriterionError_name = map[int32]string{
		0:  "UNSPECIFIED",
		1:  "UNKNOWN",
		2:  "CONCRETE_TYPE_REQUIRED",
		3:  "INVALID_EXCLUDED_CATEGORY",
		4:  "INVALID_KEYWORD_TEXT",
		5:  "KEYWORD_TEXT_TOO_LONG",
		6:  "KEYWORD_HAS_TOO_MANY_WORDS",
		7:  "KEYWORD_HAS_INVALID_CHARS",
		8:  "INVALID_PLACEMENT_URL",
		9:  "INVALID_USER_LIST",
		10: "INVALID_USER_INTEREST",
		11: "INVALID_FORMAT_FOR_PLACEMENT_URL",
		12: "PLACEMENT_URL_IS_TOO_LONG",
		13: "PLACEMENT_URL_HAS_ILLEGAL_CHAR",
		14: "PLACEMENT_URL_HAS_MULTIPLE_SITES_IN_LINE",
		15: "PLACEMENT_IS_NOT_AVAILABLE_FOR_TARGETING_OR_EXCLUSION",
		16: "INVALID_TOPIC_PATH",
		17: "INVALID_YOUTUBE_CHANNEL_ID",
		18: "INVALID_YOUTUBE_VIDEO_ID",
		19: "YOUTUBE_VERTICAL_CHANNEL_DEPRECATED",
		20: "YOUTUBE_DEMOGRAPHIC_CHANNEL_DEPRECATED",
		21: "YOUTUBE_URL_UNSUPPORTED",
		22: "CANNOT_EXCLUDE_CRITERIA_TYPE",
		23: "CANNOT_ADD_CRITERIA_TYPE",
		24: "INVALID_PRODUCT_FILTER",
		25: "PRODUCT_FILTER_TOO_LONG",
		26: "CANNOT_EXCLUDE_SIMILAR_USER_LIST",
		27: "CANNOT_ADD_CLOSED_USER_LIST",
		28: "CANNOT_ADD_DISPLAY_ONLY_LISTS_TO_SEARCH_ONLY_CAMPAIGNS",
		29: "CANNOT_ADD_DISPLAY_ONLY_LISTS_TO_SEARCH_CAMPAIGNS",
		30: "CANNOT_ADD_DISPLAY_ONLY_LISTS_TO_SHOPPING_CAMPAIGNS",
		31: "CANNOT_ADD_USER_INTERESTS_TO_SEARCH_CAMPAIGNS",
		32: "CANNOT_SET_BIDS_ON_CRITERION_TYPE_IN_SEARCH_CAMPAIGNS",
		33: "CANNOT_ADD_URLS_TO_CRITERION_TYPE_FOR_CAMPAIGN_TYPE",
		96: "INVALID_CUSTOM_AFFINITY",
		97: "INVALID_CUSTOM_INTENT",
		34: "INVALID_IP_ADDRESS",
		35: "INVALID_IP_FORMAT",
		36: "INVALID_MOBILE_APP",
		37: "INVALID_MOBILE_APP_CATEGORY",
		38: "INVALID_CRITERION_ID",
		39: "CANNOT_TARGET_CRITERION",
		40: "CANNOT_TARGET_OBSOLETE_CRITERION",
		41: "CRITERION_ID_AND_TYPE_MISMATCH",
		42: "INVALID_PROXIMITY_RADIUS",
		43: "INVALID_PROXIMITY_RADIUS_UNITS",
		44: "INVALID_STREETADDRESS_LENGTH",
		45: "INVALID_CITYNAME_LENGTH",
		46: "INVALID_REGIONCODE_LENGTH",
		47: "INVALID_REGIONNAME_LENGTH",
		48: "INVALID_POSTALCODE_LENGTH",
		49: "INVALID_COUNTRY_CODE",
		50: "INVALID_LATITUDE",
		51: "INVALID_LONGITUDE",
		52: "PROXIMITY_GEOPOINT_AND_ADDRESS_BOTH_CANNOT_BE_NULL",
		53: "INVALID_PROXIMITY_ADDRESS",
		54: "INVALID_USER_DOMAIN_NAME",
		55: "CRITERION_PARAMETER_TOO_LONG",
		56: "AD_SCHEDULE_TIME_INTERVALS_OVERLAP",
		57: "AD_SCHEDULE_INTERVAL_CANNOT_SPAN_MULTIPLE_DAYS",
		58: "AD_SCHEDULE_INVALID_TIME_INTERVAL",
		59: "AD_SCHEDULE_EXCEEDED_INTERVALS_PER_DAY_LIMIT",
		60: "AD_SCHEDULE_CRITERION_ID_MISMATCHING_FIELDS",
		61: "CANNOT_BID_MODIFY_CRITERION_TYPE",
		62: "CANNOT_BID_MODIFY_CRITERION_CAMPAIGN_OPTED_OUT",
		63: "CANNOT_BID_MODIFY_NEGATIVE_CRITERION",
		64: "BID_MODIFIER_ALREADY_EXISTS",
		65: "FEED_ID_NOT_ALLOWED",
		66: "ACCOUNT_INELIGIBLE_FOR_CRITERIA_TYPE",
		67: "CRITERIA_TYPE_INVALID_FOR_BIDDING_STRATEGY",
		68: "CANNOT_EXCLUDE_CRITERION",
		69: "CANNOT_REMOVE_CRITERION",
		70: "PRODUCT_SCOPE_TOO_LONG",
		71: "PRODUCT_SCOPE_TOO_MANY_DIMENSIONS",
		72: "PRODUCT_PARTITION_TOO_LONG",
		73: "PRODUCT_PARTITION_TOO_MANY_DIMENSIONS",
		74: "INVALID_PRODUCT_DIMENSION",
		75: "INVALID_PRODUCT_DIMENSION_TYPE",
		76: "INVALID_PRODUCT_BIDDING_CATEGORY",
		77: "MISSING_SHOPPING_SETTING",
		78: "INVALID_MATCHING_FUNCTION",
		79: "LOCATION_FILTER_NOT_ALLOWED",
		98: "INVALID_FEED_FOR_LOCATION_FILTER",
		80: "LOCATION_FILTER_INVALID",
		81: "CANNOT_ATTACH_CRITERIA_AT_CAMPAIGN_AND_ADGROUP",
		82: "HOTEL_LENGTH_OF_STAY_OVERLAPS_WITH_EXISTING_CRITERION",
		83: "HOTEL_ADVANCE_BOOKING_WINDOW_OVERLAPS_WITH_EXISTING_CRITERION",
		84: "FIELD_INCOMPATIBLE_WITH_NEGATIVE_TARGETING",
		85: "INVALID_WEBPAGE_CONDITION",
		86: "INVALID_WEBPAGE_CONDITION_URL",
		87: "WEBPAGE_CONDITION_URL_CANNOT_BE_EMPTY",
		88: "WEBPAGE_CONDITION_URL_UNSUPPORTED_PROTOCOL",
		89: "WEBPAGE_CONDITION_URL_CANNOT_BE_IP_ADDRESS",
		90: "WEBPAGE_CONDITION_URL_DOMAIN_NOT_CONSISTENT_WITH_CAMPAIGN_SETTING",
		91: "WEBPAGE_CONDITION_URL_CANNOT_BE_PUBLIC_SUFFIX",
		92: "WEBPAGE_CONDITION_URL_INVALID_PUBLIC_SUFFIX",
		93: "WEBPAGE_CONDITION_URL_VALUE_TRACK_VALUE_NOT_SUPPORTED",
		94: "WEBPAGE_CRITERION_URL_EQUALS_CAN_HAVE_ONLY_ONE_CONDITION",
		95: "WEBPAGE_CRITERION_NOT_SUPPORTED_ON_NON_DSA_AD_GROUP",
	}
	CriterionErrorEnum_CriterionError_value = map[string]int32{
		"UNSPECIFIED":                                                       0,
		"UNKNOWN":                                                           1,
		"CONCRETE_TYPE_REQUIRED":                                            2,
		"INVALID_EXCLUDED_CATEGORY":                                         3,
		"INVALID_KEYWORD_TEXT":                                              4,
		"KEYWORD_TEXT_TOO_LONG":                                             5,
		"KEYWORD_HAS_TOO_MANY_WORDS":                                        6,
		"KEYWORD_HAS_INVALID_CHARS":                                         7,
		"INVALID_PLACEMENT_URL":                                             8,
		"INVALID_USER_LIST":                                                 9,
		"INVALID_USER_INTEREST":                                             10,
		"INVALID_FORMAT_FOR_PLACEMENT_URL":                                  11,
		"PLACEMENT_URL_IS_TOO_LONG":                                         12,
		"PLACEMENT_URL_HAS_ILLEGAL_CHAR":                                    13,
		"PLACEMENT_URL_HAS_MULTIPLE_SITES_IN_LINE":                          14,
		"PLACEMENT_IS_NOT_AVAILABLE_FOR_TARGETING_OR_EXCLUSION":             15,
		"INVALID_TOPIC_PATH":                                                16,
		"INVALID_YOUTUBE_CHANNEL_ID":                                        17,
		"INVALID_YOUTUBE_VIDEO_ID":                                          18,
		"YOUTUBE_VERTICAL_CHANNEL_DEPRECATED":                               19,
		"YOUTUBE_DEMOGRAPHIC_CHANNEL_DEPRECATED":                            20,
		"YOUTUBE_URL_UNSUPPORTED":                                           21,
		"CANNOT_EXCLUDE_CRITERIA_TYPE":                                      22,
		"CANNOT_ADD_CRITERIA_TYPE":                                          23,
		"INVALID_PRODUCT_FILTER":                                            24,
		"PRODUCT_FILTER_TOO_LONG":                                           25,
		"CANNOT_EXCLUDE_SIMILAR_USER_LIST":                                  26,
		"CANNOT_ADD_CLOSED_USER_LIST":                                       27,
		"CANNOT_ADD_DISPLAY_ONLY_LISTS_TO_SEARCH_ONLY_CAMPAIGNS":            28,
		"CANNOT_ADD_DISPLAY_ONLY_LISTS_TO_SEARCH_CAMPAIGNS":                 29,
		"CANNOT_ADD_DISPLAY_ONLY_LISTS_TO_SHOPPING_CAMPAIGNS":               30,
		"CANNOT_ADD_USER_INTERESTS_TO_SEARCH_CAMPAIGNS":                     31,
		"CANNOT_SET_BIDS_ON_CRITERION_TYPE_IN_SEARCH_CAMPAIGNS":             32,
		"CANNOT_ADD_URLS_TO_CRITERION_TYPE_FOR_CAMPAIGN_TYPE":               33,
		"INVALID_CUSTOM_AFFINITY":                                           96,
		"INVALID_CUSTOM_INTENT":                                             97,
		"INVALID_IP_ADDRESS":                                                34,
		"INVALID_IP_FORMAT":                                                 35,
		"INVALID_MOBILE_APP":                                                36,
		"INVALID_MOBILE_APP_CATEGORY":                                       37,
		"INVALID_CRITERION_ID":                                              38,
		"CANNOT_TARGET_CRITERION":                                           39,
		"CANNOT_TARGET_OBSOLETE_CRITERION":                                  40,
		"CRITERION_ID_AND_TYPE_MISMATCH":                                    41,
		"INVALID_PROXIMITY_RADIUS":                                          42,
		"INVALID_PROXIMITY_RADIUS_UNITS":                                    43,
		"INVALID_STREETADDRESS_LENGTH":                                      44,
		"INVALID_CITYNAME_LENGTH":                                           45,
		"INVALID_REGIONCODE_LENGTH":                                         46,
		"INVALID_REGIONNAME_LENGTH":                                         47,
		"INVALID_POSTALCODE_LENGTH":                                         48,
		"INVALID_COUNTRY_CODE":                                              49,
		"INVALID_LATITUDE":                                                  50,
		"INVALID_LONGITUDE":                                                 51,
		"PROXIMITY_GEOPOINT_AND_ADDRESS_BOTH_CANNOT_BE_NULL":                52,
		"INVALID_PROXIMITY_ADDRESS":                                         53,
		"INVALID_USER_DOMAIN_NAME":                                          54,
		"CRITERION_PARAMETER_TOO_LONG":                                      55,
		"AD_SCHEDULE_TIME_INTERVALS_OVERLAP":                                56,
		"AD_SCHEDULE_INTERVAL_CANNOT_SPAN_MULTIPLE_DAYS":                    57,
		"AD_SCHEDULE_INVALID_TIME_INTERVAL":                                 58,
		"AD_SCHEDULE_EXCEEDED_INTERVALS_PER_DAY_LIMIT":                      59,
		"AD_SCHEDULE_CRITERION_ID_MISMATCHING_FIELDS":                       60,
		"CANNOT_BID_MODIFY_CRITERION_TYPE":                                  61,
		"CANNOT_BID_MODIFY_CRITERION_CAMPAIGN_OPTED_OUT":                    62,
		"CANNOT_BID_MODIFY_NEGATIVE_CRITERION":                              63,
		"BID_MODIFIER_ALREADY_EXISTS":                                       64,
		"FEED_ID_NOT_ALLOWED":                                               65,
		"ACCOUNT_INELIGIBLE_FOR_CRITERIA_TYPE":                              66,
		"CRITERIA_TYPE_INVALID_FOR_BIDDING_STRATEGY":                        67,
		"CANNOT_EXCLUDE_CRITERION":                                          68,
		"CANNOT_REMOVE_CRITERION":                                           69,
		"PRODUCT_SCOPE_TOO_LONG":                                            70,
		"PRODUCT_SCOPE_TOO_MANY_DIMENSIONS":                                 71,
		"PRODUCT_PARTITION_TOO_LONG":                                        72,
		"PRODUCT_PARTITION_TOO_MANY_DIMENSIONS":                             73,
		"INVALID_PRODUCT_DIMENSION":                                         74,
		"INVALID_PRODUCT_DIMENSION_TYPE":                                    75,
		"INVALID_PRODUCT_BIDDING_CATEGORY":                                  76,
		"MISSING_SHOPPING_SETTING":                                          77,
		"INVALID_MATCHING_FUNCTION":                                         78,
		"LOCATION_FILTER_NOT_ALLOWED":                                       79,
		"INVALID_FEED_FOR_LOCATION_FILTER":                                  98,
		"LOCATION_FILTER_INVALID":                                           80,
		"CANNOT_ATTACH_CRITERIA_AT_CAMPAIGN_AND_ADGROUP":                    81,
		"HOTEL_LENGTH_OF_STAY_OVERLAPS_WITH_EXISTING_CRITERION":             82,
		"HOTEL_ADVANCE_BOOKING_WINDOW_OVERLAPS_WITH_EXISTING_CRITERION":     83,
		"FIELD_INCOMPATIBLE_WITH_NEGATIVE_TARGETING":                        84,
		"INVALID_WEBPAGE_CONDITION":                                         85,
		"INVALID_WEBPAGE_CONDITION_URL":                                     86,
		"WEBPAGE_CONDITION_URL_CANNOT_BE_EMPTY":                             87,
		"WEBPAGE_CONDITION_URL_UNSUPPORTED_PROTOCOL":                        88,
		"WEBPAGE_CONDITION_URL_CANNOT_BE_IP_ADDRESS":                        89,
		"WEBPAGE_CONDITION_URL_DOMAIN_NOT_CONSISTENT_WITH_CAMPAIGN_SETTING": 90,
		"WEBPAGE_CONDITION_URL_CANNOT_BE_PUBLIC_SUFFIX":                     91,
		"WEBPAGE_CONDITION_URL_INVALID_PUBLIC_SUFFIX":                       92,
		"WEBPAGE_CONDITION_URL_VALUE_TRACK_VALUE_NOT_SUPPORTED":             93,
		"WEBPAGE_CRITERION_URL_EQUALS_CAN_HAVE_ONLY_ONE_CONDITION":          94,
		"WEBPAGE_CRITERION_NOT_SUPPORTED_ON_NON_DSA_AD_GROUP":               95,
	}
)

func (x CriterionErrorEnum_CriterionError) Enum() *CriterionErrorEnum_CriterionError {
	p := new(CriterionErrorEnum_CriterionError)
	*p = x
	return p
}

func (x CriterionErrorEnum_CriterionError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CriterionErrorEnum_CriterionError) Descriptor() protoreflect.EnumDescriptor {
	return file_google_ads_googleads_v1_errors_criterion_error_proto_enumTypes[0].Descriptor()
}

func (CriterionErrorEnum_CriterionError) Type() protoreflect.EnumType {
	return &file_google_ads_googleads_v1_errors_criterion_error_proto_enumTypes[0]
}

func (x CriterionErrorEnum_CriterionError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CriterionErrorEnum_CriterionError.Descriptor instead.
func (CriterionErrorEnum_CriterionError) EnumDescriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_criterion_error_proto_rawDescGZIP(), []int{0, 0}
}

// Container for enum describing possible criterion errors.
type CriterionErrorEnum struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CriterionErrorEnum) Reset() {
	*x = CriterionErrorEnum{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_errors_criterion_error_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CriterionErrorEnum) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CriterionErrorEnum) ProtoMessage() {}

func (x *CriterionErrorEnum) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_errors_criterion_error_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CriterionErrorEnum.ProtoReflect.Descriptor instead.
func (*CriterionErrorEnum) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_criterion_error_proto_rawDescGZIP(), []int{0}
}

var File_google_ads_googleads_v1_errors_criterion_error_proto protoreflect.FileDescriptor

var file_google_ads_googleads_v1_errors_criterion_error_proto_rawDesc = []byte{
	0x0a, 0x34, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa8, 0x1d, 0x0a, 0x12, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69,
	0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x22, 0x91, 0x1d, 0x0a, 0x0e,
	0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x0f,
	0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16,
	0x43, 0x4f, 0x4e, 0x43, 0x52, 0x45, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x45, 0x58, 0x43, 0x4c, 0x55, 0x44, 0x45, 0x44, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x4b, 0x45, 0x59, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10,
	0x04, 0x12, 0x19, 0x0a, 0x15, 0x4b, 0x45, 0x59, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x54, 0x45, 0x58,
	0x54, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4c, 0x4f, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x1e, 0x0a, 0x1a,
	0x4b, 0x45, 0x59, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x48, 0x41, 0x53, 0x5f, 0x54, 0x4f, 0x4f, 0x5f,
	0x4d, 0x41, 0x4e, 0x59, 0x5f, 0x57, 0x4f, 0x52, 0x44, 0x53, 0x10, 0x06, 0x12, 0x1d, 0x0a, 0x19,
	0x4b, 0x45, 0x59, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x48, 0x41, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x53, 0x10, 0x07, 0x12, 0x19, 0x0a, 0x15, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x55, 0x52, 0x4c, 0x10, 0x08, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x09, 0x12, 0x19, 0x0a,
	0x15, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x10, 0x0a, 0x12, 0x24, 0x0a, 0x20, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x50,
	0x4c, 0x41, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x52, 0x4c, 0x10, 0x0b, 0x12, 0x1d,
	0x0a, 0x19, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x52, 0x4c, 0x5f,
	0x49, 0x53, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4c, 0x4f, 0x4e, 0x47, 0x10, 0x0c, 0x12, 0x22, 0x0a,
	0x1e, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x52, 0x4c, 0x5f, 0x48,
	0x41, 0x53, 0x5f, 0x49, 0x4c, 0x4c, 0x45, 0x47, 0x41, 0x4c, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x10,
	0x0d, 0x12, 0x2c, 0x0a, 0x28, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55,
	0x52, 0x4c, 0x5f, 0x48, 0x41, 0x53, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c, 0x45, 0x5f,
	0x53, 0x49, 0x54, 0x45, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x0e, 0x12,
	0x39, 0x0a, 0x35, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x53, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x52, 0x5f, 0x45,
	0x58, 0x43, 0x4c, 0x55, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x0f, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x4f, 0x50, 0x49, 0x43, 0x5f, 0x50, 0x41, 0x54, 0x48,
	0x10, 0x10, 0x12, 0x1e, 0x0a, 0x1a, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x59, 0x4f,
	0x55, 0x54, 0x55, 0x42, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x49, 0x44,
	0x10, 0x11, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x59, 0x4f,
	0x55, 0x54, 0x55, 0x42, 0x45, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x44, 0x10, 0x12,
	0x12, 0x27, 0x0a, 0x23, 0x59, 0x4f, 0x55, 0x54, 0x55, 0x42, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x54,
	0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x44, 0x45, 0x50,
	0x52, 0x45, 0x43, 0x41, 0x54, 0x45, 0x44, 0x10, 0x13, 0x12, 0x2a, 0x0a, 0x26, 0x59, 0x4f, 0x55,
	0x54, 0x55, 0x42, 0x45, 0x5f, 0x44, 0x45, 0x4d, 0x4f, 0x47, 0x52, 0x41, 0x50, 0x48, 0x49, 0x43,
	0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x44, 0x45, 0x50, 0x52, 0x45, 0x43, 0x41,
	0x54, 0x45, 0x44, 0x10, 0x14, 0x12, 0x1b, 0x0a, 0x17, 0x59, 0x4f, 0x55, 0x54, 0x55, 0x42, 0x45,
	0x5f, 0x55, 0x52, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44,
	0x10, 0x15, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x43,
	0x4c, 0x55, 0x44, 0x45, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x10, 0x16, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x41,
	0x44, 0x44, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x10, 0x17, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x52,
	0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52, 0x10, 0x18, 0x12, 0x1b,
	0x0a, 0x17, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52,
	0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4c, 0x4f, 0x4e, 0x47, 0x10, 0x19, 0x12, 0x24, 0x0a, 0x20, 0x43,
	0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x43, 0x4c, 0x55, 0x44, 0x45, 0x5f, 0x53, 0x49,
	0x4d, 0x49, 0x4c, 0x41, 0x52, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10,
	0x1a, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x5f,
	0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x53, 0x54,
	0x10, 0x1b, 0x12, 0x3a, 0x0a, 0x36, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x44, 0x44,
	0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x5f, 0x4c, 0x49,
	0x53, 0x54, 0x53, 0x5f, 0x54, 0x4f, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x4e,
	0x4c, 0x59, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x53, 0x10, 0x1c, 0x12, 0x35,
	0x0a, 0x31, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x44, 0x49, 0x53,
	0x50, 0x4c, 0x41, 0x59, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x53, 0x5f,
	0x54, 0x4f, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49,
	0x47, 0x4e, 0x53, 0x10, 0x1d, 0x12, 0x37, 0x0a, 0x33, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f,
	0x41, 0x44, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x4f, 0x4e, 0x4c, 0x59,
	0x5f, 0x4c, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x54, 0x4f, 0x5f, 0x53, 0x48, 0x4f, 0x50, 0x50, 0x49,
	0x4e, 0x47, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x53, 0x10, 0x1e, 0x12, 0x31,
	0x0a, 0x2d, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x53, 0x5f, 0x54, 0x4f, 0x5f, 0x53,
	0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x53, 0x10,
	0x1f, 0x12, 0x39, 0x0a, 0x35, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x5f,
	0x42, 0x49, 0x44, 0x53, 0x5f, 0x4f, 0x4e, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48,
	0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x53, 0x10, 0x20, 0x12, 0x37, 0x0a, 0x33,
	0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x55, 0x52, 0x4c, 0x53, 0x5f,
	0x54, 0x4f, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x10, 0x21, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x41, 0x46, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x59,
	0x10, 0x60, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x10, 0x61, 0x12, 0x16, 0x0a,
	0x12, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x49, 0x50, 0x5f, 0x41, 0x44, 0x44, 0x52,
	0x45, 0x53, 0x53, 0x10, 0x22, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x49, 0x50, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x10, 0x23, 0x12, 0x16, 0x0a, 0x12,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x41,
	0x50, 0x50, 0x10, 0x24, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x10, 0x25, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x26, 0x12,
	0x1b, 0x0a, 0x17, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54,
	0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e, 0x10, 0x27, 0x12, 0x24, 0x0a, 0x20,
	0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x4f, 0x42,
	0x53, 0x4f, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e,
	0x10, 0x28, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e, 0x5f,
	0x49, 0x44, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d,
	0x41, 0x54, 0x43, 0x48, 0x10, 0x29, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x50, 0x52, 0x4f, 0x58, 0x49, 0x4d, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x41, 0x44, 0x49,
	0x55, 0x53, 0x10, 0x2a, 0x12, 0x22, 0x0a, 0x1e, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x50, 0x52, 0x4f, 0x58, 0x49, 0x4d, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x41, 0x44, 0x49, 0x55, 0x53,
	0x5f, 0x55, 0x4e, 0x49, 0x54, 0x53, 0x10, 0x2b, 0x12, 0x20, 0x0a, 0x1c, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x45, 0x54, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53,
	0x53, 0x5f, 0x4c, 0x45, 0x4e, 0x47, 0x54, 0x48, 0x10, 0x2c, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43, 0x49, 0x54, 0x59, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4c,
	0x45, 0x4e, 0x47, 0x54, 0x48, 0x10, 0x2d, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x4f, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4c, 0x45,
	0x4e, 0x47, 0x54, 0x48, 0x10, 0x2e, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4c, 0x45, 0x4e,
	0x47, 0x54, 0x48, 0x10, 0x2f, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x50, 0x4f, 0x53, 0x54, 0x41, 0x4c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4c, 0x45, 0x4e, 0x47,
	0x54, 0x48, 0x10, 0x30, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x31, 0x12, 0x14,
	0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4c, 0x41, 0x54, 0x49, 0x54, 0x55,
	0x44, 0x45, 0x10, 0x32, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x4c, 0x4f, 0x4e, 0x47, 0x49, 0x54, 0x55, 0x44, 0x45, 0x10, 0x33, 0x12, 0x36, 0x0a, 0x32, 0x50,
	0x52, 0x4f, 0x58, 0x49, 0x4d, 0x49, 0x54, 0x59, 0x5f, 0x47, 0x45, 0x4f, 0x50, 0x4f, 0x49, 0x4e,
	0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x42, 0x4f,
	0x54, 0x48, 0x5f, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x42, 0x45, 0x5f, 0x4e, 0x55, 0x4c,
	0x4c, 0x10, 0x34, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50,
	0x52, 0x4f, 0x58, 0x49, 0x4d, 0x49, 0x54, 0x59, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53,
	0x10, 0x35, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x44, 0x4f, 0x4d, 0x41, 0x49, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x36,
	0x12, 0x20, 0x0a, 0x1c, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x41,
	0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4c, 0x4f, 0x4e, 0x47,
	0x10, 0x37, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x44, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c,
	0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x41, 0x4c, 0x53,
	0x5f, 0x4f, 0x56, 0x45, 0x52, 0x4c, 0x41, 0x50, 0x10, 0x38, 0x12, 0x32, 0x0a, 0x2e, 0x41, 0x44,
	0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56,
	0x41, 0x4c, 0x5f, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x50, 0x41, 0x4e, 0x5f, 0x4d,
	0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c, 0x45, 0x5f, 0x44, 0x41, 0x59, 0x53, 0x10, 0x39, 0x12, 0x25,
	0x0a, 0x21, 0x41, 0x44, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x56, 0x41, 0x4c, 0x10, 0x3a, 0x12, 0x30, 0x0a, 0x2c, 0x41, 0x44, 0x5f, 0x53, 0x43, 0x48, 0x45,
	0x44, 0x55, 0x4c, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x56, 0x41, 0x4c, 0x53, 0x5f, 0x50, 0x45, 0x52, 0x5f, 0x44, 0x41, 0x59, 0x5f,
	0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x3b, 0x12, 0x2f, 0x0a, 0x2b, 0x41, 0x44, 0x5f, 0x53, 0x43,
	0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e,
	0x5f, 0x49, 0x44, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x49, 0x4e, 0x47, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x53, 0x10, 0x3c, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x41, 0x4e, 0x4e,
	0x4f, 0x54, 0x5f, 0x42, 0x49, 0x44, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x5f, 0x43, 0x52,
	0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x3d, 0x12, 0x32,
	0x0a, 0x2e, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x42, 0x49, 0x44, 0x5f, 0x4d, 0x4f, 0x44,
	0x49, 0x46, 0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41,
	0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x5f, 0x4f, 0x50, 0x54, 0x45, 0x44, 0x5f, 0x4f, 0x55, 0x54,
	0x10, 0x3e, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x42, 0x49, 0x44,
	0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x5f, 0x4e, 0x45, 0x47, 0x41, 0x54, 0x49, 0x56, 0x45,
	0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e, 0x10, 0x3f, 0x12, 0x1f, 0x0a, 0x1b,
	0x42, 0x49, 0x44, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x41, 0x4c, 0x52,
	0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x40, 0x12, 0x17, 0x0a,
	0x13, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x49, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c,
	0x4f, 0x57, 0x45, 0x44, 0x10, 0x41, 0x12, 0x28, 0x0a, 0x24, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x49, 0x4e, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x4f, 0x52,
	0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x42,
	0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x42, 0x49,
	0x44, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x10, 0x43,
	0x12, 0x1c, 0x0a, 0x18, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x43, 0x4c, 0x55,
	0x44, 0x45, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e, 0x10, 0x44, 0x12, 0x1b,
	0x0a, 0x17, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x4d, 0x4f, 0x56, 0x45, 0x5f,
	0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e, 0x10, 0x45, 0x12, 0x1a, 0x0a, 0x16, 0x50,
	0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x50, 0x45, 0x5f, 0x54, 0x4f, 0x4f,
	0x5f, 0x4c, 0x4f, 0x4e, 0x47, 0x10, 0x46, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x52, 0x4f, 0x44, 0x55,
	0x43, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x50, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4d, 0x41, 0x4e,
	0x59, 0x5f, 0x44, 0x49, 0x4d, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x47, 0x12, 0x1e,
	0x0a, 0x1a, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4c, 0x4f, 0x4e, 0x47, 0x10, 0x48, 0x12, 0x29,
	0x0a, 0x25, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x59, 0x5f, 0x44, 0x49, 0x4d,
	0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x49, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x44, 0x49, 0x4d,
	0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x4a, 0x12, 0x22, 0x0a, 0x1e, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x44, 0x49, 0x4d, 0x45,
	0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x4b, 0x12, 0x24, 0x0a, 0x20,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f,
	0x42, 0x49, 0x44, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x10, 0x4c, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x48,
	0x4f, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x4d,
	0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x55, 0x4e, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x4e, 0x12,
	0x1f, 0x0a, 0x1b, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x4c, 0x54,
	0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x4f,
	0x12, 0x24, 0x0a, 0x20, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x46, 0x45, 0x45, 0x44,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49,
	0x4c, 0x54, 0x45, 0x52, 0x10, 0x62, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x10, 0x50, 0x12, 0x32, 0x0a, 0x2e, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x54,
	0x54, 0x41, 0x43, 0x48, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x41, 0x54,
	0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x41, 0x44,
	0x47, 0x52, 0x4f, 0x55, 0x50, 0x10, 0x51, 0x12, 0x39, 0x0a, 0x35, 0x48, 0x4f, 0x54, 0x45, 0x4c,
	0x5f, 0x4c, 0x45, 0x4e, 0x47, 0x54, 0x48, 0x5f, 0x4f, 0x46, 0x5f, 0x53, 0x54, 0x41, 0x59, 0x5f,
	0x4f, 0x56, 0x45, 0x52, 0x4c, 0x41, 0x50, 0x53, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x45, 0x58,
	0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e,
	0x10, 0x52, 0x12, 0x41, 0x0a, 0x3d, 0x48, 0x4f, 0x54, 0x45, 0x4c, 0x5f, 0x41, 0x44, 0x56, 0x41,
	0x4e, 0x43, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x49, 0x4e, 0x44,
	0x4f, 0x57, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x4c, 0x41, 0x50, 0x53, 0x5f, 0x57, 0x49, 0x54, 0x48,
	0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52,
	0x49, 0x4f, 0x4e, 0x10, 0x53, 0x12, 0x2e, 0x0a, 0x2a, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x49,
	0x4e, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x54, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x57, 0x49, 0x54, 0x48,
	0x5f, 0x4e, 0x45, 0x47, 0x41, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54,
	0x49, 0x4e, 0x47, 0x10, 0x54, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x57, 0x45, 0x42, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x55, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x57, 0x45, 0x42, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x55, 0x52, 0x4c, 0x10, 0x56, 0x12, 0x29, 0x0a, 0x25, 0x57, 0x45, 0x42, 0x50, 0x41,
	0x47, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x52, 0x4c,
	0x5f, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x42, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59,
	0x10, 0x57, 0x12, 0x2e, 0x0a, 0x2a, 0x57, 0x45, 0x42, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x4f,
	0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x52, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x55,
	0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c,
	0x10, 0x58, 0x12, 0x2e, 0x0a, 0x2a, 0x57, 0x45, 0x42, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x4f,
	0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x52, 0x4c, 0x5f, 0x43, 0x41, 0x4e, 0x4e,
	0x4f, 0x54, 0x5f, 0x42, 0x45, 0x5f, 0x49, 0x50, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53,
	0x10, 0x59, 0x12, 0x45, 0x0a, 0x41, 0x57, 0x45, 0x42, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x4f,
	0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x52, 0x4c, 0x5f, 0x44, 0x4f, 0x4d, 0x41,
	0x49, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x49, 0x53, 0x54, 0x45, 0x4e,
	0x54, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x5f,
	0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x5a, 0x12, 0x31, 0x0a, 0x2d, 0x57, 0x45, 0x42,
	0x50, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55,
	0x52, 0x4c, 0x5f, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x42, 0x45, 0x5f, 0x50, 0x55, 0x42,
	0x4c, 0x49, 0x43, 0x5f, 0x53, 0x55, 0x46, 0x46, 0x49, 0x58, 0x10, 0x5b, 0x12, 0x2f, 0x0a, 0x2b,
	0x57, 0x45, 0x42, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x55, 0x52, 0x4c, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x55,
	0x42, 0x4c, 0x49, 0x43, 0x5f, 0x53, 0x55, 0x46, 0x46, 0x49, 0x58, 0x10, 0x5c, 0x12, 0x39, 0x0a,
	0x35, 0x57, 0x45, 0x42, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x55, 0x52, 0x4c, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x54, 0x52, 0x41,
	0x43, 0x4b, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x55, 0x50,
	0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x10, 0x5d, 0x12, 0x3c, 0x0a, 0x38, 0x57, 0x45, 0x42, 0x50,
	0x41, 0x47, 0x45, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x52,
	0x4c, 0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x53, 0x5f, 0x43, 0x41, 0x4e, 0x5f, 0x48, 0x41, 0x56,
	0x45, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x5f, 0x4f, 0x4e, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x5e, 0x12, 0x37, 0x0a, 0x33, 0x57, 0x45, 0x42, 0x50, 0x41, 0x47,
	0x45, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x5f, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x4e,
	0x5f, 0x44, 0x53, 0x41, 0x5f, 0x41, 0x44, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10, 0x5f, 0x42,
	0xee, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x42, 0x13, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x44, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67,
	0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x3b, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0xa2, 0x02, 0x03, 0x47, 0x41, 0x41, 0xaa, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x41, 0x64, 0x73, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73,
	0x2e, 0x56, 0x31, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0xca, 0x02, 0x1e, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x5c, 0x41, 0x64, 0x73, 0x5c, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64,
	0x73, 0x5c, 0x56, 0x31, 0x5c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0xea, 0x02, 0x22, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x41, 0x64, 0x73, 0x3a, 0x3a, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x41, 0x64, 0x73, 0x3a, 0x3a, 0x56, 0x31, 0x3a, 0x3a, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_ads_googleads_v1_errors_criterion_error_proto_rawDescOnce sync.Once
	file_google_ads_googleads_v1_errors_criterion_error_proto_rawDescData = file_google_ads_googleads_v1_errors_criterion_error_proto_rawDesc
)

func file_google_ads_googleads_v1_errors_criterion_error_proto_rawDescGZIP() []byte {
	file_google_ads_googleads_v1_errors_criterion_error_proto_rawDescOnce.Do(func() {
		file_google_ads_googleads_v1_errors_criterion_error_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_ads_googleads_v1_errors_criterion_error_proto_rawDescData)
	})
	return file_google_ads_googleads_v1_errors_criterion_error_proto_rawDescData
}

var file_google_ads_googleads_v1_errors_criterion_error_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_google_ads_googleads_v1_errors_criterion_error_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_google_ads_googleads_v1_errors_criterion_error_proto_goTypes = []interface{}{
	(CriterionErrorEnum_CriterionError)(0), // 0: google.ads.googleads.v1.errors.CriterionErrorEnum.CriterionError
	(*CriterionErrorEnum)(nil),             // 1: google.ads.googleads.v1.errors.CriterionErrorEnum
}
var file_google_ads_googleads_v1_errors_criterion_error_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_google_ads_googleads_v1_errors_criterion_error_proto_init() }
func file_google_ads_googleads_v1_errors_criterion_error_proto_init() {
	if File_google_ads_googleads_v1_errors_criterion_error_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_ads_googleads_v1_errors_criterion_error_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CriterionErrorEnum); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_ads_googleads_v1_errors_criterion_error_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_ads_googleads_v1_errors_criterion_error_proto_goTypes,
		DependencyIndexes: file_google_ads_googleads_v1_errors_criterion_error_proto_depIdxs,
		EnumInfos:         file_google_ads_googleads_v1_errors_criterion_error_proto_enumTypes,
		MessageInfos:      file_google_ads_googleads_v1_errors_criterion_error_proto_msgTypes,
	}.Build()
	File_google_ads_googleads_v1_errors_criterion_error_proto = out.File
	file_google_ads_googleads_v1_errors_criterion_error_proto_rawDesc = nil
	file_google_ads_googleads_v1_errors_criterion_error_proto_goTypes = nil
	file_google_ads_googleads_v1_errors_criterion_error_proto_depIdxs = nil
}
