// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.22.0
// 	protoc        v3.11.2
// source: google/ads/googleads/v1/errors/errors.proto

package errors

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	common "google.golang.org/genproto/googleapis/ads/googleads/v1/common"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// Describes how a GoogleAds API call failed. It's returned inside
// google.rpc.Status.details when a call fails.
type GoogleAdsFailure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of errors that occurred.
	Errors []*GoogleAdsError `protobuf:"bytes,1,rep,name=errors,proto3" json:"errors,omitempty"`
}

func (x *GoogleAdsFailure) Reset() {
	*x = GoogleAdsFailure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoogleAdsFailure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoogleAdsFailure) ProtoMessage() {}

func (x *GoogleAdsFailure) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoogleAdsFailure.ProtoReflect.Descriptor instead.
func (*GoogleAdsFailure) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_errors_proto_rawDescGZIP(), []int{0}
}

func (x *GoogleAdsFailure) GetErrors() []*GoogleAdsError {
	if x != nil {
		return x.Errors
	}
	return nil
}

// GoogleAds-specific error.
type GoogleAdsError struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// An enum value that indicates which error occurred.
	ErrorCode *ErrorCode `protobuf:"bytes,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	// A human-readable description of the error.
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// The value that triggered the error.
	Trigger *common.Value `protobuf:"bytes,3,opt,name=trigger,proto3" json:"trigger,omitempty"`
	// Describes the part of the request proto that caused the error.
	Location *ErrorLocation `protobuf:"bytes,4,opt,name=location,proto3" json:"location,omitempty"`
	// Additional error details, which are returned by certain error codes. Most
	// error codes do not include details.
	Details *ErrorDetails `protobuf:"bytes,5,opt,name=details,proto3" json:"details,omitempty"`
}

func (x *GoogleAdsError) Reset() {
	*x = GoogleAdsError{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoogleAdsError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoogleAdsError) ProtoMessage() {}

func (x *GoogleAdsError) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoogleAdsError.ProtoReflect.Descriptor instead.
func (*GoogleAdsError) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_errors_proto_rawDescGZIP(), []int{1}
}

func (x *GoogleAdsError) GetErrorCode() *ErrorCode {
	if x != nil {
		return x.ErrorCode
	}
	return nil
}

func (x *GoogleAdsError) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GoogleAdsError) GetTrigger() *common.Value {
	if x != nil {
		return x.Trigger
	}
	return nil
}

func (x *GoogleAdsError) GetLocation() *ErrorLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *GoogleAdsError) GetDetails() *ErrorDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

// The error reason represented by type and enum.
type ErrorCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of error enums
	//
	// Types that are assignable to ErrorCode:
	//	*ErrorCode_RequestError
	//	*ErrorCode_BiddingStrategyError
	//	*ErrorCode_UrlFieldError
	//	*ErrorCode_ListOperationError
	//	*ErrorCode_QueryError
	//	*ErrorCode_MutateError
	//	*ErrorCode_FieldMaskError
	//	*ErrorCode_AuthorizationError
	//	*ErrorCode_InternalError
	//	*ErrorCode_QuotaError
	//	*ErrorCode_AdError
	//	*ErrorCode_AdGroupError
	//	*ErrorCode_CampaignBudgetError
	//	*ErrorCode_CampaignError
	//	*ErrorCode_AuthenticationError
	//	*ErrorCode_AdGroupCriterionError
	//	*ErrorCode_AdCustomizerError
	//	*ErrorCode_AdGroupAdError
	//	*ErrorCode_AdSharingError
	//	*ErrorCode_AdxError
	//	*ErrorCode_AssetError
	//	*ErrorCode_BiddingError
	//	*ErrorCode_CampaignCriterionError
	//	*ErrorCode_CollectionSizeError
	//	*ErrorCode_CountryCodeError
	//	*ErrorCode_CriterionError
	//	*ErrorCode_CustomerError
	//	*ErrorCode_DateError
	//	*ErrorCode_DateRangeError
	//	*ErrorCode_DistinctError
	//	*ErrorCode_FeedAttributeReferenceError
	//	*ErrorCode_FunctionError
	//	*ErrorCode_FunctionParsingError
	//	*ErrorCode_IdError
	//	*ErrorCode_ImageError
	//	*ErrorCode_LanguageCodeError
	//	*ErrorCode_MediaBundleError
	//	*ErrorCode_MediaUploadError
	//	*ErrorCode_MediaFileError
	//	*ErrorCode_MultiplierError
	//	*ErrorCode_NewResourceCreationError
	//	*ErrorCode_NotEmptyError
	//	*ErrorCode_NullError
	//	*ErrorCode_OperatorError
	//	*ErrorCode_RangeError
	//	*ErrorCode_RecommendationError
	//	*ErrorCode_RegionCodeError
	//	*ErrorCode_SettingError
	//	*ErrorCode_StringFormatError
	//	*ErrorCode_StringLengthError
	//	*ErrorCode_OperationAccessDeniedError
	//	*ErrorCode_ResourceAccessDeniedError
	//	*ErrorCode_ResourceCountLimitExceededError
	//	*ErrorCode_YoutubeVideoRegistrationError
	//	*ErrorCode_AdGroupBidModifierError
	//	*ErrorCode_ContextError
	//	*ErrorCode_FieldError
	//	*ErrorCode_SharedSetError
	//	*ErrorCode_SharedCriterionError
	//	*ErrorCode_CampaignSharedSetError
	//	*ErrorCode_ConversionActionError
	//	*ErrorCode_ConversionAdjustmentUploadError
	//	*ErrorCode_ConversionUploadError
	//	*ErrorCode_HeaderError
	//	*ErrorCode_DatabaseError
	//	*ErrorCode_PolicyFindingError
	//	*ErrorCode_EnumError
	//	*ErrorCode_KeywordPlanError
	//	*ErrorCode_KeywordPlanCampaignError
	//	*ErrorCode_KeywordPlanNegativeKeywordError
	//	*ErrorCode_KeywordPlanAdGroupError
	//	*ErrorCode_KeywordPlanKeywordError
	//	*ErrorCode_KeywordPlanIdeaError
	//	*ErrorCode_AccountBudgetProposalError
	//	*ErrorCode_UserListError
	//	*ErrorCode_ChangeStatusError
	//	*ErrorCode_FeedError
	//	*ErrorCode_GeoTargetConstantSuggestionError
	//	*ErrorCode_CampaignDraftError
	//	*ErrorCode_FeedItemError
	//	*ErrorCode_LabelError
	//	*ErrorCode_BillingSetupError
	//	*ErrorCode_CustomerClientLinkError
	//	*ErrorCode_CustomerManagerLinkError
	//	*ErrorCode_FeedMappingError
	//	*ErrorCode_CustomerFeedError
	//	*ErrorCode_AdGroupFeedError
	//	*ErrorCode_CampaignFeedError
	//	*ErrorCode_CustomInterestError
	//	*ErrorCode_CampaignExperimentError
	//	*ErrorCode_ExtensionFeedItemError
	//	*ErrorCode_AdParameterError
	//	*ErrorCode_FeedItemValidationError
	//	*ErrorCode_ExtensionSettingError
	//	*ErrorCode_FeedItemTargetError
	//	*ErrorCode_PolicyViolationError
	//	*ErrorCode_MutateJobError
	//	*ErrorCode_PartialFailureError
	//	*ErrorCode_PolicyValidationParameterError
	//	*ErrorCode_SizeLimitError
	//	*ErrorCode_NotWhitelistedError
	//	*ErrorCode_ManagerLinkError
	ErrorCode isErrorCode_ErrorCode `protobuf_oneof:"error_code"`
}

func (x *ErrorCode) Reset() {
	*x = ErrorCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErrorCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorCode) ProtoMessage() {}

func (x *ErrorCode) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorCode.ProtoReflect.Descriptor instead.
func (*ErrorCode) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_errors_proto_rawDescGZIP(), []int{2}
}

func (m *ErrorCode) GetErrorCode() isErrorCode_ErrorCode {
	if m != nil {
		return m.ErrorCode
	}
	return nil
}

func (x *ErrorCode) GetRequestError() RequestErrorEnum_RequestError {
	if x, ok := x.GetErrorCode().(*ErrorCode_RequestError); ok {
		return x.RequestError
	}
	return RequestErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetBiddingStrategyError() BiddingStrategyErrorEnum_BiddingStrategyError {
	if x, ok := x.GetErrorCode().(*ErrorCode_BiddingStrategyError); ok {
		return x.BiddingStrategyError
	}
	return BiddingStrategyErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetUrlFieldError() UrlFieldErrorEnum_UrlFieldError {
	if x, ok := x.GetErrorCode().(*ErrorCode_UrlFieldError); ok {
		return x.UrlFieldError
	}
	return UrlFieldErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetListOperationError() ListOperationErrorEnum_ListOperationError {
	if x, ok := x.GetErrorCode().(*ErrorCode_ListOperationError); ok {
		return x.ListOperationError
	}
	return ListOperationErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetQueryError() QueryErrorEnum_QueryError {
	if x, ok := x.GetErrorCode().(*ErrorCode_QueryError); ok {
		return x.QueryError
	}
	return QueryErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetMutateError() MutateErrorEnum_MutateError {
	if x, ok := x.GetErrorCode().(*ErrorCode_MutateError); ok {
		return x.MutateError
	}
	return MutateErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetFieldMaskError() FieldMaskErrorEnum_FieldMaskError {
	if x, ok := x.GetErrorCode().(*ErrorCode_FieldMaskError); ok {
		return x.FieldMaskError
	}
	return FieldMaskErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetAuthorizationError() AuthorizationErrorEnum_AuthorizationError {
	if x, ok := x.GetErrorCode().(*ErrorCode_AuthorizationError); ok {
		return x.AuthorizationError
	}
	return AuthorizationErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetInternalError() InternalErrorEnum_InternalError {
	if x, ok := x.GetErrorCode().(*ErrorCode_InternalError); ok {
		return x.InternalError
	}
	return InternalErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetQuotaError() QuotaErrorEnum_QuotaError {
	if x, ok := x.GetErrorCode().(*ErrorCode_QuotaError); ok {
		return x.QuotaError
	}
	return QuotaErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetAdError() AdErrorEnum_AdError {
	if x, ok := x.GetErrorCode().(*ErrorCode_AdError); ok {
		return x.AdError
	}
	return AdErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetAdGroupError() AdGroupErrorEnum_AdGroupError {
	if x, ok := x.GetErrorCode().(*ErrorCode_AdGroupError); ok {
		return x.AdGroupError
	}
	return AdGroupErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCampaignBudgetError() CampaignBudgetErrorEnum_CampaignBudgetError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CampaignBudgetError); ok {
		return x.CampaignBudgetError
	}
	return CampaignBudgetErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCampaignError() CampaignErrorEnum_CampaignError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CampaignError); ok {
		return x.CampaignError
	}
	return CampaignErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetAuthenticationError() AuthenticationErrorEnum_AuthenticationError {
	if x, ok := x.GetErrorCode().(*ErrorCode_AuthenticationError); ok {
		return x.AuthenticationError
	}
	return AuthenticationErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetAdGroupCriterionError() AdGroupCriterionErrorEnum_AdGroupCriterionError {
	if x, ok := x.GetErrorCode().(*ErrorCode_AdGroupCriterionError); ok {
		return x.AdGroupCriterionError
	}
	return AdGroupCriterionErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetAdCustomizerError() AdCustomizerErrorEnum_AdCustomizerError {
	if x, ok := x.GetErrorCode().(*ErrorCode_AdCustomizerError); ok {
		return x.AdCustomizerError
	}
	return AdCustomizerErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetAdGroupAdError() AdGroupAdErrorEnum_AdGroupAdError {
	if x, ok := x.GetErrorCode().(*ErrorCode_AdGroupAdError); ok {
		return x.AdGroupAdError
	}
	return AdGroupAdErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetAdSharingError() AdSharingErrorEnum_AdSharingError {
	if x, ok := x.GetErrorCode().(*ErrorCode_AdSharingError); ok {
		return x.AdSharingError
	}
	return AdSharingErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetAdxError() AdxErrorEnum_AdxError {
	if x, ok := x.GetErrorCode().(*ErrorCode_AdxError); ok {
		return x.AdxError
	}
	return AdxErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetAssetError() AssetErrorEnum_AssetError {
	if x, ok := x.GetErrorCode().(*ErrorCode_AssetError); ok {
		return x.AssetError
	}
	return AssetErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetBiddingError() BiddingErrorEnum_BiddingError {
	if x, ok := x.GetErrorCode().(*ErrorCode_BiddingError); ok {
		return x.BiddingError
	}
	return BiddingErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCampaignCriterionError() CampaignCriterionErrorEnum_CampaignCriterionError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CampaignCriterionError); ok {
		return x.CampaignCriterionError
	}
	return CampaignCriterionErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCollectionSizeError() CollectionSizeErrorEnum_CollectionSizeError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CollectionSizeError); ok {
		return x.CollectionSizeError
	}
	return CollectionSizeErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCountryCodeError() CountryCodeErrorEnum_CountryCodeError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CountryCodeError); ok {
		return x.CountryCodeError
	}
	return CountryCodeErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCriterionError() CriterionErrorEnum_CriterionError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CriterionError); ok {
		return x.CriterionError
	}
	return CriterionErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCustomerError() CustomerErrorEnum_CustomerError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CustomerError); ok {
		return x.CustomerError
	}
	return CustomerErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetDateError() DateErrorEnum_DateError {
	if x, ok := x.GetErrorCode().(*ErrorCode_DateError); ok {
		return x.DateError
	}
	return DateErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetDateRangeError() DateRangeErrorEnum_DateRangeError {
	if x, ok := x.GetErrorCode().(*ErrorCode_DateRangeError); ok {
		return x.DateRangeError
	}
	return DateRangeErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetDistinctError() DistinctErrorEnum_DistinctError {
	if x, ok := x.GetErrorCode().(*ErrorCode_DistinctError); ok {
		return x.DistinctError
	}
	return DistinctErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetFeedAttributeReferenceError() FeedAttributeReferenceErrorEnum_FeedAttributeReferenceError {
	if x, ok := x.GetErrorCode().(*ErrorCode_FeedAttributeReferenceError); ok {
		return x.FeedAttributeReferenceError
	}
	return FeedAttributeReferenceErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetFunctionError() FunctionErrorEnum_FunctionError {
	if x, ok := x.GetErrorCode().(*ErrorCode_FunctionError); ok {
		return x.FunctionError
	}
	return FunctionErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetFunctionParsingError() FunctionParsingErrorEnum_FunctionParsingError {
	if x, ok := x.GetErrorCode().(*ErrorCode_FunctionParsingError); ok {
		return x.FunctionParsingError
	}
	return FunctionParsingErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetIdError() IdErrorEnum_IdError {
	if x, ok := x.GetErrorCode().(*ErrorCode_IdError); ok {
		return x.IdError
	}
	return IdErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetImageError() ImageErrorEnum_ImageError {
	if x, ok := x.GetErrorCode().(*ErrorCode_ImageError); ok {
		return x.ImageError
	}
	return ImageErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetLanguageCodeError() LanguageCodeErrorEnum_LanguageCodeError {
	if x, ok := x.GetErrorCode().(*ErrorCode_LanguageCodeError); ok {
		return x.LanguageCodeError
	}
	return LanguageCodeErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetMediaBundleError() MediaBundleErrorEnum_MediaBundleError {
	if x, ok := x.GetErrorCode().(*ErrorCode_MediaBundleError); ok {
		return x.MediaBundleError
	}
	return MediaBundleErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetMediaUploadError() MediaUploadErrorEnum_MediaUploadError {
	if x, ok := x.GetErrorCode().(*ErrorCode_MediaUploadError); ok {
		return x.MediaUploadError
	}
	return MediaUploadErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetMediaFileError() MediaFileErrorEnum_MediaFileError {
	if x, ok := x.GetErrorCode().(*ErrorCode_MediaFileError); ok {
		return x.MediaFileError
	}
	return MediaFileErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetMultiplierError() MultiplierErrorEnum_MultiplierError {
	if x, ok := x.GetErrorCode().(*ErrorCode_MultiplierError); ok {
		return x.MultiplierError
	}
	return MultiplierErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetNewResourceCreationError() NewResourceCreationErrorEnum_NewResourceCreationError {
	if x, ok := x.GetErrorCode().(*ErrorCode_NewResourceCreationError); ok {
		return x.NewResourceCreationError
	}
	return NewResourceCreationErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetNotEmptyError() NotEmptyErrorEnum_NotEmptyError {
	if x, ok := x.GetErrorCode().(*ErrorCode_NotEmptyError); ok {
		return x.NotEmptyError
	}
	return NotEmptyErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetNullError() NullErrorEnum_NullError {
	if x, ok := x.GetErrorCode().(*ErrorCode_NullError); ok {
		return x.NullError
	}
	return NullErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetOperatorError() OperatorErrorEnum_OperatorError {
	if x, ok := x.GetErrorCode().(*ErrorCode_OperatorError); ok {
		return x.OperatorError
	}
	return OperatorErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetRangeError() RangeErrorEnum_RangeError {
	if x, ok := x.GetErrorCode().(*ErrorCode_RangeError); ok {
		return x.RangeError
	}
	return RangeErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetRecommendationError() RecommendationErrorEnum_RecommendationError {
	if x, ok := x.GetErrorCode().(*ErrorCode_RecommendationError); ok {
		return x.RecommendationError
	}
	return RecommendationErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetRegionCodeError() RegionCodeErrorEnum_RegionCodeError {
	if x, ok := x.GetErrorCode().(*ErrorCode_RegionCodeError); ok {
		return x.RegionCodeError
	}
	return RegionCodeErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetSettingError() SettingErrorEnum_SettingError {
	if x, ok := x.GetErrorCode().(*ErrorCode_SettingError); ok {
		return x.SettingError
	}
	return SettingErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetStringFormatError() StringFormatErrorEnum_StringFormatError {
	if x, ok := x.GetErrorCode().(*ErrorCode_StringFormatError); ok {
		return x.StringFormatError
	}
	return StringFormatErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetStringLengthError() StringLengthErrorEnum_StringLengthError {
	if x, ok := x.GetErrorCode().(*ErrorCode_StringLengthError); ok {
		return x.StringLengthError
	}
	return StringLengthErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetOperationAccessDeniedError() OperationAccessDeniedErrorEnum_OperationAccessDeniedError {
	if x, ok := x.GetErrorCode().(*ErrorCode_OperationAccessDeniedError); ok {
		return x.OperationAccessDeniedError
	}
	return OperationAccessDeniedErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetResourceAccessDeniedError() ResourceAccessDeniedErrorEnum_ResourceAccessDeniedError {
	if x, ok := x.GetErrorCode().(*ErrorCode_ResourceAccessDeniedError); ok {
		return x.ResourceAccessDeniedError
	}
	return ResourceAccessDeniedErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetResourceCountLimitExceededError() ResourceCountLimitExceededErrorEnum_ResourceCountLimitExceededError {
	if x, ok := x.GetErrorCode().(*ErrorCode_ResourceCountLimitExceededError); ok {
		return x.ResourceCountLimitExceededError
	}
	return ResourceCountLimitExceededErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetYoutubeVideoRegistrationError() YoutubeVideoRegistrationErrorEnum_YoutubeVideoRegistrationError {
	if x, ok := x.GetErrorCode().(*ErrorCode_YoutubeVideoRegistrationError); ok {
		return x.YoutubeVideoRegistrationError
	}
	return YoutubeVideoRegistrationErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetAdGroupBidModifierError() AdGroupBidModifierErrorEnum_AdGroupBidModifierError {
	if x, ok := x.GetErrorCode().(*ErrorCode_AdGroupBidModifierError); ok {
		return x.AdGroupBidModifierError
	}
	return AdGroupBidModifierErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetContextError() ContextErrorEnum_ContextError {
	if x, ok := x.GetErrorCode().(*ErrorCode_ContextError); ok {
		return x.ContextError
	}
	return ContextErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetFieldError() FieldErrorEnum_FieldError {
	if x, ok := x.GetErrorCode().(*ErrorCode_FieldError); ok {
		return x.FieldError
	}
	return FieldErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetSharedSetError() SharedSetErrorEnum_SharedSetError {
	if x, ok := x.GetErrorCode().(*ErrorCode_SharedSetError); ok {
		return x.SharedSetError
	}
	return SharedSetErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetSharedCriterionError() SharedCriterionErrorEnum_SharedCriterionError {
	if x, ok := x.GetErrorCode().(*ErrorCode_SharedCriterionError); ok {
		return x.SharedCriterionError
	}
	return SharedCriterionErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCampaignSharedSetError() CampaignSharedSetErrorEnum_CampaignSharedSetError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CampaignSharedSetError); ok {
		return x.CampaignSharedSetError
	}
	return CampaignSharedSetErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetConversionActionError() ConversionActionErrorEnum_ConversionActionError {
	if x, ok := x.GetErrorCode().(*ErrorCode_ConversionActionError); ok {
		return x.ConversionActionError
	}
	return ConversionActionErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetConversionAdjustmentUploadError() ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError {
	if x, ok := x.GetErrorCode().(*ErrorCode_ConversionAdjustmentUploadError); ok {
		return x.ConversionAdjustmentUploadError
	}
	return ConversionAdjustmentUploadErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetConversionUploadError() ConversionUploadErrorEnum_ConversionUploadError {
	if x, ok := x.GetErrorCode().(*ErrorCode_ConversionUploadError); ok {
		return x.ConversionUploadError
	}
	return ConversionUploadErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetHeaderError() HeaderErrorEnum_HeaderError {
	if x, ok := x.GetErrorCode().(*ErrorCode_HeaderError); ok {
		return x.HeaderError
	}
	return HeaderErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetDatabaseError() DatabaseErrorEnum_DatabaseError {
	if x, ok := x.GetErrorCode().(*ErrorCode_DatabaseError); ok {
		return x.DatabaseError
	}
	return DatabaseErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetPolicyFindingError() PolicyFindingErrorEnum_PolicyFindingError {
	if x, ok := x.GetErrorCode().(*ErrorCode_PolicyFindingError); ok {
		return x.PolicyFindingError
	}
	return PolicyFindingErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetEnumError() EnumErrorEnum_EnumError {
	if x, ok := x.GetErrorCode().(*ErrorCode_EnumError); ok {
		return x.EnumError
	}
	return EnumErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetKeywordPlanError() KeywordPlanErrorEnum_KeywordPlanError {
	if x, ok := x.GetErrorCode().(*ErrorCode_KeywordPlanError); ok {
		return x.KeywordPlanError
	}
	return KeywordPlanErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetKeywordPlanCampaignError() KeywordPlanCampaignErrorEnum_KeywordPlanCampaignError {
	if x, ok := x.GetErrorCode().(*ErrorCode_KeywordPlanCampaignError); ok {
		return x.KeywordPlanCampaignError
	}
	return KeywordPlanCampaignErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetKeywordPlanNegativeKeywordError() KeywordPlanNegativeKeywordErrorEnum_KeywordPlanNegativeKeywordError {
	if x, ok := x.GetErrorCode().(*ErrorCode_KeywordPlanNegativeKeywordError); ok {
		return x.KeywordPlanNegativeKeywordError
	}
	return KeywordPlanNegativeKeywordErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetKeywordPlanAdGroupError() KeywordPlanAdGroupErrorEnum_KeywordPlanAdGroupError {
	if x, ok := x.GetErrorCode().(*ErrorCode_KeywordPlanAdGroupError); ok {
		return x.KeywordPlanAdGroupError
	}
	return KeywordPlanAdGroupErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetKeywordPlanKeywordError() KeywordPlanKeywordErrorEnum_KeywordPlanKeywordError {
	if x, ok := x.GetErrorCode().(*ErrorCode_KeywordPlanKeywordError); ok {
		return x.KeywordPlanKeywordError
	}
	return KeywordPlanKeywordErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetKeywordPlanIdeaError() KeywordPlanIdeaErrorEnum_KeywordPlanIdeaError {
	if x, ok := x.GetErrorCode().(*ErrorCode_KeywordPlanIdeaError); ok {
		return x.KeywordPlanIdeaError
	}
	return KeywordPlanIdeaErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetAccountBudgetProposalError() AccountBudgetProposalErrorEnum_AccountBudgetProposalError {
	if x, ok := x.GetErrorCode().(*ErrorCode_AccountBudgetProposalError); ok {
		return x.AccountBudgetProposalError
	}
	return AccountBudgetProposalErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetUserListError() UserListErrorEnum_UserListError {
	if x, ok := x.GetErrorCode().(*ErrorCode_UserListError); ok {
		return x.UserListError
	}
	return UserListErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetChangeStatusError() ChangeStatusErrorEnum_ChangeStatusError {
	if x, ok := x.GetErrorCode().(*ErrorCode_ChangeStatusError); ok {
		return x.ChangeStatusError
	}
	return ChangeStatusErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetFeedError() FeedErrorEnum_FeedError {
	if x, ok := x.GetErrorCode().(*ErrorCode_FeedError); ok {
		return x.FeedError
	}
	return FeedErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetGeoTargetConstantSuggestionError() GeoTargetConstantSuggestionErrorEnum_GeoTargetConstantSuggestionError {
	if x, ok := x.GetErrorCode().(*ErrorCode_GeoTargetConstantSuggestionError); ok {
		return x.GeoTargetConstantSuggestionError
	}
	return GeoTargetConstantSuggestionErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCampaignDraftError() CampaignDraftErrorEnum_CampaignDraftError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CampaignDraftError); ok {
		return x.CampaignDraftError
	}
	return CampaignDraftErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetFeedItemError() FeedItemErrorEnum_FeedItemError {
	if x, ok := x.GetErrorCode().(*ErrorCode_FeedItemError); ok {
		return x.FeedItemError
	}
	return FeedItemErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetLabelError() LabelErrorEnum_LabelError {
	if x, ok := x.GetErrorCode().(*ErrorCode_LabelError); ok {
		return x.LabelError
	}
	return LabelErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetBillingSetupError() BillingSetupErrorEnum_BillingSetupError {
	if x, ok := x.GetErrorCode().(*ErrorCode_BillingSetupError); ok {
		return x.BillingSetupError
	}
	return BillingSetupErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCustomerClientLinkError() CustomerClientLinkErrorEnum_CustomerClientLinkError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CustomerClientLinkError); ok {
		return x.CustomerClientLinkError
	}
	return CustomerClientLinkErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCustomerManagerLinkError() CustomerManagerLinkErrorEnum_CustomerManagerLinkError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CustomerManagerLinkError); ok {
		return x.CustomerManagerLinkError
	}
	return CustomerManagerLinkErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetFeedMappingError() FeedMappingErrorEnum_FeedMappingError {
	if x, ok := x.GetErrorCode().(*ErrorCode_FeedMappingError); ok {
		return x.FeedMappingError
	}
	return FeedMappingErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCustomerFeedError() CustomerFeedErrorEnum_CustomerFeedError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CustomerFeedError); ok {
		return x.CustomerFeedError
	}
	return CustomerFeedErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetAdGroupFeedError() AdGroupFeedErrorEnum_AdGroupFeedError {
	if x, ok := x.GetErrorCode().(*ErrorCode_AdGroupFeedError); ok {
		return x.AdGroupFeedError
	}
	return AdGroupFeedErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCampaignFeedError() CampaignFeedErrorEnum_CampaignFeedError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CampaignFeedError); ok {
		return x.CampaignFeedError
	}
	return CampaignFeedErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCustomInterestError() CustomInterestErrorEnum_CustomInterestError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CustomInterestError); ok {
		return x.CustomInterestError
	}
	return CustomInterestErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetCampaignExperimentError() CampaignExperimentErrorEnum_CampaignExperimentError {
	if x, ok := x.GetErrorCode().(*ErrorCode_CampaignExperimentError); ok {
		return x.CampaignExperimentError
	}
	return CampaignExperimentErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetExtensionFeedItemError() ExtensionFeedItemErrorEnum_ExtensionFeedItemError {
	if x, ok := x.GetErrorCode().(*ErrorCode_ExtensionFeedItemError); ok {
		return x.ExtensionFeedItemError
	}
	return ExtensionFeedItemErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetAdParameterError() AdParameterErrorEnum_AdParameterError {
	if x, ok := x.GetErrorCode().(*ErrorCode_AdParameterError); ok {
		return x.AdParameterError
	}
	return AdParameterErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetFeedItemValidationError() FeedItemValidationErrorEnum_FeedItemValidationError {
	if x, ok := x.GetErrorCode().(*ErrorCode_FeedItemValidationError); ok {
		return x.FeedItemValidationError
	}
	return FeedItemValidationErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetExtensionSettingError() ExtensionSettingErrorEnum_ExtensionSettingError {
	if x, ok := x.GetErrorCode().(*ErrorCode_ExtensionSettingError); ok {
		return x.ExtensionSettingError
	}
	return ExtensionSettingErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetFeedItemTargetError() FeedItemTargetErrorEnum_FeedItemTargetError {
	if x, ok := x.GetErrorCode().(*ErrorCode_FeedItemTargetError); ok {
		return x.FeedItemTargetError
	}
	return FeedItemTargetErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetPolicyViolationError() PolicyViolationErrorEnum_PolicyViolationError {
	if x, ok := x.GetErrorCode().(*ErrorCode_PolicyViolationError); ok {
		return x.PolicyViolationError
	}
	return PolicyViolationErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetMutateJobError() MutateJobErrorEnum_MutateJobError {
	if x, ok := x.GetErrorCode().(*ErrorCode_MutateJobError); ok {
		return x.MutateJobError
	}
	return MutateJobErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetPartialFailureError() PartialFailureErrorEnum_PartialFailureError {
	if x, ok := x.GetErrorCode().(*ErrorCode_PartialFailureError); ok {
		return x.PartialFailureError
	}
	return PartialFailureErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetPolicyValidationParameterError() PolicyValidationParameterErrorEnum_PolicyValidationParameterError {
	if x, ok := x.GetErrorCode().(*ErrorCode_PolicyValidationParameterError); ok {
		return x.PolicyValidationParameterError
	}
	return PolicyValidationParameterErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetSizeLimitError() SizeLimitErrorEnum_SizeLimitError {
	if x, ok := x.GetErrorCode().(*ErrorCode_SizeLimitError); ok {
		return x.SizeLimitError
	}
	return SizeLimitErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetNotWhitelistedError() NotWhitelistedErrorEnum_NotWhitelistedError {
	if x, ok := x.GetErrorCode().(*ErrorCode_NotWhitelistedError); ok {
		return x.NotWhitelistedError
	}
	return NotWhitelistedErrorEnum_UNSPECIFIED
}

func (x *ErrorCode) GetManagerLinkError() ManagerLinkErrorEnum_ManagerLinkError {
	if x, ok := x.GetErrorCode().(*ErrorCode_ManagerLinkError); ok {
		return x.ManagerLinkError
	}
	return ManagerLinkErrorEnum_UNSPECIFIED
}

type isErrorCode_ErrorCode interface {
	isErrorCode_ErrorCode()
}

type ErrorCode_RequestError struct {
	// An error caused by the request
	RequestError RequestErrorEnum_RequestError `protobuf:"varint,1,opt,name=request_error,json=requestError,proto3,enum=google.ads.googleads.v1.errors.RequestErrorEnum_RequestError,oneof"`
}

type ErrorCode_BiddingStrategyError struct {
	// An error with a Bidding Strategy mutate.
	BiddingStrategyError BiddingStrategyErrorEnum_BiddingStrategyError `protobuf:"varint,2,opt,name=bidding_strategy_error,json=biddingStrategyError,proto3,enum=google.ads.googleads.v1.errors.BiddingStrategyErrorEnum_BiddingStrategyError,oneof"`
}

type ErrorCode_UrlFieldError struct {
	// An error with a URL field mutate.
	UrlFieldError UrlFieldErrorEnum_UrlFieldError `protobuf:"varint,3,opt,name=url_field_error,json=urlFieldError,proto3,enum=google.ads.googleads.v1.errors.UrlFieldErrorEnum_UrlFieldError,oneof"`
}

type ErrorCode_ListOperationError struct {
	// An error with a list operation.
	ListOperationError ListOperationErrorEnum_ListOperationError `protobuf:"varint,4,opt,name=list_operation_error,json=listOperationError,proto3,enum=google.ads.googleads.v1.errors.ListOperationErrorEnum_ListOperationError,oneof"`
}

type ErrorCode_QueryError struct {
	// An error with an AWQL query
	QueryError QueryErrorEnum_QueryError `protobuf:"varint,5,opt,name=query_error,json=queryError,proto3,enum=google.ads.googleads.v1.errors.QueryErrorEnum_QueryError,oneof"`
}

type ErrorCode_MutateError struct {
	// An error with a mutate
	MutateError MutateErrorEnum_MutateError `protobuf:"varint,7,opt,name=mutate_error,json=mutateError,proto3,enum=google.ads.googleads.v1.errors.MutateErrorEnum_MutateError,oneof"`
}

type ErrorCode_FieldMaskError struct {
	// An error with a field mask
	FieldMaskError FieldMaskErrorEnum_FieldMaskError `protobuf:"varint,8,opt,name=field_mask_error,json=fieldMaskError,proto3,enum=google.ads.googleads.v1.errors.FieldMaskErrorEnum_FieldMaskError,oneof"`
}

type ErrorCode_AuthorizationError struct {
	// An error encountered when trying to authorize a user.
	AuthorizationError AuthorizationErrorEnum_AuthorizationError `protobuf:"varint,9,opt,name=authorization_error,json=authorizationError,proto3,enum=google.ads.googleads.v1.errors.AuthorizationErrorEnum_AuthorizationError,oneof"`
}

type ErrorCode_InternalError struct {
	// An unexpected server-side error.
	InternalError InternalErrorEnum_InternalError `protobuf:"varint,10,opt,name=internal_error,json=internalError,proto3,enum=google.ads.googleads.v1.errors.InternalErrorEnum_InternalError,oneof"`
}

type ErrorCode_QuotaError struct {
	// An error with the amonut of quota remaining.
	QuotaError QuotaErrorEnum_QuotaError `protobuf:"varint,11,opt,name=quota_error,json=quotaError,proto3,enum=google.ads.googleads.v1.errors.QuotaErrorEnum_QuotaError,oneof"`
}

type ErrorCode_AdError struct {
	// An error with an Ad Group Ad mutate.
	AdError AdErrorEnum_AdError `protobuf:"varint,12,opt,name=ad_error,json=adError,proto3,enum=google.ads.googleads.v1.errors.AdErrorEnum_AdError,oneof"`
}

type ErrorCode_AdGroupError struct {
	// An error with an Ad Group mutate.
	AdGroupError AdGroupErrorEnum_AdGroupError `protobuf:"varint,13,opt,name=ad_group_error,json=adGroupError,proto3,enum=google.ads.googleads.v1.errors.AdGroupErrorEnum_AdGroupError,oneof"`
}

type ErrorCode_CampaignBudgetError struct {
	// An error with a Campaign Budget mutate.
	CampaignBudgetError CampaignBudgetErrorEnum_CampaignBudgetError `protobuf:"varint,14,opt,name=campaign_budget_error,json=campaignBudgetError,proto3,enum=google.ads.googleads.v1.errors.CampaignBudgetErrorEnum_CampaignBudgetError,oneof"`
}

type ErrorCode_CampaignError struct {
	// An error with a Campaign mutate.
	CampaignError CampaignErrorEnum_CampaignError `protobuf:"varint,15,opt,name=campaign_error,json=campaignError,proto3,enum=google.ads.googleads.v1.errors.CampaignErrorEnum_CampaignError,oneof"`
}

type ErrorCode_AuthenticationError struct {
	// Indicates failure to properly authenticate user.
	AuthenticationError AuthenticationErrorEnum_AuthenticationError `protobuf:"varint,17,opt,name=authentication_error,json=authenticationError,proto3,enum=google.ads.googleads.v1.errors.AuthenticationErrorEnum_AuthenticationError,oneof"`
}

type ErrorCode_AdGroupCriterionError struct {
	// Indicates failure to properly authenticate user.
	AdGroupCriterionError AdGroupCriterionErrorEnum_AdGroupCriterionError `protobuf:"varint,18,opt,name=ad_group_criterion_error,json=adGroupCriterionError,proto3,enum=google.ads.googleads.v1.errors.AdGroupCriterionErrorEnum_AdGroupCriterionError,oneof"`
}

type ErrorCode_AdCustomizerError struct {
	// The reasons for the ad customizer error
	AdCustomizerError AdCustomizerErrorEnum_AdCustomizerError `protobuf:"varint,19,opt,name=ad_customizer_error,json=adCustomizerError,proto3,enum=google.ads.googleads.v1.errors.AdCustomizerErrorEnum_AdCustomizerError,oneof"`
}

type ErrorCode_AdGroupAdError struct {
	// The reasons for the ad group ad error
	AdGroupAdError AdGroupAdErrorEnum_AdGroupAdError `protobuf:"varint,21,opt,name=ad_group_ad_error,json=adGroupAdError,proto3,enum=google.ads.googleads.v1.errors.AdGroupAdErrorEnum_AdGroupAdError,oneof"`
}

type ErrorCode_AdSharingError struct {
	// The reasons for the ad sharing error
	AdSharingError AdSharingErrorEnum_AdSharingError `protobuf:"varint,24,opt,name=ad_sharing_error,json=adSharingError,proto3,enum=google.ads.googleads.v1.errors.AdSharingErrorEnum_AdSharingError,oneof"`
}

type ErrorCode_AdxError struct {
	// The reasons for the adx error
	AdxError AdxErrorEnum_AdxError `protobuf:"varint,25,opt,name=adx_error,json=adxError,proto3,enum=google.ads.googleads.v1.errors.AdxErrorEnum_AdxError,oneof"`
}

type ErrorCode_AssetError struct {
	// The reasons for the asset error
	AssetError AssetErrorEnum_AssetError `protobuf:"varint,107,opt,name=asset_error,json=assetError,proto3,enum=google.ads.googleads.v1.errors.AssetErrorEnum_AssetError,oneof"`
}

type ErrorCode_BiddingError struct {
	// The reasons for the bidding errors
	BiddingError BiddingErrorEnum_BiddingError `protobuf:"varint,26,opt,name=bidding_error,json=biddingError,proto3,enum=google.ads.googleads.v1.errors.BiddingErrorEnum_BiddingError,oneof"`
}

type ErrorCode_CampaignCriterionError struct {
	// The reasons for the campaign criterion error
	CampaignCriterionError CampaignCriterionErrorEnum_CampaignCriterionError `protobuf:"varint,29,opt,name=campaign_criterion_error,json=campaignCriterionError,proto3,enum=google.ads.googleads.v1.errors.CampaignCriterionErrorEnum_CampaignCriterionError,oneof"`
}

type ErrorCode_CollectionSizeError struct {
	// The reasons for the collection size error
	CollectionSizeError CollectionSizeErrorEnum_CollectionSizeError `protobuf:"varint,31,opt,name=collection_size_error,json=collectionSizeError,proto3,enum=google.ads.googleads.v1.errors.CollectionSizeErrorEnum_CollectionSizeError,oneof"`
}

type ErrorCode_CountryCodeError struct {
	// The reasons for the country code error
	CountryCodeError CountryCodeErrorEnum_CountryCodeError `protobuf:"varint,109,opt,name=country_code_error,json=countryCodeError,proto3,enum=google.ads.googleads.v1.errors.CountryCodeErrorEnum_CountryCodeError,oneof"`
}

type ErrorCode_CriterionError struct {
	// The reasons for the criterion error
	CriterionError CriterionErrorEnum_CriterionError `protobuf:"varint,32,opt,name=criterion_error,json=criterionError,proto3,enum=google.ads.googleads.v1.errors.CriterionErrorEnum_CriterionError,oneof"`
}

type ErrorCode_CustomerError struct {
	// The reasons for the customer error
	CustomerError CustomerErrorEnum_CustomerError `protobuf:"varint,90,opt,name=customer_error,json=customerError,proto3,enum=google.ads.googleads.v1.errors.CustomerErrorEnum_CustomerError,oneof"`
}

type ErrorCode_DateError struct {
	// The reasons for the date error
	DateError DateErrorEnum_DateError `protobuf:"varint,33,opt,name=date_error,json=dateError,proto3,enum=google.ads.googleads.v1.errors.DateErrorEnum_DateError,oneof"`
}

type ErrorCode_DateRangeError struct {
	// The reasons for the date range error
	DateRangeError DateRangeErrorEnum_DateRangeError `protobuf:"varint,34,opt,name=date_range_error,json=dateRangeError,proto3,enum=google.ads.googleads.v1.errors.DateRangeErrorEnum_DateRangeError,oneof"`
}

type ErrorCode_DistinctError struct {
	// The reasons for the distinct error
	DistinctError DistinctErrorEnum_DistinctError `protobuf:"varint,35,opt,name=distinct_error,json=distinctError,proto3,enum=google.ads.googleads.v1.errors.DistinctErrorEnum_DistinctError,oneof"`
}

type ErrorCode_FeedAttributeReferenceError struct {
	// The reasons for the feed attribute reference error
	FeedAttributeReferenceError FeedAttributeReferenceErrorEnum_FeedAttributeReferenceError `protobuf:"varint,36,opt,name=feed_attribute_reference_error,json=feedAttributeReferenceError,proto3,enum=google.ads.googleads.v1.errors.FeedAttributeReferenceErrorEnum_FeedAttributeReferenceError,oneof"`
}

type ErrorCode_FunctionError struct {
	// The reasons for the function error
	FunctionError FunctionErrorEnum_FunctionError `protobuf:"varint,37,opt,name=function_error,json=functionError,proto3,enum=google.ads.googleads.v1.errors.FunctionErrorEnum_FunctionError,oneof"`
}

type ErrorCode_FunctionParsingError struct {
	// The reasons for the function parsing error
	FunctionParsingError FunctionParsingErrorEnum_FunctionParsingError `protobuf:"varint,38,opt,name=function_parsing_error,json=functionParsingError,proto3,enum=google.ads.googleads.v1.errors.FunctionParsingErrorEnum_FunctionParsingError,oneof"`
}

type ErrorCode_IdError struct {
	// The reasons for the id error
	IdError IdErrorEnum_IdError `protobuf:"varint,39,opt,name=id_error,json=idError,proto3,enum=google.ads.googleads.v1.errors.IdErrorEnum_IdError,oneof"`
}

type ErrorCode_ImageError struct {
	// The reasons for the image error
	ImageError ImageErrorEnum_ImageError `protobuf:"varint,40,opt,name=image_error,json=imageError,proto3,enum=google.ads.googleads.v1.errors.ImageErrorEnum_ImageError,oneof"`
}

type ErrorCode_LanguageCodeError struct {
	// The reasons for the language code error
	LanguageCodeError LanguageCodeErrorEnum_LanguageCodeError `protobuf:"varint,110,opt,name=language_code_error,json=languageCodeError,proto3,enum=google.ads.googleads.v1.errors.LanguageCodeErrorEnum_LanguageCodeError,oneof"`
}

type ErrorCode_MediaBundleError struct {
	// The reasons for the media bundle error
	MediaBundleError MediaBundleErrorEnum_MediaBundleError `protobuf:"varint,42,opt,name=media_bundle_error,json=mediaBundleError,proto3,enum=google.ads.googleads.v1.errors.MediaBundleErrorEnum_MediaBundleError,oneof"`
}

type ErrorCode_MediaUploadError struct {
	// The reasons for media uploading errors.
	MediaUploadError MediaUploadErrorEnum_MediaUploadError `protobuf:"varint,116,opt,name=media_upload_error,json=mediaUploadError,proto3,enum=google.ads.googleads.v1.errors.MediaUploadErrorEnum_MediaUploadError,oneof"`
}

type ErrorCode_MediaFileError struct {
	// The reasons for the media file error
	MediaFileError MediaFileErrorEnum_MediaFileError `protobuf:"varint,86,opt,name=media_file_error,json=mediaFileError,proto3,enum=google.ads.googleads.v1.errors.MediaFileErrorEnum_MediaFileError,oneof"`
}

type ErrorCode_MultiplierError struct {
	// The reasons for the multiplier error
	MultiplierError MultiplierErrorEnum_MultiplierError `protobuf:"varint,44,opt,name=multiplier_error,json=multiplierError,proto3,enum=google.ads.googleads.v1.errors.MultiplierErrorEnum_MultiplierError,oneof"`
}

type ErrorCode_NewResourceCreationError struct {
	// The reasons for the new resource creation error
	NewResourceCreationError NewResourceCreationErrorEnum_NewResourceCreationError `protobuf:"varint,45,opt,name=new_resource_creation_error,json=newResourceCreationError,proto3,enum=google.ads.googleads.v1.errors.NewResourceCreationErrorEnum_NewResourceCreationError,oneof"`
}

type ErrorCode_NotEmptyError struct {
	// The reasons for the not empty error
	NotEmptyError NotEmptyErrorEnum_NotEmptyError `protobuf:"varint,46,opt,name=not_empty_error,json=notEmptyError,proto3,enum=google.ads.googleads.v1.errors.NotEmptyErrorEnum_NotEmptyError,oneof"`
}

type ErrorCode_NullError struct {
	// The reasons for the null error
	NullError NullErrorEnum_NullError `protobuf:"varint,47,opt,name=null_error,json=nullError,proto3,enum=google.ads.googleads.v1.errors.NullErrorEnum_NullError,oneof"`
}

type ErrorCode_OperatorError struct {
	// The reasons for the operator error
	OperatorError OperatorErrorEnum_OperatorError `protobuf:"varint,48,opt,name=operator_error,json=operatorError,proto3,enum=google.ads.googleads.v1.errors.OperatorErrorEnum_OperatorError,oneof"`
}

type ErrorCode_RangeError struct {
	// The reasons for the range error
	RangeError RangeErrorEnum_RangeError `protobuf:"varint,49,opt,name=range_error,json=rangeError,proto3,enum=google.ads.googleads.v1.errors.RangeErrorEnum_RangeError,oneof"`
}

type ErrorCode_RecommendationError struct {
	// The reasons for error in applying a recommendation
	RecommendationError RecommendationErrorEnum_RecommendationError `protobuf:"varint,58,opt,name=recommendation_error,json=recommendationError,proto3,enum=google.ads.googleads.v1.errors.RecommendationErrorEnum_RecommendationError,oneof"`
}

type ErrorCode_RegionCodeError struct {
	// The reasons for the region code error
	RegionCodeError RegionCodeErrorEnum_RegionCodeError `protobuf:"varint,51,opt,name=region_code_error,json=regionCodeError,proto3,enum=google.ads.googleads.v1.errors.RegionCodeErrorEnum_RegionCodeError,oneof"`
}

type ErrorCode_SettingError struct {
	// The reasons for the setting error
	SettingError SettingErrorEnum_SettingError `protobuf:"varint,52,opt,name=setting_error,json=settingError,proto3,enum=google.ads.googleads.v1.errors.SettingErrorEnum_SettingError,oneof"`
}

type ErrorCode_StringFormatError struct {
	// The reasons for the string format error
	StringFormatError StringFormatErrorEnum_StringFormatError `protobuf:"varint,53,opt,name=string_format_error,json=stringFormatError,proto3,enum=google.ads.googleads.v1.errors.StringFormatErrorEnum_StringFormatError,oneof"`
}

type ErrorCode_StringLengthError struct {
	// The reasons for the string length error
	StringLengthError StringLengthErrorEnum_StringLengthError `protobuf:"varint,54,opt,name=string_length_error,json=stringLengthError,proto3,enum=google.ads.googleads.v1.errors.StringLengthErrorEnum_StringLengthError,oneof"`
}

type ErrorCode_OperationAccessDeniedError struct {
	// The reasons for the operation access denied error
	OperationAccessDeniedError OperationAccessDeniedErrorEnum_OperationAccessDeniedError `protobuf:"varint,55,opt,name=operation_access_denied_error,json=operationAccessDeniedError,proto3,enum=google.ads.googleads.v1.errors.OperationAccessDeniedErrorEnum_OperationAccessDeniedError,oneof"`
}

type ErrorCode_ResourceAccessDeniedError struct {
	// The reasons for the resource access denied error
	ResourceAccessDeniedError ResourceAccessDeniedErrorEnum_ResourceAccessDeniedError `protobuf:"varint,56,opt,name=resource_access_denied_error,json=resourceAccessDeniedError,proto3,enum=google.ads.googleads.v1.errors.ResourceAccessDeniedErrorEnum_ResourceAccessDeniedError,oneof"`
}

type ErrorCode_ResourceCountLimitExceededError struct {
	// The reasons for the resource count limit exceeded error
	ResourceCountLimitExceededError ResourceCountLimitExceededErrorEnum_ResourceCountLimitExceededError `protobuf:"varint,57,opt,name=resource_count_limit_exceeded_error,json=resourceCountLimitExceededError,proto3,enum=google.ads.googleads.v1.errors.ResourceCountLimitExceededErrorEnum_ResourceCountLimitExceededError,oneof"`
}

type ErrorCode_YoutubeVideoRegistrationError struct {
	// The reasons for YouTube video registration errors.
	YoutubeVideoRegistrationError YoutubeVideoRegistrationErrorEnum_YoutubeVideoRegistrationError `protobuf:"varint,117,opt,name=youtube_video_registration_error,json=youtubeVideoRegistrationError,proto3,enum=google.ads.googleads.v1.errors.YoutubeVideoRegistrationErrorEnum_YoutubeVideoRegistrationError,oneof"`
}

type ErrorCode_AdGroupBidModifierError struct {
	// The reasons for the ad group bid modifier error
	AdGroupBidModifierError AdGroupBidModifierErrorEnum_AdGroupBidModifierError `protobuf:"varint,59,opt,name=ad_group_bid_modifier_error,json=adGroupBidModifierError,proto3,enum=google.ads.googleads.v1.errors.AdGroupBidModifierErrorEnum_AdGroupBidModifierError,oneof"`
}

type ErrorCode_ContextError struct {
	// The reasons for the context error
	ContextError ContextErrorEnum_ContextError `protobuf:"varint,60,opt,name=context_error,json=contextError,proto3,enum=google.ads.googleads.v1.errors.ContextErrorEnum_ContextError,oneof"`
}

type ErrorCode_FieldError struct {
	// The reasons for the field error
	FieldError FieldErrorEnum_FieldError `protobuf:"varint,61,opt,name=field_error,json=fieldError,proto3,enum=google.ads.googleads.v1.errors.FieldErrorEnum_FieldError,oneof"`
}

type ErrorCode_SharedSetError struct {
	// The reasons for the shared set error
	SharedSetError SharedSetErrorEnum_SharedSetError `protobuf:"varint,62,opt,name=shared_set_error,json=sharedSetError,proto3,enum=google.ads.googleads.v1.errors.SharedSetErrorEnum_SharedSetError,oneof"`
}

type ErrorCode_SharedCriterionError struct {
	// The reasons for the shared criterion error
	SharedCriterionError SharedCriterionErrorEnum_SharedCriterionError `protobuf:"varint,63,opt,name=shared_criterion_error,json=sharedCriterionError,proto3,enum=google.ads.googleads.v1.errors.SharedCriterionErrorEnum_SharedCriterionError,oneof"`
}

type ErrorCode_CampaignSharedSetError struct {
	// The reasons for the campaign shared set error
	CampaignSharedSetError CampaignSharedSetErrorEnum_CampaignSharedSetError `protobuf:"varint,64,opt,name=campaign_shared_set_error,json=campaignSharedSetError,proto3,enum=google.ads.googleads.v1.errors.CampaignSharedSetErrorEnum_CampaignSharedSetError,oneof"`
}

type ErrorCode_ConversionActionError struct {
	// The reasons for the conversion action error
	ConversionActionError ConversionActionErrorEnum_ConversionActionError `protobuf:"varint,65,opt,name=conversion_action_error,json=conversionActionError,proto3,enum=google.ads.googleads.v1.errors.ConversionActionErrorEnum_ConversionActionError,oneof"`
}

type ErrorCode_ConversionAdjustmentUploadError struct {
	// The reasons for the conversion adjustment upload error
	ConversionAdjustmentUploadError ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError `protobuf:"varint,115,opt,name=conversion_adjustment_upload_error,json=conversionAdjustmentUploadError,proto3,enum=google.ads.googleads.v1.errors.ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError,oneof"`
}

type ErrorCode_ConversionUploadError struct {
	// The reasons for the conversion upload error
	ConversionUploadError ConversionUploadErrorEnum_ConversionUploadError `protobuf:"varint,111,opt,name=conversion_upload_error,json=conversionUploadError,proto3,enum=google.ads.googleads.v1.errors.ConversionUploadErrorEnum_ConversionUploadError,oneof"`
}

type ErrorCode_HeaderError struct {
	// The reasons for the header error.
	HeaderError HeaderErrorEnum_HeaderError `protobuf:"varint,66,opt,name=header_error,json=headerError,proto3,enum=google.ads.googleads.v1.errors.HeaderErrorEnum_HeaderError,oneof"`
}

type ErrorCode_DatabaseError struct {
	// The reasons for the database error.
	DatabaseError DatabaseErrorEnum_DatabaseError `protobuf:"varint,67,opt,name=database_error,json=databaseError,proto3,enum=google.ads.googleads.v1.errors.DatabaseErrorEnum_DatabaseError,oneof"`
}

type ErrorCode_PolicyFindingError struct {
	// The reasons for the policy finding error.
	PolicyFindingError PolicyFindingErrorEnum_PolicyFindingError `protobuf:"varint,68,opt,name=policy_finding_error,json=policyFindingError,proto3,enum=google.ads.googleads.v1.errors.PolicyFindingErrorEnum_PolicyFindingError,oneof"`
}

type ErrorCode_EnumError struct {
	// The reason for enum error.
	EnumError EnumErrorEnum_EnumError `protobuf:"varint,70,opt,name=enum_error,json=enumError,proto3,enum=google.ads.googleads.v1.errors.EnumErrorEnum_EnumError,oneof"`
}

type ErrorCode_KeywordPlanError struct {
	// The reason for keyword plan error.
	KeywordPlanError KeywordPlanErrorEnum_KeywordPlanError `protobuf:"varint,71,opt,name=keyword_plan_error,json=keywordPlanError,proto3,enum=google.ads.googleads.v1.errors.KeywordPlanErrorEnum_KeywordPlanError,oneof"`
}

type ErrorCode_KeywordPlanCampaignError struct {
	// The reason for keyword plan campaign error.
	KeywordPlanCampaignError KeywordPlanCampaignErrorEnum_KeywordPlanCampaignError `protobuf:"varint,72,opt,name=keyword_plan_campaign_error,json=keywordPlanCampaignError,proto3,enum=google.ads.googleads.v1.errors.KeywordPlanCampaignErrorEnum_KeywordPlanCampaignError,oneof"`
}

type ErrorCode_KeywordPlanNegativeKeywordError struct {
	// The reason for keyword plan negative keyword error.
	KeywordPlanNegativeKeywordError KeywordPlanNegativeKeywordErrorEnum_KeywordPlanNegativeKeywordError `protobuf:"varint,73,opt,name=keyword_plan_negative_keyword_error,json=keywordPlanNegativeKeywordError,proto3,enum=google.ads.googleads.v1.errors.KeywordPlanNegativeKeywordErrorEnum_KeywordPlanNegativeKeywordError,oneof"`
}

type ErrorCode_KeywordPlanAdGroupError struct {
	// The reason for keyword plan ad group error.
	KeywordPlanAdGroupError KeywordPlanAdGroupErrorEnum_KeywordPlanAdGroupError `protobuf:"varint,74,opt,name=keyword_plan_ad_group_error,json=keywordPlanAdGroupError,proto3,enum=google.ads.googleads.v1.errors.KeywordPlanAdGroupErrorEnum_KeywordPlanAdGroupError,oneof"`
}

type ErrorCode_KeywordPlanKeywordError struct {
	// The reason for keyword plan keyword error.
	KeywordPlanKeywordError KeywordPlanKeywordErrorEnum_KeywordPlanKeywordError `protobuf:"varint,75,opt,name=keyword_plan_keyword_error,json=keywordPlanKeywordError,proto3,enum=google.ads.googleads.v1.errors.KeywordPlanKeywordErrorEnum_KeywordPlanKeywordError,oneof"`
}

type ErrorCode_KeywordPlanIdeaError struct {
	// The reason for keyword idea error.
	KeywordPlanIdeaError KeywordPlanIdeaErrorEnum_KeywordPlanIdeaError `protobuf:"varint,76,opt,name=keyword_plan_idea_error,json=keywordPlanIdeaError,proto3,enum=google.ads.googleads.v1.errors.KeywordPlanIdeaErrorEnum_KeywordPlanIdeaError,oneof"`
}

type ErrorCode_AccountBudgetProposalError struct {
	// The reasons for account budget proposal errors.
	AccountBudgetProposalError AccountBudgetProposalErrorEnum_AccountBudgetProposalError `protobuf:"varint,77,opt,name=account_budget_proposal_error,json=accountBudgetProposalError,proto3,enum=google.ads.googleads.v1.errors.AccountBudgetProposalErrorEnum_AccountBudgetProposalError,oneof"`
}

type ErrorCode_UserListError struct {
	// The reasons for the user list error
	UserListError UserListErrorEnum_UserListError `protobuf:"varint,78,opt,name=user_list_error,json=userListError,proto3,enum=google.ads.googleads.v1.errors.UserListErrorEnum_UserListError,oneof"`
}

type ErrorCode_ChangeStatusError struct {
	// The reasons for the change status error
	ChangeStatusError ChangeStatusErrorEnum_ChangeStatusError `protobuf:"varint,79,opt,name=change_status_error,json=changeStatusError,proto3,enum=google.ads.googleads.v1.errors.ChangeStatusErrorEnum_ChangeStatusError,oneof"`
}

type ErrorCode_FeedError struct {
	// The reasons for the feed error
	FeedError FeedErrorEnum_FeedError `protobuf:"varint,80,opt,name=feed_error,json=feedError,proto3,enum=google.ads.googleads.v1.errors.FeedErrorEnum_FeedError,oneof"`
}

type ErrorCode_GeoTargetConstantSuggestionError struct {
	// The reasons for the geo target constant suggestion error.
	GeoTargetConstantSuggestionError GeoTargetConstantSuggestionErrorEnum_GeoTargetConstantSuggestionError `protobuf:"varint,81,opt,name=geo_target_constant_suggestion_error,json=geoTargetConstantSuggestionError,proto3,enum=google.ads.googleads.v1.errors.GeoTargetConstantSuggestionErrorEnum_GeoTargetConstantSuggestionError,oneof"`
}

type ErrorCode_CampaignDraftError struct {
	// The reasons for the campaign draft error
	CampaignDraftError CampaignDraftErrorEnum_CampaignDraftError `protobuf:"varint,82,opt,name=campaign_draft_error,json=campaignDraftError,proto3,enum=google.ads.googleads.v1.errors.CampaignDraftErrorEnum_CampaignDraftError,oneof"`
}

type ErrorCode_FeedItemError struct {
	// The reasons for the feed item error
	FeedItemError FeedItemErrorEnum_FeedItemError `protobuf:"varint,83,opt,name=feed_item_error,json=feedItemError,proto3,enum=google.ads.googleads.v1.errors.FeedItemErrorEnum_FeedItemError,oneof"`
}

type ErrorCode_LabelError struct {
	// The reason for the label error.
	LabelError LabelErrorEnum_LabelError `protobuf:"varint,84,opt,name=label_error,json=labelError,proto3,enum=google.ads.googleads.v1.errors.LabelErrorEnum_LabelError,oneof"`
}

type ErrorCode_BillingSetupError struct {
	// The reasons for the billing setup error
	BillingSetupError BillingSetupErrorEnum_BillingSetupError `protobuf:"varint,87,opt,name=billing_setup_error,json=billingSetupError,proto3,enum=google.ads.googleads.v1.errors.BillingSetupErrorEnum_BillingSetupError,oneof"`
}

type ErrorCode_CustomerClientLinkError struct {
	// The reasons for the customer client link error
	CustomerClientLinkError CustomerClientLinkErrorEnum_CustomerClientLinkError `protobuf:"varint,88,opt,name=customer_client_link_error,json=customerClientLinkError,proto3,enum=google.ads.googleads.v1.errors.CustomerClientLinkErrorEnum_CustomerClientLinkError,oneof"`
}

type ErrorCode_CustomerManagerLinkError struct {
	// The reasons for the customer manager link error
	CustomerManagerLinkError CustomerManagerLinkErrorEnum_CustomerManagerLinkError `protobuf:"varint,91,opt,name=customer_manager_link_error,json=customerManagerLinkError,proto3,enum=google.ads.googleads.v1.errors.CustomerManagerLinkErrorEnum_CustomerManagerLinkError,oneof"`
}

type ErrorCode_FeedMappingError struct {
	// The reasons for the feed mapping error
	FeedMappingError FeedMappingErrorEnum_FeedMappingError `protobuf:"varint,92,opt,name=feed_mapping_error,json=feedMappingError,proto3,enum=google.ads.googleads.v1.errors.FeedMappingErrorEnum_FeedMappingError,oneof"`
}

type ErrorCode_CustomerFeedError struct {
	// The reasons for the customer feed error
	CustomerFeedError CustomerFeedErrorEnum_CustomerFeedError `protobuf:"varint,93,opt,name=customer_feed_error,json=customerFeedError,proto3,enum=google.ads.googleads.v1.errors.CustomerFeedErrorEnum_CustomerFeedError,oneof"`
}

type ErrorCode_AdGroupFeedError struct {
	// The reasons for the ad group feed error
	AdGroupFeedError AdGroupFeedErrorEnum_AdGroupFeedError `protobuf:"varint,94,opt,name=ad_group_feed_error,json=adGroupFeedError,proto3,enum=google.ads.googleads.v1.errors.AdGroupFeedErrorEnum_AdGroupFeedError,oneof"`
}

type ErrorCode_CampaignFeedError struct {
	// The reasons for the campaign feed error
	CampaignFeedError CampaignFeedErrorEnum_CampaignFeedError `protobuf:"varint,96,opt,name=campaign_feed_error,json=campaignFeedError,proto3,enum=google.ads.googleads.v1.errors.CampaignFeedErrorEnum_CampaignFeedError,oneof"`
}

type ErrorCode_CustomInterestError struct {
	// The reasons for the custom interest error
	CustomInterestError CustomInterestErrorEnum_CustomInterestError `protobuf:"varint,97,opt,name=custom_interest_error,json=customInterestError,proto3,enum=google.ads.googleads.v1.errors.CustomInterestErrorEnum_CustomInterestError,oneof"`
}

type ErrorCode_CampaignExperimentError struct {
	// The reasons for the campaign experiment error
	CampaignExperimentError CampaignExperimentErrorEnum_CampaignExperimentError `protobuf:"varint,98,opt,name=campaign_experiment_error,json=campaignExperimentError,proto3,enum=google.ads.googleads.v1.errors.CampaignExperimentErrorEnum_CampaignExperimentError,oneof"`
}

type ErrorCode_ExtensionFeedItemError struct {
	// The reasons for the extension feed item error
	ExtensionFeedItemError ExtensionFeedItemErrorEnum_ExtensionFeedItemError `protobuf:"varint,100,opt,name=extension_feed_item_error,json=extensionFeedItemError,proto3,enum=google.ads.googleads.v1.errors.ExtensionFeedItemErrorEnum_ExtensionFeedItemError,oneof"`
}

type ErrorCode_AdParameterError struct {
	// The reasons for the ad parameter error
	AdParameterError AdParameterErrorEnum_AdParameterError `protobuf:"varint,101,opt,name=ad_parameter_error,json=adParameterError,proto3,enum=google.ads.googleads.v1.errors.AdParameterErrorEnum_AdParameterError,oneof"`
}

type ErrorCode_FeedItemValidationError struct {
	// The reasons for the feed item validation error
	FeedItemValidationError FeedItemValidationErrorEnum_FeedItemValidationError `protobuf:"varint,102,opt,name=feed_item_validation_error,json=feedItemValidationError,proto3,enum=google.ads.googleads.v1.errors.FeedItemValidationErrorEnum_FeedItemValidationError,oneof"`
}

type ErrorCode_ExtensionSettingError struct {
	// The reasons for the extension setting error
	ExtensionSettingError ExtensionSettingErrorEnum_ExtensionSettingError `protobuf:"varint,103,opt,name=extension_setting_error,json=extensionSettingError,proto3,enum=google.ads.googleads.v1.errors.ExtensionSettingErrorEnum_ExtensionSettingError,oneof"`
}

type ErrorCode_FeedItemTargetError struct {
	// The reasons for the feed item target error
	FeedItemTargetError FeedItemTargetErrorEnum_FeedItemTargetError `protobuf:"varint,104,opt,name=feed_item_target_error,json=feedItemTargetError,proto3,enum=google.ads.googleads.v1.errors.FeedItemTargetErrorEnum_FeedItemTargetError,oneof"`
}

type ErrorCode_PolicyViolationError struct {
	// The reasons for the policy violation error
	PolicyViolationError PolicyViolationErrorEnum_PolicyViolationError `protobuf:"varint,105,opt,name=policy_violation_error,json=policyViolationError,proto3,enum=google.ads.googleads.v1.errors.PolicyViolationErrorEnum_PolicyViolationError,oneof"`
}

type ErrorCode_MutateJobError struct {
	// The reasons for the mutate job error
	MutateJobError MutateJobErrorEnum_MutateJobError `protobuf:"varint,108,opt,name=mutate_job_error,json=mutateJobError,proto3,enum=google.ads.googleads.v1.errors.MutateJobErrorEnum_MutateJobError,oneof"`
}

type ErrorCode_PartialFailureError struct {
	// The reasons for the mutate job error
	PartialFailureError PartialFailureErrorEnum_PartialFailureError `protobuf:"varint,112,opt,name=partial_failure_error,json=partialFailureError,proto3,enum=google.ads.googleads.v1.errors.PartialFailureErrorEnum_PartialFailureError,oneof"`
}

type ErrorCode_PolicyValidationParameterError struct {
	// The reasons for the policy validation parameter error
	PolicyValidationParameterError PolicyValidationParameterErrorEnum_PolicyValidationParameterError `protobuf:"varint,114,opt,name=policy_validation_parameter_error,json=policyValidationParameterError,proto3,enum=google.ads.googleads.v1.errors.PolicyValidationParameterErrorEnum_PolicyValidationParameterError,oneof"`
}

type ErrorCode_SizeLimitError struct {
	// The reasons for the size limit error
	SizeLimitError SizeLimitErrorEnum_SizeLimitError `protobuf:"varint,118,opt,name=size_limit_error,json=sizeLimitError,proto3,enum=google.ads.googleads.v1.errors.SizeLimitErrorEnum_SizeLimitError,oneof"`
}

type ErrorCode_NotWhitelistedError struct {
	// The reasons for the not whitelisted error
	NotWhitelistedError NotWhitelistedErrorEnum_NotWhitelistedError `protobuf:"varint,120,opt,name=not_whitelisted_error,json=notWhitelistedError,proto3,enum=google.ads.googleads.v1.errors.NotWhitelistedErrorEnum_NotWhitelistedError,oneof"`
}

type ErrorCode_ManagerLinkError struct {
	// The reasons for the manager link error
	ManagerLinkError ManagerLinkErrorEnum_ManagerLinkError `protobuf:"varint,121,opt,name=manager_link_error,json=managerLinkError,proto3,enum=google.ads.googleads.v1.errors.ManagerLinkErrorEnum_ManagerLinkError,oneof"`
}

func (*ErrorCode_RequestError) isErrorCode_ErrorCode() {}

func (*ErrorCode_BiddingStrategyError) isErrorCode_ErrorCode() {}

func (*ErrorCode_UrlFieldError) isErrorCode_ErrorCode() {}

func (*ErrorCode_ListOperationError) isErrorCode_ErrorCode() {}

func (*ErrorCode_QueryError) isErrorCode_ErrorCode() {}

func (*ErrorCode_MutateError) isErrorCode_ErrorCode() {}

func (*ErrorCode_FieldMaskError) isErrorCode_ErrorCode() {}

func (*ErrorCode_AuthorizationError) isErrorCode_ErrorCode() {}

func (*ErrorCode_InternalError) isErrorCode_ErrorCode() {}

func (*ErrorCode_QuotaError) isErrorCode_ErrorCode() {}

func (*ErrorCode_AdError) isErrorCode_ErrorCode() {}

func (*ErrorCode_AdGroupError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CampaignBudgetError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CampaignError) isErrorCode_ErrorCode() {}

func (*ErrorCode_AuthenticationError) isErrorCode_ErrorCode() {}

func (*ErrorCode_AdGroupCriterionError) isErrorCode_ErrorCode() {}

func (*ErrorCode_AdCustomizerError) isErrorCode_ErrorCode() {}

func (*ErrorCode_AdGroupAdError) isErrorCode_ErrorCode() {}

func (*ErrorCode_AdSharingError) isErrorCode_ErrorCode() {}

func (*ErrorCode_AdxError) isErrorCode_ErrorCode() {}

func (*ErrorCode_AssetError) isErrorCode_ErrorCode() {}

func (*ErrorCode_BiddingError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CampaignCriterionError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CollectionSizeError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CountryCodeError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CriterionError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CustomerError) isErrorCode_ErrorCode() {}

func (*ErrorCode_DateError) isErrorCode_ErrorCode() {}

func (*ErrorCode_DateRangeError) isErrorCode_ErrorCode() {}

func (*ErrorCode_DistinctError) isErrorCode_ErrorCode() {}

func (*ErrorCode_FeedAttributeReferenceError) isErrorCode_ErrorCode() {}

func (*ErrorCode_FunctionError) isErrorCode_ErrorCode() {}

func (*ErrorCode_FunctionParsingError) isErrorCode_ErrorCode() {}

func (*ErrorCode_IdError) isErrorCode_ErrorCode() {}

func (*ErrorCode_ImageError) isErrorCode_ErrorCode() {}

func (*ErrorCode_LanguageCodeError) isErrorCode_ErrorCode() {}

func (*ErrorCode_MediaBundleError) isErrorCode_ErrorCode() {}

func (*ErrorCode_MediaUploadError) isErrorCode_ErrorCode() {}

func (*ErrorCode_MediaFileError) isErrorCode_ErrorCode() {}

func (*ErrorCode_MultiplierError) isErrorCode_ErrorCode() {}

func (*ErrorCode_NewResourceCreationError) isErrorCode_ErrorCode() {}

func (*ErrorCode_NotEmptyError) isErrorCode_ErrorCode() {}

func (*ErrorCode_NullError) isErrorCode_ErrorCode() {}

func (*ErrorCode_OperatorError) isErrorCode_ErrorCode() {}

func (*ErrorCode_RangeError) isErrorCode_ErrorCode() {}

func (*ErrorCode_RecommendationError) isErrorCode_ErrorCode() {}

func (*ErrorCode_RegionCodeError) isErrorCode_ErrorCode() {}

func (*ErrorCode_SettingError) isErrorCode_ErrorCode() {}

func (*ErrorCode_StringFormatError) isErrorCode_ErrorCode() {}

func (*ErrorCode_StringLengthError) isErrorCode_ErrorCode() {}

func (*ErrorCode_OperationAccessDeniedError) isErrorCode_ErrorCode() {}

func (*ErrorCode_ResourceAccessDeniedError) isErrorCode_ErrorCode() {}

func (*ErrorCode_ResourceCountLimitExceededError) isErrorCode_ErrorCode() {}

func (*ErrorCode_YoutubeVideoRegistrationError) isErrorCode_ErrorCode() {}

func (*ErrorCode_AdGroupBidModifierError) isErrorCode_ErrorCode() {}

func (*ErrorCode_ContextError) isErrorCode_ErrorCode() {}

func (*ErrorCode_FieldError) isErrorCode_ErrorCode() {}

func (*ErrorCode_SharedSetError) isErrorCode_ErrorCode() {}

func (*ErrorCode_SharedCriterionError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CampaignSharedSetError) isErrorCode_ErrorCode() {}

func (*ErrorCode_ConversionActionError) isErrorCode_ErrorCode() {}

func (*ErrorCode_ConversionAdjustmentUploadError) isErrorCode_ErrorCode() {}

func (*ErrorCode_ConversionUploadError) isErrorCode_ErrorCode() {}

func (*ErrorCode_HeaderError) isErrorCode_ErrorCode() {}

func (*ErrorCode_DatabaseError) isErrorCode_ErrorCode() {}

func (*ErrorCode_PolicyFindingError) isErrorCode_ErrorCode() {}

func (*ErrorCode_EnumError) isErrorCode_ErrorCode() {}

func (*ErrorCode_KeywordPlanError) isErrorCode_ErrorCode() {}

func (*ErrorCode_KeywordPlanCampaignError) isErrorCode_ErrorCode() {}

func (*ErrorCode_KeywordPlanNegativeKeywordError) isErrorCode_ErrorCode() {}

func (*ErrorCode_KeywordPlanAdGroupError) isErrorCode_ErrorCode() {}

func (*ErrorCode_KeywordPlanKeywordError) isErrorCode_ErrorCode() {}

func (*ErrorCode_KeywordPlanIdeaError) isErrorCode_ErrorCode() {}

func (*ErrorCode_AccountBudgetProposalError) isErrorCode_ErrorCode() {}

func (*ErrorCode_UserListError) isErrorCode_ErrorCode() {}

func (*ErrorCode_ChangeStatusError) isErrorCode_ErrorCode() {}

func (*ErrorCode_FeedError) isErrorCode_ErrorCode() {}

func (*ErrorCode_GeoTargetConstantSuggestionError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CampaignDraftError) isErrorCode_ErrorCode() {}

func (*ErrorCode_FeedItemError) isErrorCode_ErrorCode() {}

func (*ErrorCode_LabelError) isErrorCode_ErrorCode() {}

func (*ErrorCode_BillingSetupError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CustomerClientLinkError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CustomerManagerLinkError) isErrorCode_ErrorCode() {}

func (*ErrorCode_FeedMappingError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CustomerFeedError) isErrorCode_ErrorCode() {}

func (*ErrorCode_AdGroupFeedError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CampaignFeedError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CustomInterestError) isErrorCode_ErrorCode() {}

func (*ErrorCode_CampaignExperimentError) isErrorCode_ErrorCode() {}

func (*ErrorCode_ExtensionFeedItemError) isErrorCode_ErrorCode() {}

func (*ErrorCode_AdParameterError) isErrorCode_ErrorCode() {}

func (*ErrorCode_FeedItemValidationError) isErrorCode_ErrorCode() {}

func (*ErrorCode_ExtensionSettingError) isErrorCode_ErrorCode() {}

func (*ErrorCode_FeedItemTargetError) isErrorCode_ErrorCode() {}

func (*ErrorCode_PolicyViolationError) isErrorCode_ErrorCode() {}

func (*ErrorCode_MutateJobError) isErrorCode_ErrorCode() {}

func (*ErrorCode_PartialFailureError) isErrorCode_ErrorCode() {}

func (*ErrorCode_PolicyValidationParameterError) isErrorCode_ErrorCode() {}

func (*ErrorCode_SizeLimitError) isErrorCode_ErrorCode() {}

func (*ErrorCode_NotWhitelistedError) isErrorCode_ErrorCode() {}

func (*ErrorCode_ManagerLinkError) isErrorCode_ErrorCode() {}

// Describes the part of the request proto that caused the error.
type ErrorLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A field path that indicates which field was invalid in the request.
	FieldPathElements []*ErrorLocation_FieldPathElement `protobuf:"bytes,2,rep,name=field_path_elements,json=fieldPathElements,proto3" json:"field_path_elements,omitempty"`
}

func (x *ErrorLocation) Reset() {
	*x = ErrorLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErrorLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorLocation) ProtoMessage() {}

func (x *ErrorLocation) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorLocation.ProtoReflect.Descriptor instead.
func (*ErrorLocation) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_errors_proto_rawDescGZIP(), []int{3}
}

func (x *ErrorLocation) GetFieldPathElements() []*ErrorLocation_FieldPathElement {
	if x != nil {
		return x.FieldPathElements
	}
	return nil
}

// Additional error details.
type ErrorDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The error code that should have been returned, but wasn't. This is used
	// when the error code is InternalError.ERROR_CODE_NOT_PUBLISHED.
	UnpublishedErrorCode string `protobuf:"bytes,1,opt,name=unpublished_error_code,json=unpublishedErrorCode,proto3" json:"unpublished_error_code,omitempty"`
	// Describes an ad policy violation.
	PolicyViolationDetails *PolicyViolationDetails `protobuf:"bytes,2,opt,name=policy_violation_details,json=policyViolationDetails,proto3" json:"policy_violation_details,omitempty"`
	// Describes policy violation findings.
	PolicyFindingDetails *PolicyFindingDetails `protobuf:"bytes,3,opt,name=policy_finding_details,json=policyFindingDetails,proto3" json:"policy_finding_details,omitempty"`
}

func (x *ErrorDetails) Reset() {
	*x = ErrorDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErrorDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorDetails) ProtoMessage() {}

func (x *ErrorDetails) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorDetails.ProtoReflect.Descriptor instead.
func (*ErrorDetails) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_errors_proto_rawDescGZIP(), []int{4}
}

func (x *ErrorDetails) GetUnpublishedErrorCode() string {
	if x != nil {
		return x.UnpublishedErrorCode
	}
	return ""
}

func (x *ErrorDetails) GetPolicyViolationDetails() *PolicyViolationDetails {
	if x != nil {
		return x.PolicyViolationDetails
	}
	return nil
}

func (x *ErrorDetails) GetPolicyFindingDetails() *PolicyFindingDetails {
	if x != nil {
		return x.PolicyFindingDetails
	}
	return nil
}

// Error returned as part of a mutate response.
// This error indicates single policy violation by some text
// in one of the fields.
type PolicyViolationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Human readable description of policy violation.
	ExternalPolicyDescription string `protobuf:"bytes,2,opt,name=external_policy_description,json=externalPolicyDescription,proto3" json:"external_policy_description,omitempty"`
	// Unique identifier for this violation.
	// If policy is exemptible, this key may be used to request exemption.
	Key *common.PolicyViolationKey `protobuf:"bytes,4,opt,name=key,proto3" json:"key,omitempty"`
	// Human readable name of the policy.
	ExternalPolicyName string `protobuf:"bytes,5,opt,name=external_policy_name,json=externalPolicyName,proto3" json:"external_policy_name,omitempty"`
	// Whether user can file an exemption request for this violation.
	IsExemptible bool `protobuf:"varint,6,opt,name=is_exemptible,json=isExemptible,proto3" json:"is_exemptible,omitempty"`
}

func (x *PolicyViolationDetails) Reset() {
	*x = PolicyViolationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolicyViolationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicyViolationDetails) ProtoMessage() {}

func (x *PolicyViolationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicyViolationDetails.ProtoReflect.Descriptor instead.
func (*PolicyViolationDetails) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_errors_proto_rawDescGZIP(), []int{5}
}

func (x *PolicyViolationDetails) GetExternalPolicyDescription() string {
	if x != nil {
		return x.ExternalPolicyDescription
	}
	return ""
}

func (x *PolicyViolationDetails) GetKey() *common.PolicyViolationKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *PolicyViolationDetails) GetExternalPolicyName() string {
	if x != nil {
		return x.ExternalPolicyName
	}
	return ""
}

func (x *PolicyViolationDetails) GetIsExemptible() bool {
	if x != nil {
		return x.IsExemptible
	}
	return false
}

// Error returned as part of a mutate response.
// This error indicates one or more policy findings in the fields of a
// resource.
type PolicyFindingDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of policy topics for the resource. Contains the PROHIBITED or
	// FULLY_LIMITED policy topic entries that prevented the resource from being
	// saved (among any other entries the resource may also have).
	PolicyTopicEntries []*common.PolicyTopicEntry `protobuf:"bytes,1,rep,name=policy_topic_entries,json=policyTopicEntries,proto3" json:"policy_topic_entries,omitempty"`
}

func (x *PolicyFindingDetails) Reset() {
	*x = PolicyFindingDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolicyFindingDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicyFindingDetails) ProtoMessage() {}

func (x *PolicyFindingDetails) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicyFindingDetails.ProtoReflect.Descriptor instead.
func (*PolicyFindingDetails) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_errors_proto_rawDescGZIP(), []int{6}
}

func (x *PolicyFindingDetails) GetPolicyTopicEntries() []*common.PolicyTopicEntry {
	if x != nil {
		return x.PolicyTopicEntries
	}
	return nil
}

// A part of a field path.
type ErrorLocation_FieldPathElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of a field or a oneof
	FieldName string `protobuf:"bytes,1,opt,name=field_name,json=fieldName,proto3" json:"field_name,omitempty"`
	// If field_name is a repeated field, this is the element that failed
	Index *wrappers.Int64Value `protobuf:"bytes,2,opt,name=index,proto3" json:"index,omitempty"`
}

func (x *ErrorLocation_FieldPathElement) Reset() {
	*x = ErrorLocation_FieldPathElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErrorLocation_FieldPathElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorLocation_FieldPathElement) ProtoMessage() {}

func (x *ErrorLocation_FieldPathElement) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_errors_errors_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorLocation_FieldPathElement.ProtoReflect.Descriptor instead.
func (*ErrorLocation_FieldPathElement) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_errors_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ErrorLocation_FieldPathElement) GetFieldName() string {
	if x != nil {
		return x.FieldName
	}
	return ""
}

func (x *ErrorLocation_FieldPathElement) GetIndex() *wrappers.Int64Value {
	if x != nil {
		return x.Index
	}
	return nil
}

var File_google_ads_googleads_v1_errors_errors_proto protoreflect.FileDescriptor

var file_google_ads_googleads_v1_errors_errors_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x1a, 0x2b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x42, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62,
	0x75, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x64, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x2f, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x61, 0x64, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x64, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3d, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x64,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x64, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2f, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x64, 0x5f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x64, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x64, 0x78, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x39, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x32, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2f, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x74, 0x75,
	0x70, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3d, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x39, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x5f, 0x64, 0x72, 0x61, 0x66, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x2f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x5f, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x3e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x68, 0x61, 0x72,
	0x65, 0x64, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x47, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x3c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x37, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x63, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x3a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x66, 0x65, 0x65, 0x64,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x5f, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x64,
	0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x64, 0x69, 0x73, 0x74,
	0x69, 0x6e, 0x63, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x3e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x65, 0x65, 0x64,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x3c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x43, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f,
	0x66, 0x65, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64,
	0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65, 0x65, 0x64,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x6d,
	0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x30, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x75, 0x6e, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x3b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x73, 0x69, 0x6e,
	0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x49, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x67, 0x65,
	0x6f, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x5f, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x69, 0x64, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x40, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x61,
	0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x2f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x6e,
	0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64,
	0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x65, 0x61,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72,
	0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x48, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x65, 0x67, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x39, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x62, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x35, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x75, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x35, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x75, 0x74, 0x61, 0x74,
	0x65, 0x5f, 0x6a, 0x6f, 0x62, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x40, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2f, 0x6e, 0x65, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x2f, 0x6e, 0x6f, 0x74, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6e, 0x6f, 0x74, 0x5f, 0x77, 0x68,
	0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64,
	0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6e, 0x75, 0x6c, 0x6c, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x42, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x6e, 0x69, 0x65, 0x64, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x3a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f,
	0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x39, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x5f, 0x66, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x46, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x71, 0x75,
	0x6f, 0x74, 0x61, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x30, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f,
	0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x39, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x41, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x6e, 0x69, 0x65, 0x64, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x48, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x5f, 0x65, 0x78, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64,
	0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64,
	0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x65,
	0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x73, 0x69,
	0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x2f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x73,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x75, 0x72, 0x6c, 0x5f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x45, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x2f, 0x79, 0x6f, 0x75, 0x74, 0x75, 0x62, 0x65, 0x5f, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61,
	0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5a, 0x0a, 0x10, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x46, 0x0a,
	0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x47,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x06, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x22, 0xc8, 0x02, 0x0a, 0x0e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x41, 0x64, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x48, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3f, 0x0a, 0x07,
	0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x12, 0x49, 0x0a,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x22, 0xec, 0x62, 0x0a, 0x09, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x64,
	0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x85, 0x01, 0x0a, 0x16, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e,
	0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x14, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x69, 0x0a, 0x0f,
	0x75, 0x72, 0x6c, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x55, 0x72, 0x6c, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x55, 0x72, 0x6c, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0d, 0x75, 0x72, 0x6c, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x7d, 0x0a, 0x14, 0x6c, 0x69, 0x73, 0x74, 0x5f,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x49, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x48, 0x00, 0x52, 0x12, 0x6c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x5c, 0x0a, 0x0b, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x60, 0x0a, 0x0c, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4d, 0x75, 0x74, 0x61,
	0x74, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4d, 0x75, 0x74, 0x61,
	0x74, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0b, 0x6d, 0x75, 0x74, 0x61, 0x74,
	0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x6d, 0x0a, 0x10, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x6d, 0x61, 0x73, 0x6b, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x41, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0e, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x7c, 0x0a, 0x13, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x49, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52,
	0x12, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x68, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0d,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x5c, 0x0a,
	0x0b, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e,
	0x75, 0x6d, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52,
	0x0a, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x50, 0x0a, 0x08, 0x61,
	0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x41,
	0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x64, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x48, 0x00, 0x52, 0x07, 0x61, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x65, 0x0a,
	0x0e, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0c, 0x61, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x81, 0x01, 0x0a, 0x15, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x4b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64,
	0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x42, 0x75,
	0x64, 0x67, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x48, 0x00, 0x52, 0x13, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x42, 0x75, 0x64,
	0x67, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x68, 0x0a, 0x0e, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x3f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45,
	0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x48, 0x00, 0x52, 0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x80, 0x01, 0x0a, 0x14, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x4b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x65,
	0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00,
	0x52, 0x13, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x8a, 0x01, 0x0a, 0x18, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45,
	0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x15, 0x61, 0x64, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x79, 0x0a, 0x13, 0x61, 0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69,
	0x7a, 0x65, 0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x47, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2e, 0x41, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69,
	0x7a, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x11, 0x61, 0x64, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x6e, 0x0a,
	0x11, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x61, 0x64, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x41, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x64, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x41, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0e, 0x61,
	0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x41, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x6d, 0x0a,
	0x10, 0x61, 0x64, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x41, 0x64, 0x53, 0x68, 0x61, 0x72, 0x69,
	0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x64, 0x53, 0x68,
	0x61, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0e, 0x61, 0x64,
	0x53, 0x68, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x54, 0x0a, 0x09,
	0x61, 0x64, 0x78, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x35, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2e, 0x41, 0x64, 0x78, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x64,
	0x78, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x08, 0x61, 0x64, 0x78, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x5c, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x6b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x64, 0x0a, 0x0d, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e,
	0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0c, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e,
	0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x8d, 0x01, 0x0a, 0x18, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x51, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x43, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x16,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x81, 0x01, 0x0a, 0x15, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x1f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x69, 0x7a, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x69, 0x7a, 0x65, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x13, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x69, 0x7a, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x75, 0x0a, 0x12, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x6d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52,
	0x10, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x6c, 0x0a, 0x0f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x72, 0x69, 0x74,
	0x65, 0x72, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43,
	0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52,
	0x0e, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12,
	0x68, 0x0a, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x5a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0d, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x58, 0x0a, 0x0a, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x09, 0x64, 0x61, 0x74, 0x65, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x6d, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75,
	0x6d, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x48, 0x00, 0x52, 0x0e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x68, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x63, 0x74, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x44, 0x69, 0x73, 0x74,
	0x69, 0x6e, 0x63, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x44, 0x69,
	0x73, 0x74, 0x69, 0x6e, 0x63, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0d, 0x64,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x63, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0xa2, 0x01, 0x0a,
	0x1e, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x24, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x5b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x48, 0x00, 0x52, 0x1b, 0x66, 0x65, 0x65, 0x64, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x68, 0x0a, 0x0e, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x18, 0x25, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x46, 0x75, 0x6e,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0d, 0x66, 0x75,
	0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x85, 0x01, 0x0a, 0x16,
	0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x73, 0x69, 0x6e, 0x67,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x26, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4d, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x46, 0x75,
	0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x73, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x61, 0x72, 0x73, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x14, 0x66,
	0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x73, 0x69, 0x6e, 0x67, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x50, 0x0a, 0x08, 0x69, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x27, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x49, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e,
	0x75, 0x6d, 0x2e, 0x49, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x07, 0x69, 0x64,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x5c, 0x0a, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x28, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x79, 0x0a, 0x13, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x6e, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x47, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x11, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x75,
	0x0a, 0x12, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4d, 0x65, 0x64, 0x69,
	0x61, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d,
	0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x48, 0x00, 0x52, 0x10, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x75, 0x0a, 0x12, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x74, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x45, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x10, 0x6d, 0x65, 0x64, 0x69,
	0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x6d, 0x0a, 0x10,
	0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x56, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x46, 0x69, 0x6c,
	0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61,
	0x46, 0x69, 0x6c, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0e, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x70, 0x0a, 0x10, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x2c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x43, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65,
	0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x70, 0x6c, 0x69, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0f, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x96, 0x01,
	0x0a, 0x1b, 0x6e, 0x65, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x2d, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x55, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4e, 0x65, 0x77, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75,
	0x6d, 0x2e, 0x4e, 0x65, 0x77, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x18, 0x6e, 0x65,
	0x77, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x69, 0x0a, 0x0f, 0x6e, 0x6f, 0x74, 0x5f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x3f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2e, 0x4e, 0x6f, 0x74, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e,
	0x75, 0x6d, 0x2e, 0x4e, 0x6f, 0x74, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x48, 0x00, 0x52, 0x0d, 0x6e, 0x6f, 0x74, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x58, 0x0a, 0x0a, 0x6e, 0x75, 0x6c, 0x6c, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x2f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4e, 0x75, 0x6c, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4e, 0x75, 0x6c, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00,
	0x52, 0x09, 0x6e, 0x75, 0x6c, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x68, 0x0a, 0x0e, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x30, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x5c, 0x0a, 0x0b, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x31, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0a, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x80, 0x01, 0x0a, 0x14, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x3a, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x4b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x52, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48,
	0x00, 0x52, 0x13, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x71, 0x0a, 0x11, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x33, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x43, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x64, 0x0a, 0x0d, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x34, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e,
	0x75, 0x6d, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48,
	0x00, 0x52, 0x0c, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12,
	0x79, 0x0a, 0x13, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x35, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x47, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45,
	0x6e, 0x75, 0x6d, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x11, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x79, 0x0a, 0x13, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x36, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x47, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x48, 0x00, 0x52, 0x11, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x9e, 0x01, 0x0a, 0x1d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x6e, 0x69, 0x65,
	0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x37, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x59, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x44, 0x65,
	0x6e, 0x69, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x44, 0x65, 0x6e,
	0x69, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x1a, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x44, 0x65, 0x6e, 0x69, 0x65,
	0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x9a, 0x01, 0x0a, 0x1c, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x6e, 0x69, 0x65,
	0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x38, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x57, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x44, 0x65, 0x6e,
	0x69, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x44, 0x65, 0x6e, 0x69, 0x65,
	0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x19, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x44, 0x65, 0x6e, 0x69, 0x65, 0x64, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0xb3, 0x01, 0x0a, 0x23, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x65, 0x78, 0x63,
	0x65, 0x65, 0x64, 0x65, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x39, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x63, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x65,
	0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x1f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45, 0x78, 0x63, 0x65,
	0x65, 0x64, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0xaa, 0x01, 0x0a, 0x20, 0x79, 0x6f,
	0x75, 0x74, 0x75, 0x62, 0x65, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x75,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x5f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64,
	0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x59, 0x6f, 0x75, 0x74, 0x75, 0x62, 0x65, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x59, 0x6f, 0x75, 0x74, 0x75, 0x62, 0x65, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x1d, 0x79, 0x6f, 0x75, 0x74, 0x75, 0x62, 0x65,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x93, 0x01, 0x0a, 0x1b, 0x61, 0x64, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x53, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x41, 0x64,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x69, 0x64, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x42, 0x69, 0x64, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x48, 0x00, 0x52, 0x17, 0x61, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x69, 0x64, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x64, 0x0a, 0x0d,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x3c, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x5c, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x48, 0x00, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x6d, 0x0a, 0x10, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x3e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x53, 0x68, 0x61, 0x72,
	0x65, 0x64, 0x53, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x53,
	0x68, 0x61, 0x72, 0x65, 0x64, 0x53, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52,
	0x0e, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x53, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12,
	0x85, 0x01, 0x0a, 0x16, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x3f, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x4d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x64, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48,
	0x00, 0x52, 0x14, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69,
	0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x8e, 0x01, 0x0a, 0x19, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x74, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x40, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x51, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x53, 0x65, 0x74, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x53, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00,
	0x52, 0x16, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64,
	0x53, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x89, 0x01, 0x0a, 0x17, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x41, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4f, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x15, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0xb2, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x73, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x63, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a,
	0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x1f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x89, 0x01, 0x0a, 0x17, 0x63, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x6f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x15,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x60, 0x0a, 0x0c, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x42, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x68, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x62,
	0x61, 0x73, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x43, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x3f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e,
	0x75, 0x6d, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x48, 0x00, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x7d, 0x0a, 0x14, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x66, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x44, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x49, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x46, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x12, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x58, 0x0a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x46,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64,
	0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45,
	0x6e, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52,
	0x09, 0x65, 0x6e, 0x75, 0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x75, 0x0a, 0x12, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x47, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x50,
	0x6c, 0x61, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52,
	0x10, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x96, 0x01, 0x0a, 0x1b, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x48, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x55, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x50, 0x6c, 0x61, 0x6e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6c, 0x61,
	0x6e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00,
	0x52, 0x18, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0xb3, 0x01, 0x0a, 0x23, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x65, 0x67, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x49, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x63, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72,
	0x64, 0x50, 0x6c, 0x61, 0x6e, 0x4e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x4b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x4e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52,
	0x1f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x4e, 0x65, 0x67, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x93, 0x01, 0x0a, 0x1b, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x70, 0x6c, 0x61,
	0x6e, 0x5f, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x4a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x53, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x50,
	0x6c, 0x61, 0x6e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45,
	0x6e, 0x75, 0x6d, 0x2e, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x41,
	0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x17, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x92, 0x01, 0x0a, 0x1a, 0x6b, 0x65, 0x79, 0x77, 0x6f,
	0x72, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x4b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x53, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x50, 0x6c, 0x61, 0x6e, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x48, 0x00, 0x52, 0x17, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x4b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x86, 0x01, 0x0a, 0x17,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x65,
	0x61, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x4c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4d, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x65, 0x61, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x50,
	0x6c, 0x61, 0x6e, 0x49, 0x64, 0x65, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x14,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x65, 0x61, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x9e, 0x01, 0x0a, 0x1d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x61, 0x6c,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x4d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x59, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x6f,
	0x73, 0x61, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x6f, 0x73,
	0x61, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x1a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x61, 0x6c,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x69, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x4e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75,
	0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48,
	0x00, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x79, 0x0a, 0x13, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x4f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x47, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x11, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x58, 0x0a, 0x0a, 0x66,
	0x65, 0x65, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x50, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x37, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2e, 0x46, 0x65, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x46,
	0x65, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x09, 0x66, 0x65, 0x65, 0x64,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0xb7, 0x01, 0x0a, 0x24, 0x67, 0x65, 0x6f, 0x5f, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x73, 0x75,
	0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x51,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64,
	0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x47, 0x65, 0x6f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x47, 0x65, 0x6f, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x53, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x20, 0x67,
	0x65, 0x6f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12,
	0x7d, 0x0a, 0x14, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x64, 0x72, 0x61, 0x66,
	0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x52, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x49, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x44, 0x72, 0x61, 0x66, 0x74, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x44, 0x72,
	0x61, 0x66, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x12, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x44, 0x72, 0x61, 0x66, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x69,
	0x0a, 0x0f, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x53, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0d, 0x66, 0x65, 0x65, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x5c, 0x0a, 0x0b, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x54, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0a, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x79, 0x0a, 0x13, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x5f, 0x73, 0x65, 0x74, 0x75, 0x70, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x57,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x47, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64,
	0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x74,
	0x75, 0x70, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x42, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x74, 0x75, 0x70, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52,
	0x11, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x74, 0x75, 0x70, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x92, 0x01, 0x0a, 0x1a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x58, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x53, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x17,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x6e, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x96, 0x01, 0x0a, 0x1b, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x5b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x55, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x4c, 0x69,
	0x6e, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x6b, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x18, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x75, 0x0a, 0x12, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x5c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x46, 0x65,
	0x65, 0x64, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e,
	0x75, 0x6d, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x10, 0x66, 0x65, 0x65, 0x64, 0x4d, 0x61, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x79, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x5d,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x47, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64,
	0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x46, 0x65,
	0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x46, 0x65, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52,
	0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x46, 0x65, 0x65, 0x64, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x76, 0x0a, 0x13, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x66,
	0x65, 0x65, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x5e, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x45, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x46, 0x65, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x46, 0x65, 0x65,
	0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x10, 0x61, 0x64, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x46, 0x65, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x79, 0x0a, 0x13, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x60, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x47, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x46, 0x65, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x46, 0x65, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x48, 0x00, 0x52, 0x11, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x46, 0x65, 0x65, 0x64,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x81, 0x01, 0x0a, 0x15, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x61, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x73, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x48, 0x00, 0x52, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x91, 0x01, 0x0a, 0x19, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x62, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x53, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e,
	0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x48, 0x00, 0x52, 0x17, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x45, 0x78,
	0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x8e, 0x01,
	0x0a, 0x19, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x65, 0x65, 0x64,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x64, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x51, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x16, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x46, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x75,
	0x0a, 0x12, 0x61, 0x64, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x41, 0x64, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d,
	0x2e, 0x41, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x48, 0x00, 0x52, 0x10, 0x61, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x92, 0x01, 0x0a, 0x1a, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x66, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x53, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x46, 0x65, 0x65, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48,
	0x00, 0x52, 0x17, 0x66, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x89, 0x01, 0x0a, 0x17, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x67, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4f, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52,
	0x15, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x82, 0x01, 0x0a, 0x16, 0x66, 0x65, 0x65, 0x64, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x68, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d,
	0x2e, 0x46, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x13, 0x66, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x85, 0x01, 0x0a, 0x16,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x69, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4d, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x56, 0x69, 0x6f,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x14, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x6d, 0x0a, 0x10, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x6a, 0x6f,
	0x62, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x6c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4d,
	0x75, 0x74, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75,
	0x6d, 0x2e, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x48, 0x00, 0x52, 0x0e, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x81, 0x01, 0x0a, 0x15, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x70, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x4b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x61, 0x72, 0x74,
	0x69, 0x61, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48,
	0x00, 0x52, 0x13, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0xae, 0x01, 0x0a, 0x21, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x72, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x61, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x1e, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x6d, 0x0a, 0x10, 0x73, 0x69, 0x7a, 0x65, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x76, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x41, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0e, 0x73, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x81, 0x01, 0x0a, 0x15, 0x6e, 0x6f, 0x74, 0x5f, 0x77,
	0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x78, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4e, 0x6f, 0x74, 0x57, 0x68, 0x69, 0x74, 0x65,
	0x6c, 0x69, 0x73, 0x74, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e,
	0x4e, 0x6f, 0x74, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x64, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x13, 0x6e, 0x6f, 0x74, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c,
	0x69, 0x73, 0x74, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x75, 0x0a, 0x12, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x79, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x4c,
	0x69, 0x6e, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52,
	0x10, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x6b, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x42, 0x0c, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x22,
	0xe5, 0x01, 0x0a, 0x0d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x6e, 0x0a, 0x13, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x50, 0x61, 0x74, 0x68, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x11,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x50, 0x61, 0x74, 0x68, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x1a, 0x64, 0x0a, 0x10, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x50, 0x61, 0x74, 0x68, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0xa2, 0x02, 0x0a, 0x0c, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x75, 0x6e, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x75, 0x6e, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x70,
	0x0a, 0x18, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x16, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x6a, 0x0a, 0x16, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x66, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x34, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x46, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xf5, 0x01, 0x0a,
	0x16, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3e, 0x0a, 0x1b, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64,
	0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x56, 0x69, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a,
	0x14, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x62, 0x6c, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x45, 0x78, 0x65, 0x6d, 0x70, 0x74,
	0x69, 0x62, 0x6c, 0x65, 0x22, 0x7a, 0x0a, 0x14, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x46, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x62, 0x0a, 0x14,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73,
	0x42, 0xe6, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x42, 0x0b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x44, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67,
	0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x64,
	0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x3b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0xa2, 0x02, 0x03, 0x47,
	0x41, 0x41, 0xaa, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x41, 0x64, 0x73, 0x2e,
	0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x2e, 0x56, 0x31, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0xca, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x41, 0x64, 0x73,
	0x5c, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x5c, 0x56, 0x31, 0x5c, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0xea, 0x02, 0x22, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x41,
	0x64, 0x73, 0x3a, 0x3a, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x3a, 0x3a, 0x56,
	0x31, 0x3a, 0x3a, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_google_ads_googleads_v1_errors_errors_proto_rawDescOnce sync.Once
	file_google_ads_googleads_v1_errors_errors_proto_rawDescData = file_google_ads_googleads_v1_errors_errors_proto_rawDesc
)

func file_google_ads_googleads_v1_errors_errors_proto_rawDescGZIP() []byte {
	file_google_ads_googleads_v1_errors_errors_proto_rawDescOnce.Do(func() {
		file_google_ads_googleads_v1_errors_errors_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_ads_googleads_v1_errors_errors_proto_rawDescData)
	})
	return file_google_ads_googleads_v1_errors_errors_proto_rawDescData
}

var file_google_ads_googleads_v1_errors_errors_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_google_ads_googleads_v1_errors_errors_proto_goTypes = []interface{}{
	(*GoogleAdsFailure)(nil),                                                   // 0: google.ads.googleads.v1.errors.GoogleAdsFailure
	(*GoogleAdsError)(nil),                                                     // 1: google.ads.googleads.v1.errors.GoogleAdsError
	(*ErrorCode)(nil),                                                          // 2: google.ads.googleads.v1.errors.ErrorCode
	(*ErrorLocation)(nil),                                                      // 3: google.ads.googleads.v1.errors.ErrorLocation
	(*ErrorDetails)(nil),                                                       // 4: google.ads.googleads.v1.errors.ErrorDetails
	(*PolicyViolationDetails)(nil),                                             // 5: google.ads.googleads.v1.errors.PolicyViolationDetails
	(*PolicyFindingDetails)(nil),                                               // 6: google.ads.googleads.v1.errors.PolicyFindingDetails
	(*ErrorLocation_FieldPathElement)(nil),                                     // 7: google.ads.googleads.v1.errors.ErrorLocation.FieldPathElement
	(*common.Value)(nil),                                                       // 8: google.ads.googleads.v1.common.Value
	(RequestErrorEnum_RequestError)(0),                                         // 9: google.ads.googleads.v1.errors.RequestErrorEnum.RequestError
	(BiddingStrategyErrorEnum_BiddingStrategyError)(0),                         // 10: google.ads.googleads.v1.errors.BiddingStrategyErrorEnum.BiddingStrategyError
	(UrlFieldErrorEnum_UrlFieldError)(0),                                       // 11: google.ads.googleads.v1.errors.UrlFieldErrorEnum.UrlFieldError
	(ListOperationErrorEnum_ListOperationError)(0),                             // 12: google.ads.googleads.v1.errors.ListOperationErrorEnum.ListOperationError
	(QueryErrorEnum_QueryError)(0),                                             // 13: google.ads.googleads.v1.errors.QueryErrorEnum.QueryError
	(MutateErrorEnum_MutateError)(0),                                           // 14: google.ads.googleads.v1.errors.MutateErrorEnum.MutateError
	(FieldMaskErrorEnum_FieldMaskError)(0),                                     // 15: google.ads.googleads.v1.errors.FieldMaskErrorEnum.FieldMaskError
	(AuthorizationErrorEnum_AuthorizationError)(0),                             // 16: google.ads.googleads.v1.errors.AuthorizationErrorEnum.AuthorizationError
	(InternalErrorEnum_InternalError)(0),                                       // 17: google.ads.googleads.v1.errors.InternalErrorEnum.InternalError
	(QuotaErrorEnum_QuotaError)(0),                                             // 18: google.ads.googleads.v1.errors.QuotaErrorEnum.QuotaError
	(AdErrorEnum_AdError)(0),                                                   // 19: google.ads.googleads.v1.errors.AdErrorEnum.AdError
	(AdGroupErrorEnum_AdGroupError)(0),                                         // 20: google.ads.googleads.v1.errors.AdGroupErrorEnum.AdGroupError
	(CampaignBudgetErrorEnum_CampaignBudgetError)(0),                           // 21: google.ads.googleads.v1.errors.CampaignBudgetErrorEnum.CampaignBudgetError
	(CampaignErrorEnum_CampaignError)(0),                                       // 22: google.ads.googleads.v1.errors.CampaignErrorEnum.CampaignError
	(AuthenticationErrorEnum_AuthenticationError)(0),                           // 23: google.ads.googleads.v1.errors.AuthenticationErrorEnum.AuthenticationError
	(AdGroupCriterionErrorEnum_AdGroupCriterionError)(0),                       // 24: google.ads.googleads.v1.errors.AdGroupCriterionErrorEnum.AdGroupCriterionError
	(AdCustomizerErrorEnum_AdCustomizerError)(0),                               // 25: google.ads.googleads.v1.errors.AdCustomizerErrorEnum.AdCustomizerError
	(AdGroupAdErrorEnum_AdGroupAdError)(0),                                     // 26: google.ads.googleads.v1.errors.AdGroupAdErrorEnum.AdGroupAdError
	(AdSharingErrorEnum_AdSharingError)(0),                                     // 27: google.ads.googleads.v1.errors.AdSharingErrorEnum.AdSharingError
	(AdxErrorEnum_AdxError)(0),                                                 // 28: google.ads.googleads.v1.errors.AdxErrorEnum.AdxError
	(AssetErrorEnum_AssetError)(0),                                             // 29: google.ads.googleads.v1.errors.AssetErrorEnum.AssetError
	(BiddingErrorEnum_BiddingError)(0),                                         // 30: google.ads.googleads.v1.errors.BiddingErrorEnum.BiddingError
	(CampaignCriterionErrorEnum_CampaignCriterionError)(0),                     // 31: google.ads.googleads.v1.errors.CampaignCriterionErrorEnum.CampaignCriterionError
	(CollectionSizeErrorEnum_CollectionSizeError)(0),                           // 32: google.ads.googleads.v1.errors.CollectionSizeErrorEnum.CollectionSizeError
	(CountryCodeErrorEnum_CountryCodeError)(0),                                 // 33: google.ads.googleads.v1.errors.CountryCodeErrorEnum.CountryCodeError
	(CriterionErrorEnum_CriterionError)(0),                                     // 34: google.ads.googleads.v1.errors.CriterionErrorEnum.CriterionError
	(CustomerErrorEnum_CustomerError)(0),                                       // 35: google.ads.googleads.v1.errors.CustomerErrorEnum.CustomerError
	(DateErrorEnum_DateError)(0),                                               // 36: google.ads.googleads.v1.errors.DateErrorEnum.DateError
	(DateRangeErrorEnum_DateRangeError)(0),                                     // 37: google.ads.googleads.v1.errors.DateRangeErrorEnum.DateRangeError
	(DistinctErrorEnum_DistinctError)(0),                                       // 38: google.ads.googleads.v1.errors.DistinctErrorEnum.DistinctError
	(FeedAttributeReferenceErrorEnum_FeedAttributeReferenceError)(0),           // 39: google.ads.googleads.v1.errors.FeedAttributeReferenceErrorEnum.FeedAttributeReferenceError
	(FunctionErrorEnum_FunctionError)(0),                                       // 40: google.ads.googleads.v1.errors.FunctionErrorEnum.FunctionError
	(FunctionParsingErrorEnum_FunctionParsingError)(0),                         // 41: google.ads.googleads.v1.errors.FunctionParsingErrorEnum.FunctionParsingError
	(IdErrorEnum_IdError)(0),                                                   // 42: google.ads.googleads.v1.errors.IdErrorEnum.IdError
	(ImageErrorEnum_ImageError)(0),                                             // 43: google.ads.googleads.v1.errors.ImageErrorEnum.ImageError
	(LanguageCodeErrorEnum_LanguageCodeError)(0),                               // 44: google.ads.googleads.v1.errors.LanguageCodeErrorEnum.LanguageCodeError
	(MediaBundleErrorEnum_MediaBundleError)(0),                                 // 45: google.ads.googleads.v1.errors.MediaBundleErrorEnum.MediaBundleError
	(MediaUploadErrorEnum_MediaUploadError)(0),                                 // 46: google.ads.googleads.v1.errors.MediaUploadErrorEnum.MediaUploadError
	(MediaFileErrorEnum_MediaFileError)(0),                                     // 47: google.ads.googleads.v1.errors.MediaFileErrorEnum.MediaFileError
	(MultiplierErrorEnum_MultiplierError)(0),                                   // 48: google.ads.googleads.v1.errors.MultiplierErrorEnum.MultiplierError
	(NewResourceCreationErrorEnum_NewResourceCreationError)(0),                 // 49: google.ads.googleads.v1.errors.NewResourceCreationErrorEnum.NewResourceCreationError
	(NotEmptyErrorEnum_NotEmptyError)(0),                                       // 50: google.ads.googleads.v1.errors.NotEmptyErrorEnum.NotEmptyError
	(NullErrorEnum_NullError)(0),                                               // 51: google.ads.googleads.v1.errors.NullErrorEnum.NullError
	(OperatorErrorEnum_OperatorError)(0),                                       // 52: google.ads.googleads.v1.errors.OperatorErrorEnum.OperatorError
	(RangeErrorEnum_RangeError)(0),                                             // 53: google.ads.googleads.v1.errors.RangeErrorEnum.RangeError
	(RecommendationErrorEnum_RecommendationError)(0),                           // 54: google.ads.googleads.v1.errors.RecommendationErrorEnum.RecommendationError
	(RegionCodeErrorEnum_RegionCodeError)(0),                                   // 55: google.ads.googleads.v1.errors.RegionCodeErrorEnum.RegionCodeError
	(SettingErrorEnum_SettingError)(0),                                         // 56: google.ads.googleads.v1.errors.SettingErrorEnum.SettingError
	(StringFormatErrorEnum_StringFormatError)(0),                               // 57: google.ads.googleads.v1.errors.StringFormatErrorEnum.StringFormatError
	(StringLengthErrorEnum_StringLengthError)(0),                               // 58: google.ads.googleads.v1.errors.StringLengthErrorEnum.StringLengthError
	(OperationAccessDeniedErrorEnum_OperationAccessDeniedError)(0),             // 59: google.ads.googleads.v1.errors.OperationAccessDeniedErrorEnum.OperationAccessDeniedError
	(ResourceAccessDeniedErrorEnum_ResourceAccessDeniedError)(0),               // 60: google.ads.googleads.v1.errors.ResourceAccessDeniedErrorEnum.ResourceAccessDeniedError
	(ResourceCountLimitExceededErrorEnum_ResourceCountLimitExceededError)(0),   // 61: google.ads.googleads.v1.errors.ResourceCountLimitExceededErrorEnum.ResourceCountLimitExceededError
	(YoutubeVideoRegistrationErrorEnum_YoutubeVideoRegistrationError)(0),       // 62: google.ads.googleads.v1.errors.YoutubeVideoRegistrationErrorEnum.YoutubeVideoRegistrationError
	(AdGroupBidModifierErrorEnum_AdGroupBidModifierError)(0),                   // 63: google.ads.googleads.v1.errors.AdGroupBidModifierErrorEnum.AdGroupBidModifierError
	(ContextErrorEnum_ContextError)(0),                                         // 64: google.ads.googleads.v1.errors.ContextErrorEnum.ContextError
	(FieldErrorEnum_FieldError)(0),                                             // 65: google.ads.googleads.v1.errors.FieldErrorEnum.FieldError
	(SharedSetErrorEnum_SharedSetError)(0),                                     // 66: google.ads.googleads.v1.errors.SharedSetErrorEnum.SharedSetError
	(SharedCriterionErrorEnum_SharedCriterionError)(0),                         // 67: google.ads.googleads.v1.errors.SharedCriterionErrorEnum.SharedCriterionError
	(CampaignSharedSetErrorEnum_CampaignSharedSetError)(0),                     // 68: google.ads.googleads.v1.errors.CampaignSharedSetErrorEnum.CampaignSharedSetError
	(ConversionActionErrorEnum_ConversionActionError)(0),                       // 69: google.ads.googleads.v1.errors.ConversionActionErrorEnum.ConversionActionError
	(ConversionAdjustmentUploadErrorEnum_ConversionAdjustmentUploadError)(0),   // 70: google.ads.googleads.v1.errors.ConversionAdjustmentUploadErrorEnum.ConversionAdjustmentUploadError
	(ConversionUploadErrorEnum_ConversionUploadError)(0),                       // 71: google.ads.googleads.v1.errors.ConversionUploadErrorEnum.ConversionUploadError
	(HeaderErrorEnum_HeaderError)(0),                                           // 72: google.ads.googleads.v1.errors.HeaderErrorEnum.HeaderError
	(DatabaseErrorEnum_DatabaseError)(0),                                       // 73: google.ads.googleads.v1.errors.DatabaseErrorEnum.DatabaseError
	(PolicyFindingErrorEnum_PolicyFindingError)(0),                             // 74: google.ads.googleads.v1.errors.PolicyFindingErrorEnum.PolicyFindingError
	(EnumErrorEnum_EnumError)(0),                                               // 75: google.ads.googleads.v1.errors.EnumErrorEnum.EnumError
	(KeywordPlanErrorEnum_KeywordPlanError)(0),                                 // 76: google.ads.googleads.v1.errors.KeywordPlanErrorEnum.KeywordPlanError
	(KeywordPlanCampaignErrorEnum_KeywordPlanCampaignError)(0),                 // 77: google.ads.googleads.v1.errors.KeywordPlanCampaignErrorEnum.KeywordPlanCampaignError
	(KeywordPlanNegativeKeywordErrorEnum_KeywordPlanNegativeKeywordError)(0),   // 78: google.ads.googleads.v1.errors.KeywordPlanNegativeKeywordErrorEnum.KeywordPlanNegativeKeywordError
	(KeywordPlanAdGroupErrorEnum_KeywordPlanAdGroupError)(0),                   // 79: google.ads.googleads.v1.errors.KeywordPlanAdGroupErrorEnum.KeywordPlanAdGroupError
	(KeywordPlanKeywordErrorEnum_KeywordPlanKeywordError)(0),                   // 80: google.ads.googleads.v1.errors.KeywordPlanKeywordErrorEnum.KeywordPlanKeywordError
	(KeywordPlanIdeaErrorEnum_KeywordPlanIdeaError)(0),                         // 81: google.ads.googleads.v1.errors.KeywordPlanIdeaErrorEnum.KeywordPlanIdeaError
	(AccountBudgetProposalErrorEnum_AccountBudgetProposalError)(0),             // 82: google.ads.googleads.v1.errors.AccountBudgetProposalErrorEnum.AccountBudgetProposalError
	(UserListErrorEnum_UserListError)(0),                                       // 83: google.ads.googleads.v1.errors.UserListErrorEnum.UserListError
	(ChangeStatusErrorEnum_ChangeStatusError)(0),                               // 84: google.ads.googleads.v1.errors.ChangeStatusErrorEnum.ChangeStatusError
	(FeedErrorEnum_FeedError)(0),                                               // 85: google.ads.googleads.v1.errors.FeedErrorEnum.FeedError
	(GeoTargetConstantSuggestionErrorEnum_GeoTargetConstantSuggestionError)(0), // 86: google.ads.googleads.v1.errors.GeoTargetConstantSuggestionErrorEnum.GeoTargetConstantSuggestionError
	(CampaignDraftErrorEnum_CampaignDraftError)(0),                             // 87: google.ads.googleads.v1.errors.CampaignDraftErrorEnum.CampaignDraftError
	(FeedItemErrorEnum_FeedItemError)(0),                                       // 88: google.ads.googleads.v1.errors.FeedItemErrorEnum.FeedItemError
	(LabelErrorEnum_LabelError)(0),                                             // 89: google.ads.googleads.v1.errors.LabelErrorEnum.LabelError
	(BillingSetupErrorEnum_BillingSetupError)(0),                               // 90: google.ads.googleads.v1.errors.BillingSetupErrorEnum.BillingSetupError
	(CustomerClientLinkErrorEnum_CustomerClientLinkError)(0),                   // 91: google.ads.googleads.v1.errors.CustomerClientLinkErrorEnum.CustomerClientLinkError
	(CustomerManagerLinkErrorEnum_CustomerManagerLinkError)(0),                 // 92: google.ads.googleads.v1.errors.CustomerManagerLinkErrorEnum.CustomerManagerLinkError
	(FeedMappingErrorEnum_FeedMappingError)(0),                                 // 93: google.ads.googleads.v1.errors.FeedMappingErrorEnum.FeedMappingError
	(CustomerFeedErrorEnum_CustomerFeedError)(0),                               // 94: google.ads.googleads.v1.errors.CustomerFeedErrorEnum.CustomerFeedError
	(AdGroupFeedErrorEnum_AdGroupFeedError)(0),                                 // 95: google.ads.googleads.v1.errors.AdGroupFeedErrorEnum.AdGroupFeedError
	(CampaignFeedErrorEnum_CampaignFeedError)(0),                               // 96: google.ads.googleads.v1.errors.CampaignFeedErrorEnum.CampaignFeedError
	(CustomInterestErrorEnum_CustomInterestError)(0),                           // 97: google.ads.googleads.v1.errors.CustomInterestErrorEnum.CustomInterestError
	(CampaignExperimentErrorEnum_CampaignExperimentError)(0),                   // 98: google.ads.googleads.v1.errors.CampaignExperimentErrorEnum.CampaignExperimentError
	(ExtensionFeedItemErrorEnum_ExtensionFeedItemError)(0),                     // 99: google.ads.googleads.v1.errors.ExtensionFeedItemErrorEnum.ExtensionFeedItemError
	(AdParameterErrorEnum_AdParameterError)(0),                                 // 100: google.ads.googleads.v1.errors.AdParameterErrorEnum.AdParameterError
	(FeedItemValidationErrorEnum_FeedItemValidationError)(0),                   // 101: google.ads.googleads.v1.errors.FeedItemValidationErrorEnum.FeedItemValidationError
	(ExtensionSettingErrorEnum_ExtensionSettingError)(0),                       // 102: google.ads.googleads.v1.errors.ExtensionSettingErrorEnum.ExtensionSettingError
	(FeedItemTargetErrorEnum_FeedItemTargetError)(0),                           // 103: google.ads.googleads.v1.errors.FeedItemTargetErrorEnum.FeedItemTargetError
	(PolicyViolationErrorEnum_PolicyViolationError)(0),                         // 104: google.ads.googleads.v1.errors.PolicyViolationErrorEnum.PolicyViolationError
	(MutateJobErrorEnum_MutateJobError)(0),                                     // 105: google.ads.googleads.v1.errors.MutateJobErrorEnum.MutateJobError
	(PartialFailureErrorEnum_PartialFailureError)(0),                           // 106: google.ads.googleads.v1.errors.PartialFailureErrorEnum.PartialFailureError
	(PolicyValidationParameterErrorEnum_PolicyValidationParameterError)(0),     // 107: google.ads.googleads.v1.errors.PolicyValidationParameterErrorEnum.PolicyValidationParameterError
	(SizeLimitErrorEnum_SizeLimitError)(0),                                     // 108: google.ads.googleads.v1.errors.SizeLimitErrorEnum.SizeLimitError
	(NotWhitelistedErrorEnum_NotWhitelistedError)(0),                           // 109: google.ads.googleads.v1.errors.NotWhitelistedErrorEnum.NotWhitelistedError
	(ManagerLinkErrorEnum_ManagerLinkError)(0),                                 // 110: google.ads.googleads.v1.errors.ManagerLinkErrorEnum.ManagerLinkError
	(*common.PolicyViolationKey)(nil),                                          // 111: google.ads.googleads.v1.common.PolicyViolationKey
	(*common.PolicyTopicEntry)(nil),                                            // 112: google.ads.googleads.v1.common.PolicyTopicEntry
	(*wrappers.Int64Value)(nil),                                                // 113: google.protobuf.Int64Value
}
var file_google_ads_googleads_v1_errors_errors_proto_depIdxs = []int32{
	1,   // 0: google.ads.googleads.v1.errors.GoogleAdsFailure.errors:type_name -> google.ads.googleads.v1.errors.GoogleAdsError
	2,   // 1: google.ads.googleads.v1.errors.GoogleAdsError.error_code:type_name -> google.ads.googleads.v1.errors.ErrorCode
	8,   // 2: google.ads.googleads.v1.errors.GoogleAdsError.trigger:type_name -> google.ads.googleads.v1.common.Value
	3,   // 3: google.ads.googleads.v1.errors.GoogleAdsError.location:type_name -> google.ads.googleads.v1.errors.ErrorLocation
	4,   // 4: google.ads.googleads.v1.errors.GoogleAdsError.details:type_name -> google.ads.googleads.v1.errors.ErrorDetails
	9,   // 5: google.ads.googleads.v1.errors.ErrorCode.request_error:type_name -> google.ads.googleads.v1.errors.RequestErrorEnum.RequestError
	10,  // 6: google.ads.googleads.v1.errors.ErrorCode.bidding_strategy_error:type_name -> google.ads.googleads.v1.errors.BiddingStrategyErrorEnum.BiddingStrategyError
	11,  // 7: google.ads.googleads.v1.errors.ErrorCode.url_field_error:type_name -> google.ads.googleads.v1.errors.UrlFieldErrorEnum.UrlFieldError
	12,  // 8: google.ads.googleads.v1.errors.ErrorCode.list_operation_error:type_name -> google.ads.googleads.v1.errors.ListOperationErrorEnum.ListOperationError
	13,  // 9: google.ads.googleads.v1.errors.ErrorCode.query_error:type_name -> google.ads.googleads.v1.errors.QueryErrorEnum.QueryError
	14,  // 10: google.ads.googleads.v1.errors.ErrorCode.mutate_error:type_name -> google.ads.googleads.v1.errors.MutateErrorEnum.MutateError
	15,  // 11: google.ads.googleads.v1.errors.ErrorCode.field_mask_error:type_name -> google.ads.googleads.v1.errors.FieldMaskErrorEnum.FieldMaskError
	16,  // 12: google.ads.googleads.v1.errors.ErrorCode.authorization_error:type_name -> google.ads.googleads.v1.errors.AuthorizationErrorEnum.AuthorizationError
	17,  // 13: google.ads.googleads.v1.errors.ErrorCode.internal_error:type_name -> google.ads.googleads.v1.errors.InternalErrorEnum.InternalError
	18,  // 14: google.ads.googleads.v1.errors.ErrorCode.quota_error:type_name -> google.ads.googleads.v1.errors.QuotaErrorEnum.QuotaError
	19,  // 15: google.ads.googleads.v1.errors.ErrorCode.ad_error:type_name -> google.ads.googleads.v1.errors.AdErrorEnum.AdError
	20,  // 16: google.ads.googleads.v1.errors.ErrorCode.ad_group_error:type_name -> google.ads.googleads.v1.errors.AdGroupErrorEnum.AdGroupError
	21,  // 17: google.ads.googleads.v1.errors.ErrorCode.campaign_budget_error:type_name -> google.ads.googleads.v1.errors.CampaignBudgetErrorEnum.CampaignBudgetError
	22,  // 18: google.ads.googleads.v1.errors.ErrorCode.campaign_error:type_name -> google.ads.googleads.v1.errors.CampaignErrorEnum.CampaignError
	23,  // 19: google.ads.googleads.v1.errors.ErrorCode.authentication_error:type_name -> google.ads.googleads.v1.errors.AuthenticationErrorEnum.AuthenticationError
	24,  // 20: google.ads.googleads.v1.errors.ErrorCode.ad_group_criterion_error:type_name -> google.ads.googleads.v1.errors.AdGroupCriterionErrorEnum.AdGroupCriterionError
	25,  // 21: google.ads.googleads.v1.errors.ErrorCode.ad_customizer_error:type_name -> google.ads.googleads.v1.errors.AdCustomizerErrorEnum.AdCustomizerError
	26,  // 22: google.ads.googleads.v1.errors.ErrorCode.ad_group_ad_error:type_name -> google.ads.googleads.v1.errors.AdGroupAdErrorEnum.AdGroupAdError
	27,  // 23: google.ads.googleads.v1.errors.ErrorCode.ad_sharing_error:type_name -> google.ads.googleads.v1.errors.AdSharingErrorEnum.AdSharingError
	28,  // 24: google.ads.googleads.v1.errors.ErrorCode.adx_error:type_name -> google.ads.googleads.v1.errors.AdxErrorEnum.AdxError
	29,  // 25: google.ads.googleads.v1.errors.ErrorCode.asset_error:type_name -> google.ads.googleads.v1.errors.AssetErrorEnum.AssetError
	30,  // 26: google.ads.googleads.v1.errors.ErrorCode.bidding_error:type_name -> google.ads.googleads.v1.errors.BiddingErrorEnum.BiddingError
	31,  // 27: google.ads.googleads.v1.errors.ErrorCode.campaign_criterion_error:type_name -> google.ads.googleads.v1.errors.CampaignCriterionErrorEnum.CampaignCriterionError
	32,  // 28: google.ads.googleads.v1.errors.ErrorCode.collection_size_error:type_name -> google.ads.googleads.v1.errors.CollectionSizeErrorEnum.CollectionSizeError
	33,  // 29: google.ads.googleads.v1.errors.ErrorCode.country_code_error:type_name -> google.ads.googleads.v1.errors.CountryCodeErrorEnum.CountryCodeError
	34,  // 30: google.ads.googleads.v1.errors.ErrorCode.criterion_error:type_name -> google.ads.googleads.v1.errors.CriterionErrorEnum.CriterionError
	35,  // 31: google.ads.googleads.v1.errors.ErrorCode.customer_error:type_name -> google.ads.googleads.v1.errors.CustomerErrorEnum.CustomerError
	36,  // 32: google.ads.googleads.v1.errors.ErrorCode.date_error:type_name -> google.ads.googleads.v1.errors.DateErrorEnum.DateError
	37,  // 33: google.ads.googleads.v1.errors.ErrorCode.date_range_error:type_name -> google.ads.googleads.v1.errors.DateRangeErrorEnum.DateRangeError
	38,  // 34: google.ads.googleads.v1.errors.ErrorCode.distinct_error:type_name -> google.ads.googleads.v1.errors.DistinctErrorEnum.DistinctError
	39,  // 35: google.ads.googleads.v1.errors.ErrorCode.feed_attribute_reference_error:type_name -> google.ads.googleads.v1.errors.FeedAttributeReferenceErrorEnum.FeedAttributeReferenceError
	40,  // 36: google.ads.googleads.v1.errors.ErrorCode.function_error:type_name -> google.ads.googleads.v1.errors.FunctionErrorEnum.FunctionError
	41,  // 37: google.ads.googleads.v1.errors.ErrorCode.function_parsing_error:type_name -> google.ads.googleads.v1.errors.FunctionParsingErrorEnum.FunctionParsingError
	42,  // 38: google.ads.googleads.v1.errors.ErrorCode.id_error:type_name -> google.ads.googleads.v1.errors.IdErrorEnum.IdError
	43,  // 39: google.ads.googleads.v1.errors.ErrorCode.image_error:type_name -> google.ads.googleads.v1.errors.ImageErrorEnum.ImageError
	44,  // 40: google.ads.googleads.v1.errors.ErrorCode.language_code_error:type_name -> google.ads.googleads.v1.errors.LanguageCodeErrorEnum.LanguageCodeError
	45,  // 41: google.ads.googleads.v1.errors.ErrorCode.media_bundle_error:type_name -> google.ads.googleads.v1.errors.MediaBundleErrorEnum.MediaBundleError
	46,  // 42: google.ads.googleads.v1.errors.ErrorCode.media_upload_error:type_name -> google.ads.googleads.v1.errors.MediaUploadErrorEnum.MediaUploadError
	47,  // 43: google.ads.googleads.v1.errors.ErrorCode.media_file_error:type_name -> google.ads.googleads.v1.errors.MediaFileErrorEnum.MediaFileError
	48,  // 44: google.ads.googleads.v1.errors.ErrorCode.multiplier_error:type_name -> google.ads.googleads.v1.errors.MultiplierErrorEnum.MultiplierError
	49,  // 45: google.ads.googleads.v1.errors.ErrorCode.new_resource_creation_error:type_name -> google.ads.googleads.v1.errors.NewResourceCreationErrorEnum.NewResourceCreationError
	50,  // 46: google.ads.googleads.v1.errors.ErrorCode.not_empty_error:type_name -> google.ads.googleads.v1.errors.NotEmptyErrorEnum.NotEmptyError
	51,  // 47: google.ads.googleads.v1.errors.ErrorCode.null_error:type_name -> google.ads.googleads.v1.errors.NullErrorEnum.NullError
	52,  // 48: google.ads.googleads.v1.errors.ErrorCode.operator_error:type_name -> google.ads.googleads.v1.errors.OperatorErrorEnum.OperatorError
	53,  // 49: google.ads.googleads.v1.errors.ErrorCode.range_error:type_name -> google.ads.googleads.v1.errors.RangeErrorEnum.RangeError
	54,  // 50: google.ads.googleads.v1.errors.ErrorCode.recommendation_error:type_name -> google.ads.googleads.v1.errors.RecommendationErrorEnum.RecommendationError
	55,  // 51: google.ads.googleads.v1.errors.ErrorCode.region_code_error:type_name -> google.ads.googleads.v1.errors.RegionCodeErrorEnum.RegionCodeError
	56,  // 52: google.ads.googleads.v1.errors.ErrorCode.setting_error:type_name -> google.ads.googleads.v1.errors.SettingErrorEnum.SettingError
	57,  // 53: google.ads.googleads.v1.errors.ErrorCode.string_format_error:type_name -> google.ads.googleads.v1.errors.StringFormatErrorEnum.StringFormatError
	58,  // 54: google.ads.googleads.v1.errors.ErrorCode.string_length_error:type_name -> google.ads.googleads.v1.errors.StringLengthErrorEnum.StringLengthError
	59,  // 55: google.ads.googleads.v1.errors.ErrorCode.operation_access_denied_error:type_name -> google.ads.googleads.v1.errors.OperationAccessDeniedErrorEnum.OperationAccessDeniedError
	60,  // 56: google.ads.googleads.v1.errors.ErrorCode.resource_access_denied_error:type_name -> google.ads.googleads.v1.errors.ResourceAccessDeniedErrorEnum.ResourceAccessDeniedError
	61,  // 57: google.ads.googleads.v1.errors.ErrorCode.resource_count_limit_exceeded_error:type_name -> google.ads.googleads.v1.errors.ResourceCountLimitExceededErrorEnum.ResourceCountLimitExceededError
	62,  // 58: google.ads.googleads.v1.errors.ErrorCode.youtube_video_registration_error:type_name -> google.ads.googleads.v1.errors.YoutubeVideoRegistrationErrorEnum.YoutubeVideoRegistrationError
	63,  // 59: google.ads.googleads.v1.errors.ErrorCode.ad_group_bid_modifier_error:type_name -> google.ads.googleads.v1.errors.AdGroupBidModifierErrorEnum.AdGroupBidModifierError
	64,  // 60: google.ads.googleads.v1.errors.ErrorCode.context_error:type_name -> google.ads.googleads.v1.errors.ContextErrorEnum.ContextError
	65,  // 61: google.ads.googleads.v1.errors.ErrorCode.field_error:type_name -> google.ads.googleads.v1.errors.FieldErrorEnum.FieldError
	66,  // 62: google.ads.googleads.v1.errors.ErrorCode.shared_set_error:type_name -> google.ads.googleads.v1.errors.SharedSetErrorEnum.SharedSetError
	67,  // 63: google.ads.googleads.v1.errors.ErrorCode.shared_criterion_error:type_name -> google.ads.googleads.v1.errors.SharedCriterionErrorEnum.SharedCriterionError
	68,  // 64: google.ads.googleads.v1.errors.ErrorCode.campaign_shared_set_error:type_name -> google.ads.googleads.v1.errors.CampaignSharedSetErrorEnum.CampaignSharedSetError
	69,  // 65: google.ads.googleads.v1.errors.ErrorCode.conversion_action_error:type_name -> google.ads.googleads.v1.errors.ConversionActionErrorEnum.ConversionActionError
	70,  // 66: google.ads.googleads.v1.errors.ErrorCode.conversion_adjustment_upload_error:type_name -> google.ads.googleads.v1.errors.ConversionAdjustmentUploadErrorEnum.ConversionAdjustmentUploadError
	71,  // 67: google.ads.googleads.v1.errors.ErrorCode.conversion_upload_error:type_name -> google.ads.googleads.v1.errors.ConversionUploadErrorEnum.ConversionUploadError
	72,  // 68: google.ads.googleads.v1.errors.ErrorCode.header_error:type_name -> google.ads.googleads.v1.errors.HeaderErrorEnum.HeaderError
	73,  // 69: google.ads.googleads.v1.errors.ErrorCode.database_error:type_name -> google.ads.googleads.v1.errors.DatabaseErrorEnum.DatabaseError
	74,  // 70: google.ads.googleads.v1.errors.ErrorCode.policy_finding_error:type_name -> google.ads.googleads.v1.errors.PolicyFindingErrorEnum.PolicyFindingError
	75,  // 71: google.ads.googleads.v1.errors.ErrorCode.enum_error:type_name -> google.ads.googleads.v1.errors.EnumErrorEnum.EnumError
	76,  // 72: google.ads.googleads.v1.errors.ErrorCode.keyword_plan_error:type_name -> google.ads.googleads.v1.errors.KeywordPlanErrorEnum.KeywordPlanError
	77,  // 73: google.ads.googleads.v1.errors.ErrorCode.keyword_plan_campaign_error:type_name -> google.ads.googleads.v1.errors.KeywordPlanCampaignErrorEnum.KeywordPlanCampaignError
	78,  // 74: google.ads.googleads.v1.errors.ErrorCode.keyword_plan_negative_keyword_error:type_name -> google.ads.googleads.v1.errors.KeywordPlanNegativeKeywordErrorEnum.KeywordPlanNegativeKeywordError
	79,  // 75: google.ads.googleads.v1.errors.ErrorCode.keyword_plan_ad_group_error:type_name -> google.ads.googleads.v1.errors.KeywordPlanAdGroupErrorEnum.KeywordPlanAdGroupError
	80,  // 76: google.ads.googleads.v1.errors.ErrorCode.keyword_plan_keyword_error:type_name -> google.ads.googleads.v1.errors.KeywordPlanKeywordErrorEnum.KeywordPlanKeywordError
	81,  // 77: google.ads.googleads.v1.errors.ErrorCode.keyword_plan_idea_error:type_name -> google.ads.googleads.v1.errors.KeywordPlanIdeaErrorEnum.KeywordPlanIdeaError
	82,  // 78: google.ads.googleads.v1.errors.ErrorCode.account_budget_proposal_error:type_name -> google.ads.googleads.v1.errors.AccountBudgetProposalErrorEnum.AccountBudgetProposalError
	83,  // 79: google.ads.googleads.v1.errors.ErrorCode.user_list_error:type_name -> google.ads.googleads.v1.errors.UserListErrorEnum.UserListError
	84,  // 80: google.ads.googleads.v1.errors.ErrorCode.change_status_error:type_name -> google.ads.googleads.v1.errors.ChangeStatusErrorEnum.ChangeStatusError
	85,  // 81: google.ads.googleads.v1.errors.ErrorCode.feed_error:type_name -> google.ads.googleads.v1.errors.FeedErrorEnum.FeedError
	86,  // 82: google.ads.googleads.v1.errors.ErrorCode.geo_target_constant_suggestion_error:type_name -> google.ads.googleads.v1.errors.GeoTargetConstantSuggestionErrorEnum.GeoTargetConstantSuggestionError
	87,  // 83: google.ads.googleads.v1.errors.ErrorCode.campaign_draft_error:type_name -> google.ads.googleads.v1.errors.CampaignDraftErrorEnum.CampaignDraftError
	88,  // 84: google.ads.googleads.v1.errors.ErrorCode.feed_item_error:type_name -> google.ads.googleads.v1.errors.FeedItemErrorEnum.FeedItemError
	89,  // 85: google.ads.googleads.v1.errors.ErrorCode.label_error:type_name -> google.ads.googleads.v1.errors.LabelErrorEnum.LabelError
	90,  // 86: google.ads.googleads.v1.errors.ErrorCode.billing_setup_error:type_name -> google.ads.googleads.v1.errors.BillingSetupErrorEnum.BillingSetupError
	91,  // 87: google.ads.googleads.v1.errors.ErrorCode.customer_client_link_error:type_name -> google.ads.googleads.v1.errors.CustomerClientLinkErrorEnum.CustomerClientLinkError
	92,  // 88: google.ads.googleads.v1.errors.ErrorCode.customer_manager_link_error:type_name -> google.ads.googleads.v1.errors.CustomerManagerLinkErrorEnum.CustomerManagerLinkError
	93,  // 89: google.ads.googleads.v1.errors.ErrorCode.feed_mapping_error:type_name -> google.ads.googleads.v1.errors.FeedMappingErrorEnum.FeedMappingError
	94,  // 90: google.ads.googleads.v1.errors.ErrorCode.customer_feed_error:type_name -> google.ads.googleads.v1.errors.CustomerFeedErrorEnum.CustomerFeedError
	95,  // 91: google.ads.googleads.v1.errors.ErrorCode.ad_group_feed_error:type_name -> google.ads.googleads.v1.errors.AdGroupFeedErrorEnum.AdGroupFeedError
	96,  // 92: google.ads.googleads.v1.errors.ErrorCode.campaign_feed_error:type_name -> google.ads.googleads.v1.errors.CampaignFeedErrorEnum.CampaignFeedError
	97,  // 93: google.ads.googleads.v1.errors.ErrorCode.custom_interest_error:type_name -> google.ads.googleads.v1.errors.CustomInterestErrorEnum.CustomInterestError
	98,  // 94: google.ads.googleads.v1.errors.ErrorCode.campaign_experiment_error:type_name -> google.ads.googleads.v1.errors.CampaignExperimentErrorEnum.CampaignExperimentError
	99,  // 95: google.ads.googleads.v1.errors.ErrorCode.extension_feed_item_error:type_name -> google.ads.googleads.v1.errors.ExtensionFeedItemErrorEnum.ExtensionFeedItemError
	100, // 96: google.ads.googleads.v1.errors.ErrorCode.ad_parameter_error:type_name -> google.ads.googleads.v1.errors.AdParameterErrorEnum.AdParameterError
	101, // 97: google.ads.googleads.v1.errors.ErrorCode.feed_item_validation_error:type_name -> google.ads.googleads.v1.errors.FeedItemValidationErrorEnum.FeedItemValidationError
	102, // 98: google.ads.googleads.v1.errors.ErrorCode.extension_setting_error:type_name -> google.ads.googleads.v1.errors.ExtensionSettingErrorEnum.ExtensionSettingError
	103, // 99: google.ads.googleads.v1.errors.ErrorCode.feed_item_target_error:type_name -> google.ads.googleads.v1.errors.FeedItemTargetErrorEnum.FeedItemTargetError
	104, // 100: google.ads.googleads.v1.errors.ErrorCode.policy_violation_error:type_name -> google.ads.googleads.v1.errors.PolicyViolationErrorEnum.PolicyViolationError
	105, // 101: google.ads.googleads.v1.errors.ErrorCode.mutate_job_error:type_name -> google.ads.googleads.v1.errors.MutateJobErrorEnum.MutateJobError
	106, // 102: google.ads.googleads.v1.errors.ErrorCode.partial_failure_error:type_name -> google.ads.googleads.v1.errors.PartialFailureErrorEnum.PartialFailureError
	107, // 103: google.ads.googleads.v1.errors.ErrorCode.policy_validation_parameter_error:type_name -> google.ads.googleads.v1.errors.PolicyValidationParameterErrorEnum.PolicyValidationParameterError
	108, // 104: google.ads.googleads.v1.errors.ErrorCode.size_limit_error:type_name -> google.ads.googleads.v1.errors.SizeLimitErrorEnum.SizeLimitError
	109, // 105: google.ads.googleads.v1.errors.ErrorCode.not_whitelisted_error:type_name -> google.ads.googleads.v1.errors.NotWhitelistedErrorEnum.NotWhitelistedError
	110, // 106: google.ads.googleads.v1.errors.ErrorCode.manager_link_error:type_name -> google.ads.googleads.v1.errors.ManagerLinkErrorEnum.ManagerLinkError
	7,   // 107: google.ads.googleads.v1.errors.ErrorLocation.field_path_elements:type_name -> google.ads.googleads.v1.errors.ErrorLocation.FieldPathElement
	5,   // 108: google.ads.googleads.v1.errors.ErrorDetails.policy_violation_details:type_name -> google.ads.googleads.v1.errors.PolicyViolationDetails
	6,   // 109: google.ads.googleads.v1.errors.ErrorDetails.policy_finding_details:type_name -> google.ads.googleads.v1.errors.PolicyFindingDetails
	111, // 110: google.ads.googleads.v1.errors.PolicyViolationDetails.key:type_name -> google.ads.googleads.v1.common.PolicyViolationKey
	112, // 111: google.ads.googleads.v1.errors.PolicyFindingDetails.policy_topic_entries:type_name -> google.ads.googleads.v1.common.PolicyTopicEntry
	113, // 112: google.ads.googleads.v1.errors.ErrorLocation.FieldPathElement.index:type_name -> google.protobuf.Int64Value
	113, // [113:113] is the sub-list for method output_type
	113, // [113:113] is the sub-list for method input_type
	113, // [113:113] is the sub-list for extension type_name
	113, // [113:113] is the sub-list for extension extendee
	0,   // [0:113] is the sub-list for field type_name
}

func init() { file_google_ads_googleads_v1_errors_errors_proto_init() }
func file_google_ads_googleads_v1_errors_errors_proto_init() {
	if File_google_ads_googleads_v1_errors_errors_proto != nil {
		return
	}
	file_google_ads_googleads_v1_errors_account_budget_proposal_error_proto_init()
	file_google_ads_googleads_v1_errors_ad_customizer_error_proto_init()
	file_google_ads_googleads_v1_errors_ad_error_proto_init()
	file_google_ads_googleads_v1_errors_ad_group_ad_error_proto_init()
	file_google_ads_googleads_v1_errors_ad_group_bid_modifier_error_proto_init()
	file_google_ads_googleads_v1_errors_ad_group_criterion_error_proto_init()
	file_google_ads_googleads_v1_errors_ad_group_error_proto_init()
	file_google_ads_googleads_v1_errors_ad_group_feed_error_proto_init()
	file_google_ads_googleads_v1_errors_ad_parameter_error_proto_init()
	file_google_ads_googleads_v1_errors_ad_sharing_error_proto_init()
	file_google_ads_googleads_v1_errors_adx_error_proto_init()
	file_google_ads_googleads_v1_errors_asset_error_proto_init()
	file_google_ads_googleads_v1_errors_authentication_error_proto_init()
	file_google_ads_googleads_v1_errors_authorization_error_proto_init()
	file_google_ads_googleads_v1_errors_bidding_error_proto_init()
	file_google_ads_googleads_v1_errors_bidding_strategy_error_proto_init()
	file_google_ads_googleads_v1_errors_billing_setup_error_proto_init()
	file_google_ads_googleads_v1_errors_campaign_budget_error_proto_init()
	file_google_ads_googleads_v1_errors_campaign_criterion_error_proto_init()
	file_google_ads_googleads_v1_errors_campaign_draft_error_proto_init()
	file_google_ads_googleads_v1_errors_campaign_error_proto_init()
	file_google_ads_googleads_v1_errors_campaign_experiment_error_proto_init()
	file_google_ads_googleads_v1_errors_campaign_feed_error_proto_init()
	file_google_ads_googleads_v1_errors_campaign_shared_set_error_proto_init()
	file_google_ads_googleads_v1_errors_change_status_error_proto_init()
	file_google_ads_googleads_v1_errors_collection_size_error_proto_init()
	file_google_ads_googleads_v1_errors_context_error_proto_init()
	file_google_ads_googleads_v1_errors_conversion_action_error_proto_init()
	file_google_ads_googleads_v1_errors_conversion_adjustment_upload_error_proto_init()
	file_google_ads_googleads_v1_errors_conversion_upload_error_proto_init()
	file_google_ads_googleads_v1_errors_country_code_error_proto_init()
	file_google_ads_googleads_v1_errors_criterion_error_proto_init()
	file_google_ads_googleads_v1_errors_custom_interest_error_proto_init()
	file_google_ads_googleads_v1_errors_customer_client_link_error_proto_init()
	file_google_ads_googleads_v1_errors_customer_error_proto_init()
	file_google_ads_googleads_v1_errors_customer_feed_error_proto_init()
	file_google_ads_googleads_v1_errors_customer_manager_link_error_proto_init()
	file_google_ads_googleads_v1_errors_database_error_proto_init()
	file_google_ads_googleads_v1_errors_date_error_proto_init()
	file_google_ads_googleads_v1_errors_date_range_error_proto_init()
	file_google_ads_googleads_v1_errors_distinct_error_proto_init()
	file_google_ads_googleads_v1_errors_enum_error_proto_init()
	file_google_ads_googleads_v1_errors_extension_feed_item_error_proto_init()
	file_google_ads_googleads_v1_errors_extension_setting_error_proto_init()
	file_google_ads_googleads_v1_errors_feed_attribute_reference_error_proto_init()
	file_google_ads_googleads_v1_errors_feed_error_proto_init()
	file_google_ads_googleads_v1_errors_feed_item_error_proto_init()
	file_google_ads_googleads_v1_errors_feed_item_target_error_proto_init()
	file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_init()
	file_google_ads_googleads_v1_errors_feed_mapping_error_proto_init()
	file_google_ads_googleads_v1_errors_field_error_proto_init()
	file_google_ads_googleads_v1_errors_field_mask_error_proto_init()
	file_google_ads_googleads_v1_errors_function_error_proto_init()
	file_google_ads_googleads_v1_errors_function_parsing_error_proto_init()
	file_google_ads_googleads_v1_errors_geo_target_constant_suggestion_error_proto_init()
	file_google_ads_googleads_v1_errors_header_error_proto_init()
	file_google_ads_googleads_v1_errors_id_error_proto_init()
	file_google_ads_googleads_v1_errors_image_error_proto_init()
	file_google_ads_googleads_v1_errors_internal_error_proto_init()
	file_google_ads_googleads_v1_errors_keyword_plan_ad_group_error_proto_init()
	file_google_ads_googleads_v1_errors_keyword_plan_campaign_error_proto_init()
	file_google_ads_googleads_v1_errors_keyword_plan_error_proto_init()
	file_google_ads_googleads_v1_errors_keyword_plan_idea_error_proto_init()
	file_google_ads_googleads_v1_errors_keyword_plan_keyword_error_proto_init()
	file_google_ads_googleads_v1_errors_keyword_plan_negative_keyword_error_proto_init()
	file_google_ads_googleads_v1_errors_label_error_proto_init()
	file_google_ads_googleads_v1_errors_language_code_error_proto_init()
	file_google_ads_googleads_v1_errors_list_operation_error_proto_init()
	file_google_ads_googleads_v1_errors_manager_link_error_proto_init()
	file_google_ads_googleads_v1_errors_media_bundle_error_proto_init()
	file_google_ads_googleads_v1_errors_media_file_error_proto_init()
	file_google_ads_googleads_v1_errors_media_upload_error_proto_init()
	file_google_ads_googleads_v1_errors_multiplier_error_proto_init()
	file_google_ads_googleads_v1_errors_mutate_error_proto_init()
	file_google_ads_googleads_v1_errors_mutate_job_error_proto_init()
	file_google_ads_googleads_v1_errors_new_resource_creation_error_proto_init()
	file_google_ads_googleads_v1_errors_not_empty_error_proto_init()
	file_google_ads_googleads_v1_errors_not_whitelisted_error_proto_init()
	file_google_ads_googleads_v1_errors_null_error_proto_init()
	file_google_ads_googleads_v1_errors_operation_access_denied_error_proto_init()
	file_google_ads_googleads_v1_errors_operator_error_proto_init()
	file_google_ads_googleads_v1_errors_partial_failure_error_proto_init()
	file_google_ads_googleads_v1_errors_policy_finding_error_proto_init()
	file_google_ads_googleads_v1_errors_policy_validation_parameter_error_proto_init()
	file_google_ads_googleads_v1_errors_policy_violation_error_proto_init()
	file_google_ads_googleads_v1_errors_query_error_proto_init()
	file_google_ads_googleads_v1_errors_quota_error_proto_init()
	file_google_ads_googleads_v1_errors_range_error_proto_init()
	file_google_ads_googleads_v1_errors_recommendation_error_proto_init()
	file_google_ads_googleads_v1_errors_region_code_error_proto_init()
	file_google_ads_googleads_v1_errors_request_error_proto_init()
	file_google_ads_googleads_v1_errors_resource_access_denied_error_proto_init()
	file_google_ads_googleads_v1_errors_resource_count_limit_exceeded_error_proto_init()
	file_google_ads_googleads_v1_errors_setting_error_proto_init()
	file_google_ads_googleads_v1_errors_shared_criterion_error_proto_init()
	file_google_ads_googleads_v1_errors_shared_set_error_proto_init()
	file_google_ads_googleads_v1_errors_size_limit_error_proto_init()
	file_google_ads_googleads_v1_errors_string_format_error_proto_init()
	file_google_ads_googleads_v1_errors_string_length_error_proto_init()
	file_google_ads_googleads_v1_errors_url_field_error_proto_init()
	file_google_ads_googleads_v1_errors_user_list_error_proto_init()
	file_google_ads_googleads_v1_errors_youtube_video_registration_error_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_ads_googleads_v1_errors_errors_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoogleAdsFailure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_errors_errors_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoogleAdsError); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_errors_errors_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErrorCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_errors_errors_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErrorLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_errors_errors_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErrorDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_errors_errors_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolicyViolationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_errors_errors_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolicyFindingDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_errors_errors_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErrorLocation_FieldPathElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_ads_googleads_v1_errors_errors_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*ErrorCode_RequestError)(nil),
		(*ErrorCode_BiddingStrategyError)(nil),
		(*ErrorCode_UrlFieldError)(nil),
		(*ErrorCode_ListOperationError)(nil),
		(*ErrorCode_QueryError)(nil),
		(*ErrorCode_MutateError)(nil),
		(*ErrorCode_FieldMaskError)(nil),
		(*ErrorCode_AuthorizationError)(nil),
		(*ErrorCode_InternalError)(nil),
		(*ErrorCode_QuotaError)(nil),
		(*ErrorCode_AdError)(nil),
		(*ErrorCode_AdGroupError)(nil),
		(*ErrorCode_CampaignBudgetError)(nil),
		(*ErrorCode_CampaignError)(nil),
		(*ErrorCode_AuthenticationError)(nil),
		(*ErrorCode_AdGroupCriterionError)(nil),
		(*ErrorCode_AdCustomizerError)(nil),
		(*ErrorCode_AdGroupAdError)(nil),
		(*ErrorCode_AdSharingError)(nil),
		(*ErrorCode_AdxError)(nil),
		(*ErrorCode_AssetError)(nil),
		(*ErrorCode_BiddingError)(nil),
		(*ErrorCode_CampaignCriterionError)(nil),
		(*ErrorCode_CollectionSizeError)(nil),
		(*ErrorCode_CountryCodeError)(nil),
		(*ErrorCode_CriterionError)(nil),
		(*ErrorCode_CustomerError)(nil),
		(*ErrorCode_DateError)(nil),
		(*ErrorCode_DateRangeError)(nil),
		(*ErrorCode_DistinctError)(nil),
		(*ErrorCode_FeedAttributeReferenceError)(nil),
		(*ErrorCode_FunctionError)(nil),
		(*ErrorCode_FunctionParsingError)(nil),
		(*ErrorCode_IdError)(nil),
		(*ErrorCode_ImageError)(nil),
		(*ErrorCode_LanguageCodeError)(nil),
		(*ErrorCode_MediaBundleError)(nil),
		(*ErrorCode_MediaUploadError)(nil),
		(*ErrorCode_MediaFileError)(nil),
		(*ErrorCode_MultiplierError)(nil),
		(*ErrorCode_NewResourceCreationError)(nil),
		(*ErrorCode_NotEmptyError)(nil),
		(*ErrorCode_NullError)(nil),
		(*ErrorCode_OperatorError)(nil),
		(*ErrorCode_RangeError)(nil),
		(*ErrorCode_RecommendationError)(nil),
		(*ErrorCode_RegionCodeError)(nil),
		(*ErrorCode_SettingError)(nil),
		(*ErrorCode_StringFormatError)(nil),
		(*ErrorCode_StringLengthError)(nil),
		(*ErrorCode_OperationAccessDeniedError)(nil),
		(*ErrorCode_ResourceAccessDeniedError)(nil),
		(*ErrorCode_ResourceCountLimitExceededError)(nil),
		(*ErrorCode_YoutubeVideoRegistrationError)(nil),
		(*ErrorCode_AdGroupBidModifierError)(nil),
		(*ErrorCode_ContextError)(nil),
		(*ErrorCode_FieldError)(nil),
		(*ErrorCode_SharedSetError)(nil),
		(*ErrorCode_SharedCriterionError)(nil),
		(*ErrorCode_CampaignSharedSetError)(nil),
		(*ErrorCode_ConversionActionError)(nil),
		(*ErrorCode_ConversionAdjustmentUploadError)(nil),
		(*ErrorCode_ConversionUploadError)(nil),
		(*ErrorCode_HeaderError)(nil),
		(*ErrorCode_DatabaseError)(nil),
		(*ErrorCode_PolicyFindingError)(nil),
		(*ErrorCode_EnumError)(nil),
		(*ErrorCode_KeywordPlanError)(nil),
		(*ErrorCode_KeywordPlanCampaignError)(nil),
		(*ErrorCode_KeywordPlanNegativeKeywordError)(nil),
		(*ErrorCode_KeywordPlanAdGroupError)(nil),
		(*ErrorCode_KeywordPlanKeywordError)(nil),
		(*ErrorCode_KeywordPlanIdeaError)(nil),
		(*ErrorCode_AccountBudgetProposalError)(nil),
		(*ErrorCode_UserListError)(nil),
		(*ErrorCode_ChangeStatusError)(nil),
		(*ErrorCode_FeedError)(nil),
		(*ErrorCode_GeoTargetConstantSuggestionError)(nil),
		(*ErrorCode_CampaignDraftError)(nil),
		(*ErrorCode_FeedItemError)(nil),
		(*ErrorCode_LabelError)(nil),
		(*ErrorCode_BillingSetupError)(nil),
		(*ErrorCode_CustomerClientLinkError)(nil),
		(*ErrorCode_CustomerManagerLinkError)(nil),
		(*ErrorCode_FeedMappingError)(nil),
		(*ErrorCode_CustomerFeedError)(nil),
		(*ErrorCode_AdGroupFeedError)(nil),
		(*ErrorCode_CampaignFeedError)(nil),
		(*ErrorCode_CustomInterestError)(nil),
		(*ErrorCode_CampaignExperimentError)(nil),
		(*ErrorCode_ExtensionFeedItemError)(nil),
		(*ErrorCode_AdParameterError)(nil),
		(*ErrorCode_FeedItemValidationError)(nil),
		(*ErrorCode_ExtensionSettingError)(nil),
		(*ErrorCode_FeedItemTargetError)(nil),
		(*ErrorCode_PolicyViolationError)(nil),
		(*ErrorCode_MutateJobError)(nil),
		(*ErrorCode_PartialFailureError)(nil),
		(*ErrorCode_PolicyValidationParameterError)(nil),
		(*ErrorCode_SizeLimitError)(nil),
		(*ErrorCode_NotWhitelistedError)(nil),
		(*ErrorCode_ManagerLinkError)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_ads_googleads_v1_errors_errors_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_ads_googleads_v1_errors_errors_proto_goTypes,
		DependencyIndexes: file_google_ads_googleads_v1_errors_errors_proto_depIdxs,
		MessageInfos:      file_google_ads_googleads_v1_errors_errors_proto_msgTypes,
	}.Build()
	File_google_ads_googleads_v1_errors_errors_proto = out.File
	file_google_ads_googleads_v1_errors_errors_proto_rawDesc = nil
	file_google_ads_googleads_v1_errors_errors_proto_goTypes = nil
	file_google_ads_googleads_v1_errors_errors_proto_depIdxs = nil
}
