// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.22.0
// 	protoc        v3.11.2
// source: google/ads/googleads/v1/common/criteria.proto

package common

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	enums "google.golang.org/genproto/googleapis/ads/googleads/v1/enums"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// A keyword criterion.
type KeywordInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The text of the keyword (at most 80 characters and 10 words).
	Text *wrappers.StringValue `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// The match type of the keyword.
	MatchType enums.KeywordMatchTypeEnum_KeywordMatchType `protobuf:"varint,2,opt,name=match_type,json=matchType,proto3,enum=google.ads.googleads.v1.enums.KeywordMatchTypeEnum_KeywordMatchType" json:"match_type,omitempty"`
}

func (x *KeywordInfo) Reset() {
	*x = KeywordInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeywordInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeywordInfo) ProtoMessage() {}

func (x *KeywordInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeywordInfo.ProtoReflect.Descriptor instead.
func (*KeywordInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{0}
}

func (x *KeywordInfo) GetText() *wrappers.StringValue {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *KeywordInfo) GetMatchType() enums.KeywordMatchTypeEnum_KeywordMatchType {
	if x != nil {
		return x.MatchType
	}
	return enums.KeywordMatchTypeEnum_UNSPECIFIED
}

// A placement criterion. This can be used to modify bids for sites when
// targeting the content network.
type PlacementInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// URL of the placement.
	//
	// For example, "http://www.domain.com".
	Url *wrappers.StringValue `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *PlacementInfo) Reset() {
	*x = PlacementInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlacementInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlacementInfo) ProtoMessage() {}

func (x *PlacementInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlacementInfo.ProtoReflect.Descriptor instead.
func (*PlacementInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{1}
}

func (x *PlacementInfo) GetUrl() *wrappers.StringValue {
	if x != nil {
		return x.Url
	}
	return nil
}

// A mobile app category criterion.
type MobileAppCategoryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The mobile app category constant resource name.
	MobileAppCategoryConstant *wrappers.StringValue `protobuf:"bytes,1,opt,name=mobile_app_category_constant,json=mobileAppCategoryConstant,proto3" json:"mobile_app_category_constant,omitempty"`
}

func (x *MobileAppCategoryInfo) Reset() {
	*x = MobileAppCategoryInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MobileAppCategoryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MobileAppCategoryInfo) ProtoMessage() {}

func (x *MobileAppCategoryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MobileAppCategoryInfo.ProtoReflect.Descriptor instead.
func (*MobileAppCategoryInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{2}
}

func (x *MobileAppCategoryInfo) GetMobileAppCategoryConstant() *wrappers.StringValue {
	if x != nil {
		return x.MobileAppCategoryConstant
	}
	return nil
}

// A mobile application criterion.
type MobileApplicationInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A string that uniquely identifies a mobile application to Google Ads API.
	// The format of this string is "{platform}-{platform_native_id}", where
	// platform is "1" for iOS apps and "2" for Android apps, and where
	// platform_native_id is the mobile application identifier native to the
	// corresponding platform.
	// For iOS, this native identifier is the 9 digit string that appears at the
	// end of an App Store URL (e.g., "476943146" for "Flood-It! 2" whose App
	// Store link is "http://itunes.apple.com/us/app/flood-it!-2/id476943146").
	// For Android, this native identifier is the application's package name
	// (e.g., "com.labpixies.colordrips" for "Color Drips" given Google Play link
	// "https://play.google.com/store/apps/details?id=com.labpixies.colordrips").
	// A well formed app id for Google Ads API would thus be "1-476943146" for iOS
	// and "2-com.labpixies.colordrips" for Android.
	// This field is required and must be set in CREATE operations.
	AppId *wrappers.StringValue `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// Name of this mobile application.
	Name *wrappers.StringValue `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *MobileApplicationInfo) Reset() {
	*x = MobileApplicationInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MobileApplicationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MobileApplicationInfo) ProtoMessage() {}

func (x *MobileApplicationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MobileApplicationInfo.ProtoReflect.Descriptor instead.
func (*MobileApplicationInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{3}
}

func (x *MobileApplicationInfo) GetAppId() *wrappers.StringValue {
	if x != nil {
		return x.AppId
	}
	return nil
}

func (x *MobileApplicationInfo) GetName() *wrappers.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

// A location criterion.
type LocationInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The geo target constant resource name.
	GeoTargetConstant *wrappers.StringValue `protobuf:"bytes,1,opt,name=geo_target_constant,json=geoTargetConstant,proto3" json:"geo_target_constant,omitempty"`
}

func (x *LocationInfo) Reset() {
	*x = LocationInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationInfo) ProtoMessage() {}

func (x *LocationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationInfo.ProtoReflect.Descriptor instead.
func (*LocationInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{4}
}

func (x *LocationInfo) GetGeoTargetConstant() *wrappers.StringValue {
	if x != nil {
		return x.GeoTargetConstant
	}
	return nil
}

// A device criterion.
type DeviceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of the device.
	Type enums.DeviceEnum_Device `protobuf:"varint,1,opt,name=type,proto3,enum=google.ads.googleads.v1.enums.DeviceEnum_Device" json:"type,omitempty"`
}

func (x *DeviceInfo) Reset() {
	*x = DeviceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceInfo) ProtoMessage() {}

func (x *DeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceInfo.ProtoReflect.Descriptor instead.
func (*DeviceInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{5}
}

func (x *DeviceInfo) GetType() enums.DeviceEnum_Device {
	if x != nil {
		return x.Type
	}
	return enums.DeviceEnum_UNSPECIFIED
}

// A preferred content criterion.
type PreferredContentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of the preferred content.
	Type enums.PreferredContentTypeEnum_PreferredContentType `protobuf:"varint,2,opt,name=type,proto3,enum=google.ads.googleads.v1.enums.PreferredContentTypeEnum_PreferredContentType" json:"type,omitempty"`
}

func (x *PreferredContentInfo) Reset() {
	*x = PreferredContentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreferredContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreferredContentInfo) ProtoMessage() {}

func (x *PreferredContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreferredContentInfo.ProtoReflect.Descriptor instead.
func (*PreferredContentInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{6}
}

func (x *PreferredContentInfo) GetType() enums.PreferredContentTypeEnum_PreferredContentType {
	if x != nil {
		return x.Type
	}
	return enums.PreferredContentTypeEnum_UNSPECIFIED
}

// A listing group criterion.
type ListingGroupInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of the listing group.
	Type enums.ListingGroupTypeEnum_ListingGroupType `protobuf:"varint,1,opt,name=type,proto3,enum=google.ads.googleads.v1.enums.ListingGroupTypeEnum_ListingGroupType" json:"type,omitempty"`
	// Dimension value with which this listing group is refining its parent.
	// Undefined for the root group.
	CaseValue *ListingDimensionInfo `protobuf:"bytes,2,opt,name=case_value,json=caseValue,proto3" json:"case_value,omitempty"`
	// Resource name of ad group criterion which is the parent listing group
	// subdivision. Null for the root group.
	ParentAdGroupCriterion *wrappers.StringValue `protobuf:"bytes,3,opt,name=parent_ad_group_criterion,json=parentAdGroupCriterion,proto3" json:"parent_ad_group_criterion,omitempty"`
}

func (x *ListingGroupInfo) Reset() {
	*x = ListingGroupInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListingGroupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListingGroupInfo) ProtoMessage() {}

func (x *ListingGroupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListingGroupInfo.ProtoReflect.Descriptor instead.
func (*ListingGroupInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{7}
}

func (x *ListingGroupInfo) GetType() enums.ListingGroupTypeEnum_ListingGroupType {
	if x != nil {
		return x.Type
	}
	return enums.ListingGroupTypeEnum_UNSPECIFIED
}

func (x *ListingGroupInfo) GetCaseValue() *ListingDimensionInfo {
	if x != nil {
		return x.CaseValue
	}
	return nil
}

func (x *ListingGroupInfo) GetParentAdGroupCriterion() *wrappers.StringValue {
	if x != nil {
		return x.ParentAdGroupCriterion
	}
	return nil
}

// A listing scope criterion.
type ListingScopeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Scope of the campaign criterion.
	Dimensions []*ListingDimensionInfo `protobuf:"bytes,2,rep,name=dimensions,proto3" json:"dimensions,omitempty"`
}

func (x *ListingScopeInfo) Reset() {
	*x = ListingScopeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListingScopeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListingScopeInfo) ProtoMessage() {}

func (x *ListingScopeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListingScopeInfo.ProtoReflect.Descriptor instead.
func (*ListingScopeInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{8}
}

func (x *ListingScopeInfo) GetDimensions() []*ListingDimensionInfo {
	if x != nil {
		return x.Dimensions
	}
	return nil
}

// Listing dimensions for listing group criterion.
type ListingDimensionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Dimension of one of the types below is always present.
	//
	// Types that are assignable to Dimension:
	//	*ListingDimensionInfo_ListingBrand
	//	*ListingDimensionInfo_HotelId
	//	*ListingDimensionInfo_HotelClass
	//	*ListingDimensionInfo_HotelCountryRegion
	//	*ListingDimensionInfo_HotelState
	//	*ListingDimensionInfo_HotelCity
	//	*ListingDimensionInfo_ListingCustomAttribute
	//	*ListingDimensionInfo_ProductBiddingCategory
	//	*ListingDimensionInfo_ProductChannel
	//	*ListingDimensionInfo_ProductChannelExclusivity
	//	*ListingDimensionInfo_ProductCondition
	//	*ListingDimensionInfo_ProductItemId
	//	*ListingDimensionInfo_ProductType
	//	*ListingDimensionInfo_UnknownListingDimension
	Dimension isListingDimensionInfo_Dimension `protobuf_oneof:"dimension"`
}

func (x *ListingDimensionInfo) Reset() {
	*x = ListingDimensionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListingDimensionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListingDimensionInfo) ProtoMessage() {}

func (x *ListingDimensionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListingDimensionInfo.ProtoReflect.Descriptor instead.
func (*ListingDimensionInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{9}
}

func (m *ListingDimensionInfo) GetDimension() isListingDimensionInfo_Dimension {
	if m != nil {
		return m.Dimension
	}
	return nil
}

func (x *ListingDimensionInfo) GetListingBrand() *ListingBrandInfo {
	if x, ok := x.GetDimension().(*ListingDimensionInfo_ListingBrand); ok {
		return x.ListingBrand
	}
	return nil
}

func (x *ListingDimensionInfo) GetHotelId() *HotelIdInfo {
	if x, ok := x.GetDimension().(*ListingDimensionInfo_HotelId); ok {
		return x.HotelId
	}
	return nil
}

func (x *ListingDimensionInfo) GetHotelClass() *HotelClassInfo {
	if x, ok := x.GetDimension().(*ListingDimensionInfo_HotelClass); ok {
		return x.HotelClass
	}
	return nil
}

func (x *ListingDimensionInfo) GetHotelCountryRegion() *HotelCountryRegionInfo {
	if x, ok := x.GetDimension().(*ListingDimensionInfo_HotelCountryRegion); ok {
		return x.HotelCountryRegion
	}
	return nil
}

func (x *ListingDimensionInfo) GetHotelState() *HotelStateInfo {
	if x, ok := x.GetDimension().(*ListingDimensionInfo_HotelState); ok {
		return x.HotelState
	}
	return nil
}

func (x *ListingDimensionInfo) GetHotelCity() *HotelCityInfo {
	if x, ok := x.GetDimension().(*ListingDimensionInfo_HotelCity); ok {
		return x.HotelCity
	}
	return nil
}

func (x *ListingDimensionInfo) GetListingCustomAttribute() *ListingCustomAttributeInfo {
	if x, ok := x.GetDimension().(*ListingDimensionInfo_ListingCustomAttribute); ok {
		return x.ListingCustomAttribute
	}
	return nil
}

func (x *ListingDimensionInfo) GetProductBiddingCategory() *ProductBiddingCategoryInfo {
	if x, ok := x.GetDimension().(*ListingDimensionInfo_ProductBiddingCategory); ok {
		return x.ProductBiddingCategory
	}
	return nil
}

func (x *ListingDimensionInfo) GetProductChannel() *ProductChannelInfo {
	if x, ok := x.GetDimension().(*ListingDimensionInfo_ProductChannel); ok {
		return x.ProductChannel
	}
	return nil
}

func (x *ListingDimensionInfo) GetProductChannelExclusivity() *ProductChannelExclusivityInfo {
	if x, ok := x.GetDimension().(*ListingDimensionInfo_ProductChannelExclusivity); ok {
		return x.ProductChannelExclusivity
	}
	return nil
}

func (x *ListingDimensionInfo) GetProductCondition() *ProductConditionInfo {
	if x, ok := x.GetDimension().(*ListingDimensionInfo_ProductCondition); ok {
		return x.ProductCondition
	}
	return nil
}

func (x *ListingDimensionInfo) GetProductItemId() *ProductItemIdInfo {
	if x, ok := x.GetDimension().(*ListingDimensionInfo_ProductItemId); ok {
		return x.ProductItemId
	}
	return nil
}

func (x *ListingDimensionInfo) GetProductType() *ProductTypeInfo {
	if x, ok := x.GetDimension().(*ListingDimensionInfo_ProductType); ok {
		return x.ProductType
	}
	return nil
}

func (x *ListingDimensionInfo) GetUnknownListingDimension() *UnknownListingDimensionInfo {
	if x, ok := x.GetDimension().(*ListingDimensionInfo_UnknownListingDimension); ok {
		return x.UnknownListingDimension
	}
	return nil
}

type isListingDimensionInfo_Dimension interface {
	isListingDimensionInfo_Dimension()
}

type ListingDimensionInfo_ListingBrand struct {
	// Brand of the listing.
	ListingBrand *ListingBrandInfo `protobuf:"bytes,1,opt,name=listing_brand,json=listingBrand,proto3,oneof"`
}

type ListingDimensionInfo_HotelId struct {
	// Advertiser-specific hotel ID.
	HotelId *HotelIdInfo `protobuf:"bytes,2,opt,name=hotel_id,json=hotelId,proto3,oneof"`
}

type ListingDimensionInfo_HotelClass struct {
	// Class of the hotel as a number of stars 1 to 5.
	HotelClass *HotelClassInfo `protobuf:"bytes,3,opt,name=hotel_class,json=hotelClass,proto3,oneof"`
}

type ListingDimensionInfo_HotelCountryRegion struct {
	// Country or Region the hotel is located in.
	HotelCountryRegion *HotelCountryRegionInfo `protobuf:"bytes,4,opt,name=hotel_country_region,json=hotelCountryRegion,proto3,oneof"`
}

type ListingDimensionInfo_HotelState struct {
	// State the hotel is located in.
	HotelState *HotelStateInfo `protobuf:"bytes,5,opt,name=hotel_state,json=hotelState,proto3,oneof"`
}

type ListingDimensionInfo_HotelCity struct {
	// City the hotel is located in.
	HotelCity *HotelCityInfo `protobuf:"bytes,6,opt,name=hotel_city,json=hotelCity,proto3,oneof"`
}

type ListingDimensionInfo_ListingCustomAttribute struct {
	// Listing custom attribute.
	ListingCustomAttribute *ListingCustomAttributeInfo `protobuf:"bytes,7,opt,name=listing_custom_attribute,json=listingCustomAttribute,proto3,oneof"`
}

type ListingDimensionInfo_ProductBiddingCategory struct {
	// Bidding category of a product offer.
	ProductBiddingCategory *ProductBiddingCategoryInfo `protobuf:"bytes,13,opt,name=product_bidding_category,json=productBiddingCategory,proto3,oneof"`
}

type ListingDimensionInfo_ProductChannel struct {
	// Locality of a product offer.
	ProductChannel *ProductChannelInfo `protobuf:"bytes,8,opt,name=product_channel,json=productChannel,proto3,oneof"`
}

type ListingDimensionInfo_ProductChannelExclusivity struct {
	// Availability of a product offer.
	ProductChannelExclusivity *ProductChannelExclusivityInfo `protobuf:"bytes,9,opt,name=product_channel_exclusivity,json=productChannelExclusivity,proto3,oneof"`
}

type ListingDimensionInfo_ProductCondition struct {
	// Condition of a product offer.
	ProductCondition *ProductConditionInfo `protobuf:"bytes,10,opt,name=product_condition,json=productCondition,proto3,oneof"`
}

type ListingDimensionInfo_ProductItemId struct {
	// Item id of a product offer.
	ProductItemId *ProductItemIdInfo `protobuf:"bytes,11,opt,name=product_item_id,json=productItemId,proto3,oneof"`
}

type ListingDimensionInfo_ProductType struct {
	// Type of a product offer.
	ProductType *ProductTypeInfo `protobuf:"bytes,12,opt,name=product_type,json=productType,proto3,oneof"`
}

type ListingDimensionInfo_UnknownListingDimension struct {
	// Unknown dimension. Set when no other listing dimension is set.
	UnknownListingDimension *UnknownListingDimensionInfo `protobuf:"bytes,14,opt,name=unknown_listing_dimension,json=unknownListingDimension,proto3,oneof"`
}

func (*ListingDimensionInfo_ListingBrand) isListingDimensionInfo_Dimension() {}

func (*ListingDimensionInfo_HotelId) isListingDimensionInfo_Dimension() {}

func (*ListingDimensionInfo_HotelClass) isListingDimensionInfo_Dimension() {}

func (*ListingDimensionInfo_HotelCountryRegion) isListingDimensionInfo_Dimension() {}

func (*ListingDimensionInfo_HotelState) isListingDimensionInfo_Dimension() {}

func (*ListingDimensionInfo_HotelCity) isListingDimensionInfo_Dimension() {}

func (*ListingDimensionInfo_ListingCustomAttribute) isListingDimensionInfo_Dimension() {}

func (*ListingDimensionInfo_ProductBiddingCategory) isListingDimensionInfo_Dimension() {}

func (*ListingDimensionInfo_ProductChannel) isListingDimensionInfo_Dimension() {}

func (*ListingDimensionInfo_ProductChannelExclusivity) isListingDimensionInfo_Dimension() {}

func (*ListingDimensionInfo_ProductCondition) isListingDimensionInfo_Dimension() {}

func (*ListingDimensionInfo_ProductItemId) isListingDimensionInfo_Dimension() {}

func (*ListingDimensionInfo_ProductType) isListingDimensionInfo_Dimension() {}

func (*ListingDimensionInfo_UnknownListingDimension) isListingDimensionInfo_Dimension() {}

// Brand of the listing.
type ListingBrandInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// String value of the listing brand.
	Value *wrappers.StringValue `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *ListingBrandInfo) Reset() {
	*x = ListingBrandInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListingBrandInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListingBrandInfo) ProtoMessage() {}

func (x *ListingBrandInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListingBrandInfo.ProtoReflect.Descriptor instead.
func (*ListingBrandInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{10}
}

func (x *ListingBrandInfo) GetValue() *wrappers.StringValue {
	if x != nil {
		return x.Value
	}
	return nil
}

// Advertiser-specific hotel ID.
type HotelIdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// String value of the hotel ID.
	Value *wrappers.StringValue `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *HotelIdInfo) Reset() {
	*x = HotelIdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotelIdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotelIdInfo) ProtoMessage() {}

func (x *HotelIdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotelIdInfo.ProtoReflect.Descriptor instead.
func (*HotelIdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{11}
}

func (x *HotelIdInfo) GetValue() *wrappers.StringValue {
	if x != nil {
		return x.Value
	}
	return nil
}

// Class of the hotel as a number of stars 1 to 5.
type HotelClassInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Long value of the hotel class.
	Value *wrappers.Int64Value `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *HotelClassInfo) Reset() {
	*x = HotelClassInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotelClassInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotelClassInfo) ProtoMessage() {}

func (x *HotelClassInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotelClassInfo.ProtoReflect.Descriptor instead.
func (*HotelClassInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{12}
}

func (x *HotelClassInfo) GetValue() *wrappers.Int64Value {
	if x != nil {
		return x.Value
	}
	return nil
}

// Country or Region the hotel is located in.
type HotelCountryRegionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Geo Target Constant resource name.
	CountryRegionCriterion *wrappers.StringValue `protobuf:"bytes,1,opt,name=country_region_criterion,json=countryRegionCriterion,proto3" json:"country_region_criterion,omitempty"`
}

func (x *HotelCountryRegionInfo) Reset() {
	*x = HotelCountryRegionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotelCountryRegionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotelCountryRegionInfo) ProtoMessage() {}

func (x *HotelCountryRegionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotelCountryRegionInfo.ProtoReflect.Descriptor instead.
func (*HotelCountryRegionInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{13}
}

func (x *HotelCountryRegionInfo) GetCountryRegionCriterion() *wrappers.StringValue {
	if x != nil {
		return x.CountryRegionCriterion
	}
	return nil
}

// State the hotel is located in.
type HotelStateInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Geo Target Constant resource name.
	StateCriterion *wrappers.StringValue `protobuf:"bytes,1,opt,name=state_criterion,json=stateCriterion,proto3" json:"state_criterion,omitempty"`
}

func (x *HotelStateInfo) Reset() {
	*x = HotelStateInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotelStateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotelStateInfo) ProtoMessage() {}

func (x *HotelStateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotelStateInfo.ProtoReflect.Descriptor instead.
func (*HotelStateInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{14}
}

func (x *HotelStateInfo) GetStateCriterion() *wrappers.StringValue {
	if x != nil {
		return x.StateCriterion
	}
	return nil
}

// City the hotel is located in.
type HotelCityInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Geo Target Constant resource name.
	CityCriterion *wrappers.StringValue `protobuf:"bytes,1,opt,name=city_criterion,json=cityCriterion,proto3" json:"city_criterion,omitempty"`
}

func (x *HotelCityInfo) Reset() {
	*x = HotelCityInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotelCityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotelCityInfo) ProtoMessage() {}

func (x *HotelCityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotelCityInfo.ProtoReflect.Descriptor instead.
func (*HotelCityInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{15}
}

func (x *HotelCityInfo) GetCityCriterion() *wrappers.StringValue {
	if x != nil {
		return x.CityCriterion
	}
	return nil
}

// Listing custom attribute.
type ListingCustomAttributeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// String value of the listing custom attribute.
	Value *wrappers.StringValue `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	// Indicates the index of the custom attribute.
	Index enums.ListingCustomAttributeIndexEnum_ListingCustomAttributeIndex `protobuf:"varint,2,opt,name=index,proto3,enum=google.ads.googleads.v1.enums.ListingCustomAttributeIndexEnum_ListingCustomAttributeIndex" json:"index,omitempty"`
}

func (x *ListingCustomAttributeInfo) Reset() {
	*x = ListingCustomAttributeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListingCustomAttributeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListingCustomAttributeInfo) ProtoMessage() {}

func (x *ListingCustomAttributeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListingCustomAttributeInfo.ProtoReflect.Descriptor instead.
func (*ListingCustomAttributeInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{16}
}

func (x *ListingCustomAttributeInfo) GetValue() *wrappers.StringValue {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *ListingCustomAttributeInfo) GetIndex() enums.ListingCustomAttributeIndexEnum_ListingCustomAttributeIndex {
	if x != nil {
		return x.Index
	}
	return enums.ListingCustomAttributeIndexEnum_UNSPECIFIED
}

// Bidding category of a product offer.
type ProductBiddingCategoryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID of the product bidding category.
	//
	// This ID is equivalent to the google_product_category ID as described in
	// this article: https://support.google.com/merchants/answer/6324436
	Id *wrappers.Int64Value `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Two-letter upper-case country code of the product bidding category. It must
	// match the campaign.shopping_setting.sales_country field.
	CountryCode *wrappers.StringValue `protobuf:"bytes,2,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// Level of the product bidding category.
	Level enums.ProductBiddingCategoryLevelEnum_ProductBiddingCategoryLevel `protobuf:"varint,3,opt,name=level,proto3,enum=google.ads.googleads.v1.enums.ProductBiddingCategoryLevelEnum_ProductBiddingCategoryLevel" json:"level,omitempty"`
}

func (x *ProductBiddingCategoryInfo) Reset() {
	*x = ProductBiddingCategoryInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductBiddingCategoryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductBiddingCategoryInfo) ProtoMessage() {}

func (x *ProductBiddingCategoryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductBiddingCategoryInfo.ProtoReflect.Descriptor instead.
func (*ProductBiddingCategoryInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{17}
}

func (x *ProductBiddingCategoryInfo) GetId() *wrappers.Int64Value {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *ProductBiddingCategoryInfo) GetCountryCode() *wrappers.StringValue {
	if x != nil {
		return x.CountryCode
	}
	return nil
}

func (x *ProductBiddingCategoryInfo) GetLevel() enums.ProductBiddingCategoryLevelEnum_ProductBiddingCategoryLevel {
	if x != nil {
		return x.Level
	}
	return enums.ProductBiddingCategoryLevelEnum_UNSPECIFIED
}

// Locality of a product offer.
type ProductChannelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Value of the locality.
	Channel enums.ProductChannelEnum_ProductChannel `protobuf:"varint,1,opt,name=channel,proto3,enum=google.ads.googleads.v1.enums.ProductChannelEnum_ProductChannel" json:"channel,omitempty"`
}

func (x *ProductChannelInfo) Reset() {
	*x = ProductChannelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductChannelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductChannelInfo) ProtoMessage() {}

func (x *ProductChannelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductChannelInfo.ProtoReflect.Descriptor instead.
func (*ProductChannelInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{18}
}

func (x *ProductChannelInfo) GetChannel() enums.ProductChannelEnum_ProductChannel {
	if x != nil {
		return x.Channel
	}
	return enums.ProductChannelEnum_UNSPECIFIED
}

// Availability of a product offer.
type ProductChannelExclusivityInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Value of the availability.
	ChannelExclusivity enums.ProductChannelExclusivityEnum_ProductChannelExclusivity `protobuf:"varint,1,opt,name=channel_exclusivity,json=channelExclusivity,proto3,enum=google.ads.googleads.v1.enums.ProductChannelExclusivityEnum_ProductChannelExclusivity" json:"channel_exclusivity,omitempty"`
}

func (x *ProductChannelExclusivityInfo) Reset() {
	*x = ProductChannelExclusivityInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductChannelExclusivityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductChannelExclusivityInfo) ProtoMessage() {}

func (x *ProductChannelExclusivityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductChannelExclusivityInfo.ProtoReflect.Descriptor instead.
func (*ProductChannelExclusivityInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{19}
}

func (x *ProductChannelExclusivityInfo) GetChannelExclusivity() enums.ProductChannelExclusivityEnum_ProductChannelExclusivity {
	if x != nil {
		return x.ChannelExclusivity
	}
	return enums.ProductChannelExclusivityEnum_UNSPECIFIED
}

// Condition of a product offer.
type ProductConditionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Value of the condition.
	Condition enums.ProductConditionEnum_ProductCondition `protobuf:"varint,1,opt,name=condition,proto3,enum=google.ads.googleads.v1.enums.ProductConditionEnum_ProductCondition" json:"condition,omitempty"`
}

func (x *ProductConditionInfo) Reset() {
	*x = ProductConditionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductConditionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductConditionInfo) ProtoMessage() {}

func (x *ProductConditionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductConditionInfo.ProtoReflect.Descriptor instead.
func (*ProductConditionInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{20}
}

func (x *ProductConditionInfo) GetCondition() enums.ProductConditionEnum_ProductCondition {
	if x != nil {
		return x.Condition
	}
	return enums.ProductConditionEnum_UNSPECIFIED
}

// Item id of a product offer.
type ProductItemIdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Value of the id.
	Value *wrappers.StringValue `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *ProductItemIdInfo) Reset() {
	*x = ProductItemIdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductItemIdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductItemIdInfo) ProtoMessage() {}

func (x *ProductItemIdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductItemIdInfo.ProtoReflect.Descriptor instead.
func (*ProductItemIdInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{21}
}

func (x *ProductItemIdInfo) GetValue() *wrappers.StringValue {
	if x != nil {
		return x.Value
	}
	return nil
}

// Type of a product offer.
type ProductTypeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Value of the type.
	Value *wrappers.StringValue `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	// Level of the type.
	Level enums.ProductTypeLevelEnum_ProductTypeLevel `protobuf:"varint,2,opt,name=level,proto3,enum=google.ads.googleads.v1.enums.ProductTypeLevelEnum_ProductTypeLevel" json:"level,omitempty"`
}

func (x *ProductTypeInfo) Reset() {
	*x = ProductTypeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductTypeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductTypeInfo) ProtoMessage() {}

func (x *ProductTypeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductTypeInfo.ProtoReflect.Descriptor instead.
func (*ProductTypeInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{22}
}

func (x *ProductTypeInfo) GetValue() *wrappers.StringValue {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *ProductTypeInfo) GetLevel() enums.ProductTypeLevelEnum_ProductTypeLevel {
	if x != nil {
		return x.Level
	}
	return enums.ProductTypeLevelEnum_UNSPECIFIED
}

// Unknown listing dimension.
type UnknownListingDimensionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UnknownListingDimensionInfo) Reset() {
	*x = UnknownListingDimensionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnknownListingDimensionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnknownListingDimensionInfo) ProtoMessage() {}

func (x *UnknownListingDimensionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnknownListingDimensionInfo.ProtoReflect.Descriptor instead.
func (*UnknownListingDimensionInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{23}
}

// Criterion for hotel date selection (default dates vs. user selected).
type HotelDateSelectionTypeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of the hotel date selection
	Type enums.HotelDateSelectionTypeEnum_HotelDateSelectionType `protobuf:"varint,1,opt,name=type,proto3,enum=google.ads.googleads.v1.enums.HotelDateSelectionTypeEnum_HotelDateSelectionType" json:"type,omitempty"`
}

func (x *HotelDateSelectionTypeInfo) Reset() {
	*x = HotelDateSelectionTypeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotelDateSelectionTypeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotelDateSelectionTypeInfo) ProtoMessage() {}

func (x *HotelDateSelectionTypeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotelDateSelectionTypeInfo.ProtoReflect.Descriptor instead.
func (*HotelDateSelectionTypeInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{24}
}

func (x *HotelDateSelectionTypeInfo) GetType() enums.HotelDateSelectionTypeEnum_HotelDateSelectionType {
	if x != nil {
		return x.Type
	}
	return enums.HotelDateSelectionTypeEnum_UNSPECIFIED
}

// Criterion for number of days prior to the stay the booking is being made.
type HotelAdvanceBookingWindowInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Low end of the number of days prior to the stay.
	MinDays *wrappers.Int64Value `protobuf:"bytes,1,opt,name=min_days,json=minDays,proto3" json:"min_days,omitempty"`
	// High end of the number of days prior to the stay.
	MaxDays *wrappers.Int64Value `protobuf:"bytes,2,opt,name=max_days,json=maxDays,proto3" json:"max_days,omitempty"`
}

func (x *HotelAdvanceBookingWindowInfo) Reset() {
	*x = HotelAdvanceBookingWindowInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotelAdvanceBookingWindowInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotelAdvanceBookingWindowInfo) ProtoMessage() {}

func (x *HotelAdvanceBookingWindowInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotelAdvanceBookingWindowInfo.ProtoReflect.Descriptor instead.
func (*HotelAdvanceBookingWindowInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{25}
}

func (x *HotelAdvanceBookingWindowInfo) GetMinDays() *wrappers.Int64Value {
	if x != nil {
		return x.MinDays
	}
	return nil
}

func (x *HotelAdvanceBookingWindowInfo) GetMaxDays() *wrappers.Int64Value {
	if x != nil {
		return x.MaxDays
	}
	return nil
}

// Criterion for length of hotel stay in nights.
type HotelLengthOfStayInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Low end of the number of nights in the stay.
	MinNights *wrappers.Int64Value `protobuf:"bytes,1,opt,name=min_nights,json=minNights,proto3" json:"min_nights,omitempty"`
	// High end of the number of nights in the stay.
	MaxNights *wrappers.Int64Value `protobuf:"bytes,2,opt,name=max_nights,json=maxNights,proto3" json:"max_nights,omitempty"`
}

func (x *HotelLengthOfStayInfo) Reset() {
	*x = HotelLengthOfStayInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotelLengthOfStayInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotelLengthOfStayInfo) ProtoMessage() {}

func (x *HotelLengthOfStayInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotelLengthOfStayInfo.ProtoReflect.Descriptor instead.
func (*HotelLengthOfStayInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{26}
}

func (x *HotelLengthOfStayInfo) GetMinNights() *wrappers.Int64Value {
	if x != nil {
		return x.MinNights
	}
	return nil
}

func (x *HotelLengthOfStayInfo) GetMaxNights() *wrappers.Int64Value {
	if x != nil {
		return x.MaxNights
	}
	return nil
}

// Criterion for day of the week the booking is for.
type HotelCheckInDayInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The day of the week.
	DayOfWeek enums.DayOfWeekEnum_DayOfWeek `protobuf:"varint,1,opt,name=day_of_week,json=dayOfWeek,proto3,enum=google.ads.googleads.v1.enums.DayOfWeekEnum_DayOfWeek" json:"day_of_week,omitempty"`
}

func (x *HotelCheckInDayInfo) Reset() {
	*x = HotelCheckInDayInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotelCheckInDayInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotelCheckInDayInfo) ProtoMessage() {}

func (x *HotelCheckInDayInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotelCheckInDayInfo.ProtoReflect.Descriptor instead.
func (*HotelCheckInDayInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{27}
}

func (x *HotelCheckInDayInfo) GetDayOfWeek() enums.DayOfWeekEnum_DayOfWeek {
	if x != nil {
		return x.DayOfWeek
	}
	return enums.DayOfWeekEnum_UNSPECIFIED
}

// Criterion for Interaction Type.
type InteractionTypeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The interaction type.
	Type enums.InteractionTypeEnum_InteractionType `protobuf:"varint,1,opt,name=type,proto3,enum=google.ads.googleads.v1.enums.InteractionTypeEnum_InteractionType" json:"type,omitempty"`
}

func (x *InteractionTypeInfo) Reset() {
	*x = InteractionTypeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InteractionTypeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InteractionTypeInfo) ProtoMessage() {}

func (x *InteractionTypeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InteractionTypeInfo.ProtoReflect.Descriptor instead.
func (*InteractionTypeInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{28}
}

func (x *InteractionTypeInfo) GetType() enums.InteractionTypeEnum_InteractionType {
	if x != nil {
		return x.Type
	}
	return enums.InteractionTypeEnum_UNSPECIFIED
}

// Represents an AdSchedule criterion.
//
// AdSchedule is specified as the day of the week and a time interval
// within which ads will be shown.
//
// No more than six AdSchedules can be added for the same day.
type AdScheduleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Minutes after the start hour at which this schedule starts.
	//
	// This field is required for CREATE operations and is prohibited on UPDATE
	// operations.
	StartMinute enums.MinuteOfHourEnum_MinuteOfHour `protobuf:"varint,1,opt,name=start_minute,json=startMinute,proto3,enum=google.ads.googleads.v1.enums.MinuteOfHourEnum_MinuteOfHour" json:"start_minute,omitempty"`
	// Minutes after the end hour at which this schedule ends. The schedule is
	// exclusive of the end minute.
	//
	// This field is required for CREATE operations and is prohibited on UPDATE
	// operations.
	EndMinute enums.MinuteOfHourEnum_MinuteOfHour `protobuf:"varint,2,opt,name=end_minute,json=endMinute,proto3,enum=google.ads.googleads.v1.enums.MinuteOfHourEnum_MinuteOfHour" json:"end_minute,omitempty"`
	// Starting hour in 24 hour time.
	// This field must be between 0 and 23, inclusive.
	//
	// This field is required for CREATE operations and is prohibited on UPDATE
	// operations.
	StartHour *wrappers.Int32Value `protobuf:"bytes,3,opt,name=start_hour,json=startHour,proto3" json:"start_hour,omitempty"`
	// Ending hour in 24 hour time; 24 signifies end of the day.
	// This field must be between 0 and 24, inclusive.
	//
	// This field is required for CREATE operations and is prohibited on UPDATE
	// operations.
	EndHour *wrappers.Int32Value `protobuf:"bytes,4,opt,name=end_hour,json=endHour,proto3" json:"end_hour,omitempty"`
	// Day of the week the schedule applies to.
	//
	// This field is required for CREATE operations and is prohibited on UPDATE
	// operations.
	DayOfWeek enums.DayOfWeekEnum_DayOfWeek `protobuf:"varint,5,opt,name=day_of_week,json=dayOfWeek,proto3,enum=google.ads.googleads.v1.enums.DayOfWeekEnum_DayOfWeek" json:"day_of_week,omitempty"`
}

func (x *AdScheduleInfo) Reset() {
	*x = AdScheduleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdScheduleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdScheduleInfo) ProtoMessage() {}

func (x *AdScheduleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdScheduleInfo.ProtoReflect.Descriptor instead.
func (*AdScheduleInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{29}
}

func (x *AdScheduleInfo) GetStartMinute() enums.MinuteOfHourEnum_MinuteOfHour {
	if x != nil {
		return x.StartMinute
	}
	return enums.MinuteOfHourEnum_UNSPECIFIED
}

func (x *AdScheduleInfo) GetEndMinute() enums.MinuteOfHourEnum_MinuteOfHour {
	if x != nil {
		return x.EndMinute
	}
	return enums.MinuteOfHourEnum_UNSPECIFIED
}

func (x *AdScheduleInfo) GetStartHour() *wrappers.Int32Value {
	if x != nil {
		return x.StartHour
	}
	return nil
}

func (x *AdScheduleInfo) GetEndHour() *wrappers.Int32Value {
	if x != nil {
		return x.EndHour
	}
	return nil
}

func (x *AdScheduleInfo) GetDayOfWeek() enums.DayOfWeekEnum_DayOfWeek {
	if x != nil {
		return x.DayOfWeek
	}
	return enums.DayOfWeekEnum_UNSPECIFIED
}

// An age range criterion.
type AgeRangeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of the age range.
	Type enums.AgeRangeTypeEnum_AgeRangeType `protobuf:"varint,1,opt,name=type,proto3,enum=google.ads.googleads.v1.enums.AgeRangeTypeEnum_AgeRangeType" json:"type,omitempty"`
}

func (x *AgeRangeInfo) Reset() {
	*x = AgeRangeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgeRangeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgeRangeInfo) ProtoMessage() {}

func (x *AgeRangeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgeRangeInfo.ProtoReflect.Descriptor instead.
func (*AgeRangeInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{30}
}

func (x *AgeRangeInfo) GetType() enums.AgeRangeTypeEnum_AgeRangeType {
	if x != nil {
		return x.Type
	}
	return enums.AgeRangeTypeEnum_UNSPECIFIED
}

// A gender criterion.
type GenderInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of the gender.
	Type enums.GenderTypeEnum_GenderType `protobuf:"varint,1,opt,name=type,proto3,enum=google.ads.googleads.v1.enums.GenderTypeEnum_GenderType" json:"type,omitempty"`
}

func (x *GenderInfo) Reset() {
	*x = GenderInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenderInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenderInfo) ProtoMessage() {}

func (x *GenderInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenderInfo.ProtoReflect.Descriptor instead.
func (*GenderInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{31}
}

func (x *GenderInfo) GetType() enums.GenderTypeEnum_GenderType {
	if x != nil {
		return x.Type
	}
	return enums.GenderTypeEnum_UNSPECIFIED
}

// An income range criterion.
type IncomeRangeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of the income range.
	Type enums.IncomeRangeTypeEnum_IncomeRangeType `protobuf:"varint,1,opt,name=type,proto3,enum=google.ads.googleads.v1.enums.IncomeRangeTypeEnum_IncomeRangeType" json:"type,omitempty"`
}

func (x *IncomeRangeInfo) Reset() {
	*x = IncomeRangeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncomeRangeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncomeRangeInfo) ProtoMessage() {}

func (x *IncomeRangeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncomeRangeInfo.ProtoReflect.Descriptor instead.
func (*IncomeRangeInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{32}
}

func (x *IncomeRangeInfo) GetType() enums.IncomeRangeTypeEnum_IncomeRangeType {
	if x != nil {
		return x.Type
	}
	return enums.IncomeRangeTypeEnum_UNSPECIFIED
}

// A parental status criterion.
type ParentalStatusInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of the parental status.
	Type enums.ParentalStatusTypeEnum_ParentalStatusType `protobuf:"varint,1,opt,name=type,proto3,enum=google.ads.googleads.v1.enums.ParentalStatusTypeEnum_ParentalStatusType" json:"type,omitempty"`
}

func (x *ParentalStatusInfo) Reset() {
	*x = ParentalStatusInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParentalStatusInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParentalStatusInfo) ProtoMessage() {}

func (x *ParentalStatusInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParentalStatusInfo.ProtoReflect.Descriptor instead.
func (*ParentalStatusInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{33}
}

func (x *ParentalStatusInfo) GetType() enums.ParentalStatusTypeEnum_ParentalStatusType {
	if x != nil {
		return x.Type
	}
	return enums.ParentalStatusTypeEnum_UNSPECIFIED
}

// A YouTube Video criterion.
type YouTubeVideoInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// YouTube video id as it appears on the YouTube watch page.
	VideoId *wrappers.StringValue `protobuf:"bytes,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty"`
}

func (x *YouTubeVideoInfo) Reset() {
	*x = YouTubeVideoInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YouTubeVideoInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YouTubeVideoInfo) ProtoMessage() {}

func (x *YouTubeVideoInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YouTubeVideoInfo.ProtoReflect.Descriptor instead.
func (*YouTubeVideoInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{34}
}

func (x *YouTubeVideoInfo) GetVideoId() *wrappers.StringValue {
	if x != nil {
		return x.VideoId
	}
	return nil
}

// A YouTube Channel criterion.
type YouTubeChannelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The YouTube uploader channel id or the channel code of a YouTube channel.
	ChannelId *wrappers.StringValue `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
}

func (x *YouTubeChannelInfo) Reset() {
	*x = YouTubeChannelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YouTubeChannelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YouTubeChannelInfo) ProtoMessage() {}

func (x *YouTubeChannelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YouTubeChannelInfo.ProtoReflect.Descriptor instead.
func (*YouTubeChannelInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{35}
}

func (x *YouTubeChannelInfo) GetChannelId() *wrappers.StringValue {
	if x != nil {
		return x.ChannelId
	}
	return nil
}

// A User List criterion. Represents a user list that is defined by the
// advertiser to be targeted.
type UserListInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The User List resource name.
	UserList *wrappers.StringValue `protobuf:"bytes,1,opt,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
}

func (x *UserListInfo) Reset() {
	*x = UserListInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserListInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserListInfo) ProtoMessage() {}

func (x *UserListInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserListInfo.ProtoReflect.Descriptor instead.
func (*UserListInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{36}
}

func (x *UserListInfo) GetUserList() *wrappers.StringValue {
	if x != nil {
		return x.UserList
	}
	return nil
}

// A Proximity criterion. The geo point and radius determine what geographical
// area is included. The address is a description of the geo point that does
// not affect ad serving.
//
// There are two ways to create a proximity. First, by setting an address
// and radius. The geo point will be automatically computed. Second, by
// setting a geo point and radius. The address is an optional label that won't
// be validated.
type ProximityInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Latitude and longitude.
	GeoPoint *GeoPointInfo `protobuf:"bytes,1,opt,name=geo_point,json=geoPoint,proto3" json:"geo_point,omitempty"`
	// The radius of the proximity.
	Radius *wrappers.DoubleValue `protobuf:"bytes,2,opt,name=radius,proto3" json:"radius,omitempty"`
	// The unit of measurement of the radius. Default is KILOMETERS.
	RadiusUnits enums.ProximityRadiusUnitsEnum_ProximityRadiusUnits `protobuf:"varint,3,opt,name=radius_units,json=radiusUnits,proto3,enum=google.ads.googleads.v1.enums.ProximityRadiusUnitsEnum_ProximityRadiusUnits" json:"radius_units,omitempty"`
	// Full address.
	Address *AddressInfo `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *ProximityInfo) Reset() {
	*x = ProximityInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProximityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProximityInfo) ProtoMessage() {}

func (x *ProximityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProximityInfo.ProtoReflect.Descriptor instead.
func (*ProximityInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{37}
}

func (x *ProximityInfo) GetGeoPoint() *GeoPointInfo {
	if x != nil {
		return x.GeoPoint
	}
	return nil
}

func (x *ProximityInfo) GetRadius() *wrappers.DoubleValue {
	if x != nil {
		return x.Radius
	}
	return nil
}

func (x *ProximityInfo) GetRadiusUnits() enums.ProximityRadiusUnitsEnum_ProximityRadiusUnits {
	if x != nil {
		return x.RadiusUnits
	}
	return enums.ProximityRadiusUnitsEnum_UNSPECIFIED
}

func (x *ProximityInfo) GetAddress() *AddressInfo {
	if x != nil {
		return x.Address
	}
	return nil
}

// Geo point for proximity criterion.
type GeoPointInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Micro degrees for the longitude.
	LongitudeInMicroDegrees *wrappers.Int32Value `protobuf:"bytes,1,opt,name=longitude_in_micro_degrees,json=longitudeInMicroDegrees,proto3" json:"longitude_in_micro_degrees,omitempty"`
	// Micro degrees for the latitude.
	LatitudeInMicroDegrees *wrappers.Int32Value `protobuf:"bytes,2,opt,name=latitude_in_micro_degrees,json=latitudeInMicroDegrees,proto3" json:"latitude_in_micro_degrees,omitempty"`
}

func (x *GeoPointInfo) Reset() {
	*x = GeoPointInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeoPointInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeoPointInfo) ProtoMessage() {}

func (x *GeoPointInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeoPointInfo.ProtoReflect.Descriptor instead.
func (*GeoPointInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{38}
}

func (x *GeoPointInfo) GetLongitudeInMicroDegrees() *wrappers.Int32Value {
	if x != nil {
		return x.LongitudeInMicroDegrees
	}
	return nil
}

func (x *GeoPointInfo) GetLatitudeInMicroDegrees() *wrappers.Int32Value {
	if x != nil {
		return x.LatitudeInMicroDegrees
	}
	return nil
}

// Address for proximity criterion.
type AddressInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Postal code.
	PostalCode *wrappers.StringValue `protobuf:"bytes,1,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	// Province or state code.
	ProvinceCode *wrappers.StringValue `protobuf:"bytes,2,opt,name=province_code,json=provinceCode,proto3" json:"province_code,omitempty"`
	// Country code.
	CountryCode *wrappers.StringValue `protobuf:"bytes,3,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// Province or state name.
	ProvinceName *wrappers.StringValue `protobuf:"bytes,4,opt,name=province_name,json=provinceName,proto3" json:"province_name,omitempty"`
	// Street address line 1.
	StreetAddress *wrappers.StringValue `protobuf:"bytes,5,opt,name=street_address,json=streetAddress,proto3" json:"street_address,omitempty"`
	// Street address line 2. This field is write-only. It is only used for
	// calculating the longitude and latitude of an address when geo_point is
	// empty.
	StreetAddress2 *wrappers.StringValue `protobuf:"bytes,6,opt,name=street_address2,json=streetAddress2,proto3" json:"street_address2,omitempty"`
	// Name of the city.
	CityName *wrappers.StringValue `protobuf:"bytes,7,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
}

func (x *AddressInfo) Reset() {
	*x = AddressInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressInfo) ProtoMessage() {}

func (x *AddressInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressInfo.ProtoReflect.Descriptor instead.
func (*AddressInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{39}
}

func (x *AddressInfo) GetPostalCode() *wrappers.StringValue {
	if x != nil {
		return x.PostalCode
	}
	return nil
}

func (x *AddressInfo) GetProvinceCode() *wrappers.StringValue {
	if x != nil {
		return x.ProvinceCode
	}
	return nil
}

func (x *AddressInfo) GetCountryCode() *wrappers.StringValue {
	if x != nil {
		return x.CountryCode
	}
	return nil
}

func (x *AddressInfo) GetProvinceName() *wrappers.StringValue {
	if x != nil {
		return x.ProvinceName
	}
	return nil
}

func (x *AddressInfo) GetStreetAddress() *wrappers.StringValue {
	if x != nil {
		return x.StreetAddress
	}
	return nil
}

func (x *AddressInfo) GetStreetAddress2() *wrappers.StringValue {
	if x != nil {
		return x.StreetAddress2
	}
	return nil
}

func (x *AddressInfo) GetCityName() *wrappers.StringValue {
	if x != nil {
		return x.CityName
	}
	return nil
}

// A topic criterion. Use topics to target or exclude placements in the
// Google Display Network based on the category into which the placement falls
// (for example, "Pets & Animals/Pets/Dogs").
type TopicInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Topic Constant resource name.
	TopicConstant *wrappers.StringValue `protobuf:"bytes,1,opt,name=topic_constant,json=topicConstant,proto3" json:"topic_constant,omitempty"`
	// The category to target or exclude. Each subsequent element in the array
	// describes a more specific sub-category. For example,
	// "Pets & Animals", "Pets", "Dogs" represents the "Pets & Animals/Pets/Dogs"
	// category.
	Path []*wrappers.StringValue `protobuf:"bytes,2,rep,name=path,proto3" json:"path,omitempty"`
}

func (x *TopicInfo) Reset() {
	*x = TopicInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TopicInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopicInfo) ProtoMessage() {}

func (x *TopicInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopicInfo.ProtoReflect.Descriptor instead.
func (*TopicInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{40}
}

func (x *TopicInfo) GetTopicConstant() *wrappers.StringValue {
	if x != nil {
		return x.TopicConstant
	}
	return nil
}

func (x *TopicInfo) GetPath() []*wrappers.StringValue {
	if x != nil {
		return x.Path
	}
	return nil
}

// A language criterion.
type LanguageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The language constant resource name.
	LanguageConstant *wrappers.StringValue `protobuf:"bytes,1,opt,name=language_constant,json=languageConstant,proto3" json:"language_constant,omitempty"`
}

func (x *LanguageInfo) Reset() {
	*x = LanguageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LanguageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LanguageInfo) ProtoMessage() {}

func (x *LanguageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LanguageInfo.ProtoReflect.Descriptor instead.
func (*LanguageInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{41}
}

func (x *LanguageInfo) GetLanguageConstant() *wrappers.StringValue {
	if x != nil {
		return x.LanguageConstant
	}
	return nil
}

// An IpBlock criterion used for IP exclusions. We allow:
//  - IPv4 and IPv6 addresses
//  - individual addresses (***********)
//  - masks for individual addresses (***********/32)
//  - masks for Class C networks (***********/24)
type IpBlockInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The IP address of this IP block.
	IpAddress *wrappers.StringValue `protobuf:"bytes,1,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
}

func (x *IpBlockInfo) Reset() {
	*x = IpBlockInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IpBlockInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpBlockInfo) ProtoMessage() {}

func (x *IpBlockInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpBlockInfo.ProtoReflect.Descriptor instead.
func (*IpBlockInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{42}
}

func (x *IpBlockInfo) GetIpAddress() *wrappers.StringValue {
	if x != nil {
		return x.IpAddress
	}
	return nil
}

// Content Label for category exclusion.
type ContentLabelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Content label type, required for CREATE operations.
	Type enums.ContentLabelTypeEnum_ContentLabelType `protobuf:"varint,1,opt,name=type,proto3,enum=google.ads.googleads.v1.enums.ContentLabelTypeEnum_ContentLabelType" json:"type,omitempty"`
}

func (x *ContentLabelInfo) Reset() {
	*x = ContentLabelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContentLabelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentLabelInfo) ProtoMessage() {}

func (x *ContentLabelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentLabelInfo.ProtoReflect.Descriptor instead.
func (*ContentLabelInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{43}
}

func (x *ContentLabelInfo) GetType() enums.ContentLabelTypeEnum_ContentLabelType {
	if x != nil {
		return x.Type
	}
	return enums.ContentLabelTypeEnum_UNSPECIFIED
}

// Represents a Carrier Criterion.
type CarrierInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Carrier constant resource name.
	CarrierConstant *wrappers.StringValue `protobuf:"bytes,1,opt,name=carrier_constant,json=carrierConstant,proto3" json:"carrier_constant,omitempty"`
}

func (x *CarrierInfo) Reset() {
	*x = CarrierInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarrierInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarrierInfo) ProtoMessage() {}

func (x *CarrierInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarrierInfo.ProtoReflect.Descriptor instead.
func (*CarrierInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{44}
}

func (x *CarrierInfo) GetCarrierConstant() *wrappers.StringValue {
	if x != nil {
		return x.CarrierConstant
	}
	return nil
}

// Represents a particular interest-based topic to be targeted.
type UserInterestInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The UserInterest resource name.
	UserInterestCategory *wrappers.StringValue `protobuf:"bytes,1,opt,name=user_interest_category,json=userInterestCategory,proto3" json:"user_interest_category,omitempty"`
}

func (x *UserInterestInfo) Reset() {
	*x = UserInterestInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInterestInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInterestInfo) ProtoMessage() {}

func (x *UserInterestInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInterestInfo.ProtoReflect.Descriptor instead.
func (*UserInterestInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{45}
}

func (x *UserInterestInfo) GetUserInterestCategory() *wrappers.StringValue {
	if x != nil {
		return x.UserInterestCategory
	}
	return nil
}

// Represents a criterion for targeting webpages of an advertiser's website.
type WebpageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the criterion that is defined by this parameter. The name value
	// will be used for identifying, sorting and filtering criteria with this type
	// of parameters.
	//
	// This field is required for CREATE operations and is prohibited on UPDATE
	// operations.
	CriterionName *wrappers.StringValue `protobuf:"bytes,1,opt,name=criterion_name,json=criterionName,proto3" json:"criterion_name,omitempty"`
	// Conditions, or logical expressions, for webpage targeting. The list of
	// webpage targeting conditions are and-ed together when evaluated
	// for targeting.
	//
	// This field is required for CREATE operations and is prohibited on UPDATE
	// operations.
	Conditions []*WebpageConditionInfo `protobuf:"bytes,2,rep,name=conditions,proto3" json:"conditions,omitempty"`
}

func (x *WebpageInfo) Reset() {
	*x = WebpageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebpageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebpageInfo) ProtoMessage() {}

func (x *WebpageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebpageInfo.ProtoReflect.Descriptor instead.
func (*WebpageInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{46}
}

func (x *WebpageInfo) GetCriterionName() *wrappers.StringValue {
	if x != nil {
		return x.CriterionName
	}
	return nil
}

func (x *WebpageInfo) GetConditions() []*WebpageConditionInfo {
	if x != nil {
		return x.Conditions
	}
	return nil
}

// Logical expression for targeting webpages of an advertiser's website.
type WebpageConditionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Operand of webpage targeting condition.
	Operand enums.WebpageConditionOperandEnum_WebpageConditionOperand `protobuf:"varint,1,opt,name=operand,proto3,enum=google.ads.googleads.v1.enums.WebpageConditionOperandEnum_WebpageConditionOperand" json:"operand,omitempty"`
	// Operator of webpage targeting condition.
	Operator enums.WebpageConditionOperatorEnum_WebpageConditionOperator `protobuf:"varint,2,opt,name=operator,proto3,enum=google.ads.googleads.v1.enums.WebpageConditionOperatorEnum_WebpageConditionOperator" json:"operator,omitempty"`
	// Argument of webpage targeting condition.
	Argument *wrappers.StringValue `protobuf:"bytes,3,opt,name=argument,proto3" json:"argument,omitempty"`
}

func (x *WebpageConditionInfo) Reset() {
	*x = WebpageConditionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebpageConditionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebpageConditionInfo) ProtoMessage() {}

func (x *WebpageConditionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebpageConditionInfo.ProtoReflect.Descriptor instead.
func (*WebpageConditionInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{47}
}

func (x *WebpageConditionInfo) GetOperand() enums.WebpageConditionOperandEnum_WebpageConditionOperand {
	if x != nil {
		return x.Operand
	}
	return enums.WebpageConditionOperandEnum_UNSPECIFIED
}

func (x *WebpageConditionInfo) GetOperator() enums.WebpageConditionOperatorEnum_WebpageConditionOperator {
	if x != nil {
		return x.Operator
	}
	return enums.WebpageConditionOperatorEnum_UNSPECIFIED
}

func (x *WebpageConditionInfo) GetArgument() *wrappers.StringValue {
	if x != nil {
		return x.Argument
	}
	return nil
}

// Represents an operating system version to be targeted.
type OperatingSystemVersionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The operating system version constant resource name.
	OperatingSystemVersionConstant *wrappers.StringValue `protobuf:"bytes,1,opt,name=operating_system_version_constant,json=operatingSystemVersionConstant,proto3" json:"operating_system_version_constant,omitempty"`
}

func (x *OperatingSystemVersionInfo) Reset() {
	*x = OperatingSystemVersionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperatingSystemVersionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperatingSystemVersionInfo) ProtoMessage() {}

func (x *OperatingSystemVersionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperatingSystemVersionInfo.ProtoReflect.Descriptor instead.
func (*OperatingSystemVersionInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{48}
}

func (x *OperatingSystemVersionInfo) GetOperatingSystemVersionConstant() *wrappers.StringValue {
	if x != nil {
		return x.OperatingSystemVersionConstant
	}
	return nil
}

// An app payment model criterion.
type AppPaymentModelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of the app payment model.
	Type enums.AppPaymentModelTypeEnum_AppPaymentModelType `protobuf:"varint,1,opt,name=type,proto3,enum=google.ads.googleads.v1.enums.AppPaymentModelTypeEnum_AppPaymentModelType" json:"type,omitempty"`
}

func (x *AppPaymentModelInfo) Reset() {
	*x = AppPaymentModelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppPaymentModelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppPaymentModelInfo) ProtoMessage() {}

func (x *AppPaymentModelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppPaymentModelInfo.ProtoReflect.Descriptor instead.
func (*AppPaymentModelInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{49}
}

func (x *AppPaymentModelInfo) GetType() enums.AppPaymentModelTypeEnum_AppPaymentModelType {
	if x != nil {
		return x.Type
	}
	return enums.AppPaymentModelTypeEnum_UNSPECIFIED
}

// A mobile device criterion.
type MobileDeviceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The mobile device constant resource name.
	MobileDeviceConstant *wrappers.StringValue `protobuf:"bytes,1,opt,name=mobile_device_constant,json=mobileDeviceConstant,proto3" json:"mobile_device_constant,omitempty"`
}

func (x *MobileDeviceInfo) Reset() {
	*x = MobileDeviceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MobileDeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MobileDeviceInfo) ProtoMessage() {}

func (x *MobileDeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MobileDeviceInfo.ProtoReflect.Descriptor instead.
func (*MobileDeviceInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{50}
}

func (x *MobileDeviceInfo) GetMobileDeviceConstant() *wrappers.StringValue {
	if x != nil {
		return x.MobileDeviceConstant
	}
	return nil
}

// A custom affinity criterion.
// A criterion of this type is only targetable.
type CustomAffinityInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The CustomInterest resource name.
	CustomAffinity *wrappers.StringValue `protobuf:"bytes,1,opt,name=custom_affinity,json=customAffinity,proto3" json:"custom_affinity,omitempty"`
}

func (x *CustomAffinityInfo) Reset() {
	*x = CustomAffinityInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomAffinityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomAffinityInfo) ProtoMessage() {}

func (x *CustomAffinityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomAffinityInfo.ProtoReflect.Descriptor instead.
func (*CustomAffinityInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{51}
}

func (x *CustomAffinityInfo) GetCustomAffinity() *wrappers.StringValue {
	if x != nil {
		return x.CustomAffinity
	}
	return nil
}

// A custom intent criterion.
// A criterion of this type is only targetable.
type CustomIntentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The CustomInterest resource name.
	CustomIntent *wrappers.StringValue `protobuf:"bytes,1,opt,name=custom_intent,json=customIntent,proto3" json:"custom_intent,omitempty"`
}

func (x *CustomIntentInfo) Reset() {
	*x = CustomIntentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomIntentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomIntentInfo) ProtoMessage() {}

func (x *CustomIntentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomIntentInfo.ProtoReflect.Descriptor instead.
func (*CustomIntentInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{52}
}

func (x *CustomIntentInfo) GetCustomIntent() *wrappers.StringValue {
	if x != nil {
		return x.CustomIntent
	}
	return nil
}

// A radius around a list of locations specified via a feed.
type LocationGroupInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Feed specifying locations for targeting.
	// This is required and must be set in CREATE operations.
	Feed *wrappers.StringValue `protobuf:"bytes,1,opt,name=feed,proto3" json:"feed,omitempty"`
	// Geo target constant(s) restricting the scope of the geographic area within
	// the feed. Currently only one geo target constant is allowed.
	GeoTargetConstants []*wrappers.StringValue `protobuf:"bytes,2,rep,name=geo_target_constants,json=geoTargetConstants,proto3" json:"geo_target_constants,omitempty"`
	// Distance in units specifying the radius around targeted locations.
	// This is required and must be set in CREATE operations.
	Radius *wrappers.Int64Value `protobuf:"bytes,3,opt,name=radius,proto3" json:"radius,omitempty"`
	// Unit of the radius, miles and meters supported currently.
	// This is required and must be set in CREATE operations.
	RadiusUnits enums.LocationGroupRadiusUnitsEnum_LocationGroupRadiusUnits `protobuf:"varint,4,opt,name=radius_units,json=radiusUnits,proto3,enum=google.ads.googleads.v1.enums.LocationGroupRadiusUnitsEnum_LocationGroupRadiusUnits" json:"radius_units,omitempty"`
}

func (x *LocationGroupInfo) Reset() {
	*x = LocationGroupInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationGroupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationGroupInfo) ProtoMessage() {}

func (x *LocationGroupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_common_criteria_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationGroupInfo.ProtoReflect.Descriptor instead.
func (*LocationGroupInfo) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP(), []int{53}
}

func (x *LocationGroupInfo) GetFeed() *wrappers.StringValue {
	if x != nil {
		return x.Feed
	}
	return nil
}

func (x *LocationGroupInfo) GetGeoTargetConstants() []*wrappers.StringValue {
	if x != nil {
		return x.GeoTargetConstants
	}
	return nil
}

func (x *LocationGroupInfo) GetRadius() *wrappers.Int64Value {
	if x != nil {
		return x.Radius
	}
	return nil
}

func (x *LocationGroupInfo) GetRadiusUnits() enums.LocationGroupRadiusUnitsEnum_LocationGroupRadiusUnits {
	if x != nil {
		return x.RadiusUnits
	}
	return enums.LocationGroupRadiusUnitsEnum_UNSPECIFIED
}

var File_google_ads_googleads_v1_common_criteria_proto protoreflect.FileDescriptor

var file_google_ads_googleads_v1_common_criteria_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a,
	0x32, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x61,
	0x67, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x36, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x64, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65,
	0x65, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2f, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3d, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64,
	0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x68, 0x6f, 0x74, 0x65, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x36, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x42, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x6c, 0x69, 0x73,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64,
	0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f,
	0x68, 0x6f, 0x75, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x42, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x36, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x78, 0x69, 0x6d, 0x69,
	0x74, 0x79, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3d, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64,
	0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x77, 0x65, 0x62, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2f, 0x77, 0x65, 0x62, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xa4, 0x01, 0x0a, 0x0b, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x63, 0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x44, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x22, 0x3f, 0x0a, 0x0d, 0x50, 0x6c, 0x61,
	0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x76, 0x0a, 0x15, 0x4d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x5d, 0x0a, 0x1c, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x61, 0x70,
	0x70, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x19, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x41,
	0x70, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x22, 0x7e, 0x0a, 0x15, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x06, 0x61,
	0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x30, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x5c, 0x0a, 0x0c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x4c, 0x0a, 0x13, 0x67, 0x65, 0x6f, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x11, 0x67,
	0x65, 0x6f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x22, 0x52, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x44,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x78, 0x0a, 0x14, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x60, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x45,
	0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x9a,
	0x02, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x58, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x44, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79,
	0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a,
	0x0a, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x63, 0x61, 0x73, 0x65, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x57, 0x0a, 0x19, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x16, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x22, 0x68, 0x0a, 0x10, 0x4c,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x54, 0x0a, 0x0a, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x6d, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x64, 0x69, 0x6d, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x8b, 0x0b, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e,
	0x67, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x57,
	0x0a, 0x0d, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x72,
	0x61, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0c, 0x6c, 0x69, 0x73, 0x74, 0x69,
	0x6e, 0x67, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x48, 0x0a, 0x08, 0x68, 0x6f, 0x74, 0x65, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x74, 0x65, 0x6c,
	0x49, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x07, 0x68, 0x6f, 0x74, 0x65, 0x6c, 0x49,
	0x64, 0x12, 0x51, 0x0a, 0x0b, 0x68, 0x6f, 0x74, 0x65, 0x6c, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x74, 0x65, 0x6c, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0a, 0x68, 0x6f, 0x74, 0x65, 0x6c, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x12, 0x6a, 0x0a, 0x14, 0x68, 0x6f, 0x74, 0x65, 0x6c, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x74, 0x65, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x12, 0x68, 0x6f,
	0x74, 0x65, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x51, 0x0a, 0x0b, 0x68, 0x6f, 0x74, 0x65, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x74, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0a, 0x68, 0x6f, 0x74, 0x65, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x4e, 0x0a, 0x0a, 0x68, 0x6f, 0x74, 0x65, 0x6c, 0x5f, 0x63, 0x69, 0x74,
	0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x74, 0x65, 0x6c, 0x43, 0x69,
	0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x09, 0x68, 0x6f, 0x74, 0x65, 0x6c, 0x43,
	0x69, 0x74, 0x79, 0x12, 0x76, 0x0a, 0x18, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x48, 0x00, 0x52, 0x16, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x76, 0x0a, 0x18, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x16, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x5d, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x48, 0x00, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x12, 0x7f, 0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x19, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x12, 0x63, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5b, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49,
	0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x54, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0b,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x79, 0x0a, 0x19, 0x75,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x69,
	0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x17, 0x75,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x6d,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0x46, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x72,
	0x61, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x41, 0x0a, 0x0b, 0x48,
	0x6f, 0x74, 0x65, 0x6c, 0x49, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x43,
	0x0a, 0x0e, 0x48, 0x6f, 0x74, 0x65, 0x6c, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x31, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0x70, 0x0a, 0x16, 0x48, 0x6f, 0x74, 0x65, 0x6c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x56, 0x0a,
	0x18, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x16, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x69, 0x74,
	0x65, 0x72, 0x69, 0x6f, 0x6e, 0x22, 0x57, 0x0a, 0x0e, 0x48, 0x6f, 0x74, 0x65, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x45, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x22, 0x54,
	0x0a, 0x0d, 0x48, 0x6f, 0x74, 0x65, 0x6c, 0x43, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x43, 0x0a, 0x0e, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x63, 0x69, 0x74, 0x79, 0x43, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x6f, 0x6e, 0x22, 0xc2, 0x01, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x70, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x5a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0xfc, 0x01, 0x0a, 0x1a, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3f, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x70, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x5a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x42, 0x69, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x42, 0x69, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x70, 0x0a, 0x12, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5a,
	0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x40, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x45, 0x6e,
	0x75, 0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0xa9, 0x01, 0x0a, 0x1d, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x45, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x87, 0x01, 0x0a,
	0x13, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x56, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x52, 0x12, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x45, 0x78, 0x63, 0x6c, 0x75,
	0x73, 0x69, 0x76, 0x69, 0x74, 0x79, 0x22, 0x7a, 0x0a, 0x14, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x62,
	0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x44, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x47, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x49, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xa1, 0x01, 0x0a, 0x0f,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x5a, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x44, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22,
	0x1d, 0x0a, 0x1b, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e,
	0x67, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x82,
	0x01, 0x0a, 0x1a, 0x48, 0x6f, 0x74, 0x65, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x64, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x50, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x48, 0x6f, 0x74, 0x65,
	0x6c, 0x44, 0x61, 0x74, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x48, 0x6f, 0x74, 0x65, 0x6c, 0x44, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x8f, 0x01, 0x0a, 0x1d, 0x48, 0x6f, 0x74, 0x65, 0x6c, 0x41, 0x64, 0x76,
	0x61, 0x6e, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x57, 0x69, 0x6e, 0x64, 0x6f,
	0x77, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x36, 0x0a, 0x08, 0x6d, 0x69, 0x6e, 0x5f, 0x64, 0x61, 0x79,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x6d, 0x69, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x12, 0x36, 0x0a,
	0x08, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x6d, 0x61,
	0x78, 0x44, 0x61, 0x79, 0x73, 0x22, 0x8f, 0x01, 0x0a, 0x15, 0x48, 0x6f, 0x74, 0x65, 0x6c, 0x4c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x4f, 0x66, 0x53, 0x74, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x3a, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x6e, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x09, 0x6d, 0x69, 0x6e, 0x4e, 0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x3a, 0x0a, 0x0a, 0x6d,
	0x61, 0x78, 0x5f, 0x6e, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6d, 0x61,
	0x78, 0x4e, 0x69, 0x67, 0x68, 0x74, 0x73, 0x22, 0x6d, 0x0a, 0x13, 0x48, 0x6f, 0x74, 0x65, 0x6c,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x44, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x56,
	0x0a, 0x0b, 0x64, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x45, 0x6e, 0x75,
	0x6d, 0x2e, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x09, 0x64, 0x61, 0x79,
	0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x22, 0x6d, 0x0a, 0x13, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x56, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x42, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x9a, 0x03, 0x0a, 0x0e, 0x41, 0x64, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5f, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4d,
	0x69, 0x6e, 0x75, 0x74, 0x65, 0x4f, 0x66, 0x48, 0x6f, 0x75, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e,
	0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x4f, 0x66, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x0b, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x12, 0x5b, 0x0a, 0x0a, 0x65, 0x6e, 0x64,
	0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4d, 0x69,
	0x6e, 0x75, 0x74, 0x65, 0x4f, 0x66, 0x48, 0x6f, 0x75, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4d,
	0x69, 0x6e, 0x75, 0x74, 0x65, 0x4f, 0x66, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x09, 0x65, 0x6e, 0x64,
	0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x68, 0x6f, 0x75, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x48, 0x6f,
	0x75, 0x72, 0x12, 0x36, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x48, 0x6f, 0x75, 0x72, 0x12, 0x56, 0x0a, 0x0b, 0x64, 0x61,
	0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x36, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x44, 0x61,
	0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x09, 0x64, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65,
	0x65, 0x6b, 0x22, 0x60, 0x0a, 0x0c, 0x41, 0x67, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x50, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x3c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x41, 0x67, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75,
	0x6d, 0x2e, 0x41, 0x67, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x5a, 0x0a, 0x0a, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x4c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x38, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e,
	0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x22, 0x69, 0x0a, 0x0f, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x56, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x42, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x72, 0x0a, 0x12, 0x50,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x5c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x48, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22,
	0x4b, 0x0a, 0x10, 0x59, 0x6f, 0x75, 0x54, 0x75, 0x62, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x37, 0x0a, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x07, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x64, 0x22, 0x51, 0x0a, 0x12,
	0x59, 0x6f, 0x75, 0x54, 0x75, 0x62, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x22,
	0x49, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x39, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xc8, 0x02, 0x0a, 0x0d, 0x50,
	0x72, 0x6f, 0x78, 0x69, 0x6d, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x49, 0x0a, 0x09,
	0x67, 0x65, 0x6f, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x47, 0x65, 0x6f, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x67,
	0x65, 0x6f, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x72, 0x61, 0x64, 0x69, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x6f, 0x0a,
	0x0c, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x4c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x78, 0x69, 0x6d, 0x69, 0x74, 0x79, 0x52, 0x61, 0x64,
	0x69, 0x75, 0x73, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x50, 0x72, 0x6f,
	0x78, 0x69, 0x6d, 0x69, 0x74, 0x79, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x55, 0x6e, 0x69, 0x74,
	0x73, 0x52, 0x0b, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x45,
	0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xc0, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x6f, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x58, 0x0a, 0x1a, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x5f, 0x64, 0x65, 0x67,
	0x72, 0x65, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x17, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x49, 0x6e, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x44, 0x65, 0x67, 0x72, 0x65, 0x65, 0x73,
	0x12, 0x56, 0x0a, 0x19, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x5f, 0x69, 0x6e, 0x5f,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x5f, 0x64, 0x65, 0x67, 0x72, 0x65, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x16, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x49, 0x6e, 0x4d, 0x69, 0x63, 0x72,
	0x6f, 0x44, 0x65, 0x67, 0x72, 0x65, 0x65, 0x73, 0x22, 0xda, 0x03, 0x0a, 0x0b, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3d, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x70, 0x6f, 0x73,
	0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x0c, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43,
	0x0a, 0x0e, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x45, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x73, 0x74, 0x72, 0x65,
	0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x12, 0x39, 0x0a, 0x09, 0x63, 0x69,
	0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x63, 0x69, 0x74,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x09, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x43, 0x0a, 0x0e, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x63, 0x6f, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x43, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0x59, 0x0a, 0x0c, 0x4c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x49, 0x0a, 0x11, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x10, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x22, 0x4a, 0x0a, 0x0b, 0x49, 0x70, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x0a, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x22, 0x6c, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x58, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x44, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22,
	0x56, 0x0a, 0x0b, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x47,
	0x0a, 0x10, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x22, 0x66, 0x0a, 0x10, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x52, 0x0a, 0x16, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x14, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22,
	0xa8, 0x01, 0x0a, 0x0b, 0x57, 0x65, 0x62, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x43, 0x0a, 0x0e, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x54, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x57, 0x65, 0x62, 0x70, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xb0, 0x02, 0x0a, 0x14, 0x57,
	0x65, 0x62, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x6c, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x52, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64,
	0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x57, 0x65, 0x62, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x6e, 0x64, 0x45, 0x6e, 0x75, 0x6d,
	0x2e, 0x57, 0x65, 0x62, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x6e, 0x64, 0x52, 0x07, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x6e,
	0x64, 0x12, 0x70, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x54, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x57, 0x65, 0x62, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d,
	0x2e, 0x57, 0x65, 0x62, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x38, 0x0a, 0x08, 0x61, 0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x08, 0x61, 0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x85, 0x01,
	0x0a, 0x1a, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x67, 0x0a, 0x21,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x1e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x22, 0x75, 0x0a, 0x13, 0x41, 0x70, 0x70, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5e, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x70, 0x70, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e,
	0x75, 0x6d, 0x2e, 0x41, 0x70, 0x70, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x66, 0x0a, 0x10,
	0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x52, 0x0a, 0x16, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x14,
	0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x22, 0x5b, 0x0a, 0x12, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x66,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x45, 0x0a, 0x0f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x61, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x79, 0x22, 0x55, 0x0a, 0x10, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x41, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f,
	0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xc3, 0x02, 0x0a, 0x11, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30,
	0x0a, 0x04, 0x66, 0x65, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x66, 0x65, 0x65, 0x64,
	0x12, 0x4e, 0x0a, 0x14, 0x67, 0x65, 0x6f, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x12, 0x67, 0x65,
	0x6f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x73,
	0x12, 0x33, 0x0a, 0x06, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x72,
	0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x77, 0x0a, 0x0c, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x54, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x55,
	0x6e, 0x69, 0x74, 0x73, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x55, 0x6e, 0x69, 0x74,
	0x73, 0x52, 0x0b, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x42, 0xe8,
	0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64,
	0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x42, 0x0d, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x44, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67,
	0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x64,
	0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0xa2, 0x02, 0x03, 0x47,
	0x41, 0x41, 0xaa, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x41, 0x64, 0x73, 0x2e,
	0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x2e, 0x56, 0x31, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0xca, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x41, 0x64, 0x73,
	0x5c, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x5c, 0x56, 0x31, 0x5c, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0xea, 0x02, 0x22, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x41,
	0x64, 0x73, 0x3a, 0x3a, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x3a, 0x3a, 0x56,
	0x31, 0x3a, 0x3a, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_google_ads_googleads_v1_common_criteria_proto_rawDescOnce sync.Once
	file_google_ads_googleads_v1_common_criteria_proto_rawDescData = file_google_ads_googleads_v1_common_criteria_proto_rawDesc
)

func file_google_ads_googleads_v1_common_criteria_proto_rawDescGZIP() []byte {
	file_google_ads_googleads_v1_common_criteria_proto_rawDescOnce.Do(func() {
		file_google_ads_googleads_v1_common_criteria_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_ads_googleads_v1_common_criteria_proto_rawDescData)
	})
	return file_google_ads_googleads_v1_common_criteria_proto_rawDescData
}

var file_google_ads_googleads_v1_common_criteria_proto_msgTypes = make([]protoimpl.MessageInfo, 54)
var file_google_ads_googleads_v1_common_criteria_proto_goTypes = []interface{}{
	(*KeywordInfo)(nil),                                      // 0: google.ads.googleads.v1.common.KeywordInfo
	(*PlacementInfo)(nil),                                    // 1: google.ads.googleads.v1.common.PlacementInfo
	(*MobileAppCategoryInfo)(nil),                            // 2: google.ads.googleads.v1.common.MobileAppCategoryInfo
	(*MobileApplicationInfo)(nil),                            // 3: google.ads.googleads.v1.common.MobileApplicationInfo
	(*LocationInfo)(nil),                                     // 4: google.ads.googleads.v1.common.LocationInfo
	(*DeviceInfo)(nil),                                       // 5: google.ads.googleads.v1.common.DeviceInfo
	(*PreferredContentInfo)(nil),                             // 6: google.ads.googleads.v1.common.PreferredContentInfo
	(*ListingGroupInfo)(nil),                                 // 7: google.ads.googleads.v1.common.ListingGroupInfo
	(*ListingScopeInfo)(nil),                                 // 8: google.ads.googleads.v1.common.ListingScopeInfo
	(*ListingDimensionInfo)(nil),                             // 9: google.ads.googleads.v1.common.ListingDimensionInfo
	(*ListingBrandInfo)(nil),                                 // 10: google.ads.googleads.v1.common.ListingBrandInfo
	(*HotelIdInfo)(nil),                                      // 11: google.ads.googleads.v1.common.HotelIdInfo
	(*HotelClassInfo)(nil),                                   // 12: google.ads.googleads.v1.common.HotelClassInfo
	(*HotelCountryRegionInfo)(nil),                           // 13: google.ads.googleads.v1.common.HotelCountryRegionInfo
	(*HotelStateInfo)(nil),                                   // 14: google.ads.googleads.v1.common.HotelStateInfo
	(*HotelCityInfo)(nil),                                    // 15: google.ads.googleads.v1.common.HotelCityInfo
	(*ListingCustomAttributeInfo)(nil),                       // 16: google.ads.googleads.v1.common.ListingCustomAttributeInfo
	(*ProductBiddingCategoryInfo)(nil),                       // 17: google.ads.googleads.v1.common.ProductBiddingCategoryInfo
	(*ProductChannelInfo)(nil),                               // 18: google.ads.googleads.v1.common.ProductChannelInfo
	(*ProductChannelExclusivityInfo)(nil),                    // 19: google.ads.googleads.v1.common.ProductChannelExclusivityInfo
	(*ProductConditionInfo)(nil),                             // 20: google.ads.googleads.v1.common.ProductConditionInfo
	(*ProductItemIdInfo)(nil),                                // 21: google.ads.googleads.v1.common.ProductItemIdInfo
	(*ProductTypeInfo)(nil),                                  // 22: google.ads.googleads.v1.common.ProductTypeInfo
	(*UnknownListingDimensionInfo)(nil),                      // 23: google.ads.googleads.v1.common.UnknownListingDimensionInfo
	(*HotelDateSelectionTypeInfo)(nil),                       // 24: google.ads.googleads.v1.common.HotelDateSelectionTypeInfo
	(*HotelAdvanceBookingWindowInfo)(nil),                    // 25: google.ads.googleads.v1.common.HotelAdvanceBookingWindowInfo
	(*HotelLengthOfStayInfo)(nil),                            // 26: google.ads.googleads.v1.common.HotelLengthOfStayInfo
	(*HotelCheckInDayInfo)(nil),                              // 27: google.ads.googleads.v1.common.HotelCheckInDayInfo
	(*InteractionTypeInfo)(nil),                              // 28: google.ads.googleads.v1.common.InteractionTypeInfo
	(*AdScheduleInfo)(nil),                                   // 29: google.ads.googleads.v1.common.AdScheduleInfo
	(*AgeRangeInfo)(nil),                                     // 30: google.ads.googleads.v1.common.AgeRangeInfo
	(*GenderInfo)(nil),                                       // 31: google.ads.googleads.v1.common.GenderInfo
	(*IncomeRangeInfo)(nil),                                  // 32: google.ads.googleads.v1.common.IncomeRangeInfo
	(*ParentalStatusInfo)(nil),                               // 33: google.ads.googleads.v1.common.ParentalStatusInfo
	(*YouTubeVideoInfo)(nil),                                 // 34: google.ads.googleads.v1.common.YouTubeVideoInfo
	(*YouTubeChannelInfo)(nil),                               // 35: google.ads.googleads.v1.common.YouTubeChannelInfo
	(*UserListInfo)(nil),                                     // 36: google.ads.googleads.v1.common.UserListInfo
	(*ProximityInfo)(nil),                                    // 37: google.ads.googleads.v1.common.ProximityInfo
	(*GeoPointInfo)(nil),                                     // 38: google.ads.googleads.v1.common.GeoPointInfo
	(*AddressInfo)(nil),                                      // 39: google.ads.googleads.v1.common.AddressInfo
	(*TopicInfo)(nil),                                        // 40: google.ads.googleads.v1.common.TopicInfo
	(*LanguageInfo)(nil),                                     // 41: google.ads.googleads.v1.common.LanguageInfo
	(*IpBlockInfo)(nil),                                      // 42: google.ads.googleads.v1.common.IpBlockInfo
	(*ContentLabelInfo)(nil),                                 // 43: google.ads.googleads.v1.common.ContentLabelInfo
	(*CarrierInfo)(nil),                                      // 44: google.ads.googleads.v1.common.CarrierInfo
	(*UserInterestInfo)(nil),                                 // 45: google.ads.googleads.v1.common.UserInterestInfo
	(*WebpageInfo)(nil),                                      // 46: google.ads.googleads.v1.common.WebpageInfo
	(*WebpageConditionInfo)(nil),                             // 47: google.ads.googleads.v1.common.WebpageConditionInfo
	(*OperatingSystemVersionInfo)(nil),                       // 48: google.ads.googleads.v1.common.OperatingSystemVersionInfo
	(*AppPaymentModelInfo)(nil),                              // 49: google.ads.googleads.v1.common.AppPaymentModelInfo
	(*MobileDeviceInfo)(nil),                                 // 50: google.ads.googleads.v1.common.MobileDeviceInfo
	(*CustomAffinityInfo)(nil),                               // 51: google.ads.googleads.v1.common.CustomAffinityInfo
	(*CustomIntentInfo)(nil),                                 // 52: google.ads.googleads.v1.common.CustomIntentInfo
	(*LocationGroupInfo)(nil),                                // 53: google.ads.googleads.v1.common.LocationGroupInfo
	(*wrappers.StringValue)(nil),                             // 54: google.protobuf.StringValue
	(enums.KeywordMatchTypeEnum_KeywordMatchType)(0),         // 55: google.ads.googleads.v1.enums.KeywordMatchTypeEnum.KeywordMatchType
	(enums.DeviceEnum_Device)(0),                             // 56: google.ads.googleads.v1.enums.DeviceEnum.Device
	(enums.PreferredContentTypeEnum_PreferredContentType)(0), // 57: google.ads.googleads.v1.enums.PreferredContentTypeEnum.PreferredContentType
	(enums.ListingGroupTypeEnum_ListingGroupType)(0),         // 58: google.ads.googleads.v1.enums.ListingGroupTypeEnum.ListingGroupType
	(*wrappers.Int64Value)(nil),                              // 59: google.protobuf.Int64Value
	(enums.ListingCustomAttributeIndexEnum_ListingCustomAttributeIndex)(0), // 60: google.ads.googleads.v1.enums.ListingCustomAttributeIndexEnum.ListingCustomAttributeIndex
	(enums.ProductBiddingCategoryLevelEnum_ProductBiddingCategoryLevel)(0), // 61: google.ads.googleads.v1.enums.ProductBiddingCategoryLevelEnum.ProductBiddingCategoryLevel
	(enums.ProductChannelEnum_ProductChannel)(0),                           // 62: google.ads.googleads.v1.enums.ProductChannelEnum.ProductChannel
	(enums.ProductChannelExclusivityEnum_ProductChannelExclusivity)(0),     // 63: google.ads.googleads.v1.enums.ProductChannelExclusivityEnum.ProductChannelExclusivity
	(enums.ProductConditionEnum_ProductCondition)(0),                       // 64: google.ads.googleads.v1.enums.ProductConditionEnum.ProductCondition
	(enums.ProductTypeLevelEnum_ProductTypeLevel)(0),                       // 65: google.ads.googleads.v1.enums.ProductTypeLevelEnum.ProductTypeLevel
	(enums.HotelDateSelectionTypeEnum_HotelDateSelectionType)(0),           // 66: google.ads.googleads.v1.enums.HotelDateSelectionTypeEnum.HotelDateSelectionType
	(enums.DayOfWeekEnum_DayOfWeek)(0),                                     // 67: google.ads.googleads.v1.enums.DayOfWeekEnum.DayOfWeek
	(enums.InteractionTypeEnum_InteractionType)(0),                         // 68: google.ads.googleads.v1.enums.InteractionTypeEnum.InteractionType
	(enums.MinuteOfHourEnum_MinuteOfHour)(0),                               // 69: google.ads.googleads.v1.enums.MinuteOfHourEnum.MinuteOfHour
	(*wrappers.Int32Value)(nil),                                            // 70: google.protobuf.Int32Value
	(enums.AgeRangeTypeEnum_AgeRangeType)(0),                               // 71: google.ads.googleads.v1.enums.AgeRangeTypeEnum.AgeRangeType
	(enums.GenderTypeEnum_GenderType)(0),                                   // 72: google.ads.googleads.v1.enums.GenderTypeEnum.GenderType
	(enums.IncomeRangeTypeEnum_IncomeRangeType)(0),                         // 73: google.ads.googleads.v1.enums.IncomeRangeTypeEnum.IncomeRangeType
	(enums.ParentalStatusTypeEnum_ParentalStatusType)(0),                   // 74: google.ads.googleads.v1.enums.ParentalStatusTypeEnum.ParentalStatusType
	(*wrappers.DoubleValue)(nil),                                           // 75: google.protobuf.DoubleValue
	(enums.ProximityRadiusUnitsEnum_ProximityRadiusUnits)(0),               // 76: google.ads.googleads.v1.enums.ProximityRadiusUnitsEnum.ProximityRadiusUnits
	(enums.ContentLabelTypeEnum_ContentLabelType)(0),                       // 77: google.ads.googleads.v1.enums.ContentLabelTypeEnum.ContentLabelType
	(enums.WebpageConditionOperandEnum_WebpageConditionOperand)(0),         // 78: google.ads.googleads.v1.enums.WebpageConditionOperandEnum.WebpageConditionOperand
	(enums.WebpageConditionOperatorEnum_WebpageConditionOperator)(0),       // 79: google.ads.googleads.v1.enums.WebpageConditionOperatorEnum.WebpageConditionOperator
	(enums.AppPaymentModelTypeEnum_AppPaymentModelType)(0),                 // 80: google.ads.googleads.v1.enums.AppPaymentModelTypeEnum.AppPaymentModelType
	(enums.LocationGroupRadiusUnitsEnum_LocationGroupRadiusUnits)(0),       // 81: google.ads.googleads.v1.enums.LocationGroupRadiusUnitsEnum.LocationGroupRadiusUnits
}
var file_google_ads_googleads_v1_common_criteria_proto_depIdxs = []int32{
	54, // 0: google.ads.googleads.v1.common.KeywordInfo.text:type_name -> google.protobuf.StringValue
	55, // 1: google.ads.googleads.v1.common.KeywordInfo.match_type:type_name -> google.ads.googleads.v1.enums.KeywordMatchTypeEnum.KeywordMatchType
	54, // 2: google.ads.googleads.v1.common.PlacementInfo.url:type_name -> google.protobuf.StringValue
	54, // 3: google.ads.googleads.v1.common.MobileAppCategoryInfo.mobile_app_category_constant:type_name -> google.protobuf.StringValue
	54, // 4: google.ads.googleads.v1.common.MobileApplicationInfo.app_id:type_name -> google.protobuf.StringValue
	54, // 5: google.ads.googleads.v1.common.MobileApplicationInfo.name:type_name -> google.protobuf.StringValue
	54, // 6: google.ads.googleads.v1.common.LocationInfo.geo_target_constant:type_name -> google.protobuf.StringValue
	56, // 7: google.ads.googleads.v1.common.DeviceInfo.type:type_name -> google.ads.googleads.v1.enums.DeviceEnum.Device
	57, // 8: google.ads.googleads.v1.common.PreferredContentInfo.type:type_name -> google.ads.googleads.v1.enums.PreferredContentTypeEnum.PreferredContentType
	58, // 9: google.ads.googleads.v1.common.ListingGroupInfo.type:type_name -> google.ads.googleads.v1.enums.ListingGroupTypeEnum.ListingGroupType
	9,  // 10: google.ads.googleads.v1.common.ListingGroupInfo.case_value:type_name -> google.ads.googleads.v1.common.ListingDimensionInfo
	54, // 11: google.ads.googleads.v1.common.ListingGroupInfo.parent_ad_group_criterion:type_name -> google.protobuf.StringValue
	9,  // 12: google.ads.googleads.v1.common.ListingScopeInfo.dimensions:type_name -> google.ads.googleads.v1.common.ListingDimensionInfo
	10, // 13: google.ads.googleads.v1.common.ListingDimensionInfo.listing_brand:type_name -> google.ads.googleads.v1.common.ListingBrandInfo
	11, // 14: google.ads.googleads.v1.common.ListingDimensionInfo.hotel_id:type_name -> google.ads.googleads.v1.common.HotelIdInfo
	12, // 15: google.ads.googleads.v1.common.ListingDimensionInfo.hotel_class:type_name -> google.ads.googleads.v1.common.HotelClassInfo
	13, // 16: google.ads.googleads.v1.common.ListingDimensionInfo.hotel_country_region:type_name -> google.ads.googleads.v1.common.HotelCountryRegionInfo
	14, // 17: google.ads.googleads.v1.common.ListingDimensionInfo.hotel_state:type_name -> google.ads.googleads.v1.common.HotelStateInfo
	15, // 18: google.ads.googleads.v1.common.ListingDimensionInfo.hotel_city:type_name -> google.ads.googleads.v1.common.HotelCityInfo
	16, // 19: google.ads.googleads.v1.common.ListingDimensionInfo.listing_custom_attribute:type_name -> google.ads.googleads.v1.common.ListingCustomAttributeInfo
	17, // 20: google.ads.googleads.v1.common.ListingDimensionInfo.product_bidding_category:type_name -> google.ads.googleads.v1.common.ProductBiddingCategoryInfo
	18, // 21: google.ads.googleads.v1.common.ListingDimensionInfo.product_channel:type_name -> google.ads.googleads.v1.common.ProductChannelInfo
	19, // 22: google.ads.googleads.v1.common.ListingDimensionInfo.product_channel_exclusivity:type_name -> google.ads.googleads.v1.common.ProductChannelExclusivityInfo
	20, // 23: google.ads.googleads.v1.common.ListingDimensionInfo.product_condition:type_name -> google.ads.googleads.v1.common.ProductConditionInfo
	21, // 24: google.ads.googleads.v1.common.ListingDimensionInfo.product_item_id:type_name -> google.ads.googleads.v1.common.ProductItemIdInfo
	22, // 25: google.ads.googleads.v1.common.ListingDimensionInfo.product_type:type_name -> google.ads.googleads.v1.common.ProductTypeInfo
	23, // 26: google.ads.googleads.v1.common.ListingDimensionInfo.unknown_listing_dimension:type_name -> google.ads.googleads.v1.common.UnknownListingDimensionInfo
	54, // 27: google.ads.googleads.v1.common.ListingBrandInfo.value:type_name -> google.protobuf.StringValue
	54, // 28: google.ads.googleads.v1.common.HotelIdInfo.value:type_name -> google.protobuf.StringValue
	59, // 29: google.ads.googleads.v1.common.HotelClassInfo.value:type_name -> google.protobuf.Int64Value
	54, // 30: google.ads.googleads.v1.common.HotelCountryRegionInfo.country_region_criterion:type_name -> google.protobuf.StringValue
	54, // 31: google.ads.googleads.v1.common.HotelStateInfo.state_criterion:type_name -> google.protobuf.StringValue
	54, // 32: google.ads.googleads.v1.common.HotelCityInfo.city_criterion:type_name -> google.protobuf.StringValue
	54, // 33: google.ads.googleads.v1.common.ListingCustomAttributeInfo.value:type_name -> google.protobuf.StringValue
	60, // 34: google.ads.googleads.v1.common.ListingCustomAttributeInfo.index:type_name -> google.ads.googleads.v1.enums.ListingCustomAttributeIndexEnum.ListingCustomAttributeIndex
	59, // 35: google.ads.googleads.v1.common.ProductBiddingCategoryInfo.id:type_name -> google.protobuf.Int64Value
	54, // 36: google.ads.googleads.v1.common.ProductBiddingCategoryInfo.country_code:type_name -> google.protobuf.StringValue
	61, // 37: google.ads.googleads.v1.common.ProductBiddingCategoryInfo.level:type_name -> google.ads.googleads.v1.enums.ProductBiddingCategoryLevelEnum.ProductBiddingCategoryLevel
	62, // 38: google.ads.googleads.v1.common.ProductChannelInfo.channel:type_name -> google.ads.googleads.v1.enums.ProductChannelEnum.ProductChannel
	63, // 39: google.ads.googleads.v1.common.ProductChannelExclusivityInfo.channel_exclusivity:type_name -> google.ads.googleads.v1.enums.ProductChannelExclusivityEnum.ProductChannelExclusivity
	64, // 40: google.ads.googleads.v1.common.ProductConditionInfo.condition:type_name -> google.ads.googleads.v1.enums.ProductConditionEnum.ProductCondition
	54, // 41: google.ads.googleads.v1.common.ProductItemIdInfo.value:type_name -> google.protobuf.StringValue
	54, // 42: google.ads.googleads.v1.common.ProductTypeInfo.value:type_name -> google.protobuf.StringValue
	65, // 43: google.ads.googleads.v1.common.ProductTypeInfo.level:type_name -> google.ads.googleads.v1.enums.ProductTypeLevelEnum.ProductTypeLevel
	66, // 44: google.ads.googleads.v1.common.HotelDateSelectionTypeInfo.type:type_name -> google.ads.googleads.v1.enums.HotelDateSelectionTypeEnum.HotelDateSelectionType
	59, // 45: google.ads.googleads.v1.common.HotelAdvanceBookingWindowInfo.min_days:type_name -> google.protobuf.Int64Value
	59, // 46: google.ads.googleads.v1.common.HotelAdvanceBookingWindowInfo.max_days:type_name -> google.protobuf.Int64Value
	59, // 47: google.ads.googleads.v1.common.HotelLengthOfStayInfo.min_nights:type_name -> google.protobuf.Int64Value
	59, // 48: google.ads.googleads.v1.common.HotelLengthOfStayInfo.max_nights:type_name -> google.protobuf.Int64Value
	67, // 49: google.ads.googleads.v1.common.HotelCheckInDayInfo.day_of_week:type_name -> google.ads.googleads.v1.enums.DayOfWeekEnum.DayOfWeek
	68, // 50: google.ads.googleads.v1.common.InteractionTypeInfo.type:type_name -> google.ads.googleads.v1.enums.InteractionTypeEnum.InteractionType
	69, // 51: google.ads.googleads.v1.common.AdScheduleInfo.start_minute:type_name -> google.ads.googleads.v1.enums.MinuteOfHourEnum.MinuteOfHour
	69, // 52: google.ads.googleads.v1.common.AdScheduleInfo.end_minute:type_name -> google.ads.googleads.v1.enums.MinuteOfHourEnum.MinuteOfHour
	70, // 53: google.ads.googleads.v1.common.AdScheduleInfo.start_hour:type_name -> google.protobuf.Int32Value
	70, // 54: google.ads.googleads.v1.common.AdScheduleInfo.end_hour:type_name -> google.protobuf.Int32Value
	67, // 55: google.ads.googleads.v1.common.AdScheduleInfo.day_of_week:type_name -> google.ads.googleads.v1.enums.DayOfWeekEnum.DayOfWeek
	71, // 56: google.ads.googleads.v1.common.AgeRangeInfo.type:type_name -> google.ads.googleads.v1.enums.AgeRangeTypeEnum.AgeRangeType
	72, // 57: google.ads.googleads.v1.common.GenderInfo.type:type_name -> google.ads.googleads.v1.enums.GenderTypeEnum.GenderType
	73, // 58: google.ads.googleads.v1.common.IncomeRangeInfo.type:type_name -> google.ads.googleads.v1.enums.IncomeRangeTypeEnum.IncomeRangeType
	74, // 59: google.ads.googleads.v1.common.ParentalStatusInfo.type:type_name -> google.ads.googleads.v1.enums.ParentalStatusTypeEnum.ParentalStatusType
	54, // 60: google.ads.googleads.v1.common.YouTubeVideoInfo.video_id:type_name -> google.protobuf.StringValue
	54, // 61: google.ads.googleads.v1.common.YouTubeChannelInfo.channel_id:type_name -> google.protobuf.StringValue
	54, // 62: google.ads.googleads.v1.common.UserListInfo.user_list:type_name -> google.protobuf.StringValue
	38, // 63: google.ads.googleads.v1.common.ProximityInfo.geo_point:type_name -> google.ads.googleads.v1.common.GeoPointInfo
	75, // 64: google.ads.googleads.v1.common.ProximityInfo.radius:type_name -> google.protobuf.DoubleValue
	76, // 65: google.ads.googleads.v1.common.ProximityInfo.radius_units:type_name -> google.ads.googleads.v1.enums.ProximityRadiusUnitsEnum.ProximityRadiusUnits
	39, // 66: google.ads.googleads.v1.common.ProximityInfo.address:type_name -> google.ads.googleads.v1.common.AddressInfo
	70, // 67: google.ads.googleads.v1.common.GeoPointInfo.longitude_in_micro_degrees:type_name -> google.protobuf.Int32Value
	70, // 68: google.ads.googleads.v1.common.GeoPointInfo.latitude_in_micro_degrees:type_name -> google.protobuf.Int32Value
	54, // 69: google.ads.googleads.v1.common.AddressInfo.postal_code:type_name -> google.protobuf.StringValue
	54, // 70: google.ads.googleads.v1.common.AddressInfo.province_code:type_name -> google.protobuf.StringValue
	54, // 71: google.ads.googleads.v1.common.AddressInfo.country_code:type_name -> google.protobuf.StringValue
	54, // 72: google.ads.googleads.v1.common.AddressInfo.province_name:type_name -> google.protobuf.StringValue
	54, // 73: google.ads.googleads.v1.common.AddressInfo.street_address:type_name -> google.protobuf.StringValue
	54, // 74: google.ads.googleads.v1.common.AddressInfo.street_address2:type_name -> google.protobuf.StringValue
	54, // 75: google.ads.googleads.v1.common.AddressInfo.city_name:type_name -> google.protobuf.StringValue
	54, // 76: google.ads.googleads.v1.common.TopicInfo.topic_constant:type_name -> google.protobuf.StringValue
	54, // 77: google.ads.googleads.v1.common.TopicInfo.path:type_name -> google.protobuf.StringValue
	54, // 78: google.ads.googleads.v1.common.LanguageInfo.language_constant:type_name -> google.protobuf.StringValue
	54, // 79: google.ads.googleads.v1.common.IpBlockInfo.ip_address:type_name -> google.protobuf.StringValue
	77, // 80: google.ads.googleads.v1.common.ContentLabelInfo.type:type_name -> google.ads.googleads.v1.enums.ContentLabelTypeEnum.ContentLabelType
	54, // 81: google.ads.googleads.v1.common.CarrierInfo.carrier_constant:type_name -> google.protobuf.StringValue
	54, // 82: google.ads.googleads.v1.common.UserInterestInfo.user_interest_category:type_name -> google.protobuf.StringValue
	54, // 83: google.ads.googleads.v1.common.WebpageInfo.criterion_name:type_name -> google.protobuf.StringValue
	47, // 84: google.ads.googleads.v1.common.WebpageInfo.conditions:type_name -> google.ads.googleads.v1.common.WebpageConditionInfo
	78, // 85: google.ads.googleads.v1.common.WebpageConditionInfo.operand:type_name -> google.ads.googleads.v1.enums.WebpageConditionOperandEnum.WebpageConditionOperand
	79, // 86: google.ads.googleads.v1.common.WebpageConditionInfo.operator:type_name -> google.ads.googleads.v1.enums.WebpageConditionOperatorEnum.WebpageConditionOperator
	54, // 87: google.ads.googleads.v1.common.WebpageConditionInfo.argument:type_name -> google.protobuf.StringValue
	54, // 88: google.ads.googleads.v1.common.OperatingSystemVersionInfo.operating_system_version_constant:type_name -> google.protobuf.StringValue
	80, // 89: google.ads.googleads.v1.common.AppPaymentModelInfo.type:type_name -> google.ads.googleads.v1.enums.AppPaymentModelTypeEnum.AppPaymentModelType
	54, // 90: google.ads.googleads.v1.common.MobileDeviceInfo.mobile_device_constant:type_name -> google.protobuf.StringValue
	54, // 91: google.ads.googleads.v1.common.CustomAffinityInfo.custom_affinity:type_name -> google.protobuf.StringValue
	54, // 92: google.ads.googleads.v1.common.CustomIntentInfo.custom_intent:type_name -> google.protobuf.StringValue
	54, // 93: google.ads.googleads.v1.common.LocationGroupInfo.feed:type_name -> google.protobuf.StringValue
	54, // 94: google.ads.googleads.v1.common.LocationGroupInfo.geo_target_constants:type_name -> google.protobuf.StringValue
	59, // 95: google.ads.googleads.v1.common.LocationGroupInfo.radius:type_name -> google.protobuf.Int64Value
	81, // 96: google.ads.googleads.v1.common.LocationGroupInfo.radius_units:type_name -> google.ads.googleads.v1.enums.LocationGroupRadiusUnitsEnum.LocationGroupRadiusUnits
	97, // [97:97] is the sub-list for method output_type
	97, // [97:97] is the sub-list for method input_type
	97, // [97:97] is the sub-list for extension type_name
	97, // [97:97] is the sub-list for extension extendee
	0,  // [0:97] is the sub-list for field type_name
}

func init() { file_google_ads_googleads_v1_common_criteria_proto_init() }
func file_google_ads_googleads_v1_common_criteria_proto_init() {
	if File_google_ads_googleads_v1_common_criteria_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeywordInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlacementInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MobileAppCategoryInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MobileApplicationInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreferredContentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListingGroupInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListingScopeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListingDimensionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListingBrandInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotelIdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotelClassInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotelCountryRegionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotelStateInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotelCityInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListingCustomAttributeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductBiddingCategoryInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductChannelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductChannelExclusivityInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductConditionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductItemIdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductTypeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnknownListingDimensionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotelDateSelectionTypeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotelAdvanceBookingWindowInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotelLengthOfStayInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotelCheckInDayInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InteractionTypeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdScheduleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgeRangeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenderInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncomeRangeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParentalStatusInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*YouTubeVideoInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*YouTubeChannelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserListInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProximityInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeoPointInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TopicInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LanguageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IpBlockInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContentLabelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarrierInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInterestInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebpageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebpageConditionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperatingSystemVersionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppPaymentModelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MobileDeviceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomAffinityInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomIntentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_ads_googleads_v1_common_criteria_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationGroupInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_ads_googleads_v1_common_criteria_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*ListingDimensionInfo_ListingBrand)(nil),
		(*ListingDimensionInfo_HotelId)(nil),
		(*ListingDimensionInfo_HotelClass)(nil),
		(*ListingDimensionInfo_HotelCountryRegion)(nil),
		(*ListingDimensionInfo_HotelState)(nil),
		(*ListingDimensionInfo_HotelCity)(nil),
		(*ListingDimensionInfo_ListingCustomAttribute)(nil),
		(*ListingDimensionInfo_ProductBiddingCategory)(nil),
		(*ListingDimensionInfo_ProductChannel)(nil),
		(*ListingDimensionInfo_ProductChannelExclusivity)(nil),
		(*ListingDimensionInfo_ProductCondition)(nil),
		(*ListingDimensionInfo_ProductItemId)(nil),
		(*ListingDimensionInfo_ProductType)(nil),
		(*ListingDimensionInfo_UnknownListingDimension)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_ads_googleads_v1_common_criteria_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   54,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_ads_googleads_v1_common_criteria_proto_goTypes,
		DependencyIndexes: file_google_ads_googleads_v1_common_criteria_proto_depIdxs,
		MessageInfos:      file_google_ads_googleads_v1_common_criteria_proto_msgTypes,
	}.Build()
	File_google_ads_googleads_v1_common_criteria_proto = out.File
	file_google_ads_googleads_v1_common_criteria_proto_rawDesc = nil
	file_google_ads_googleads_v1_common_criteria_proto_goTypes = nil
	file_google_ads_googleads_v1_common_criteria_proto_depIdxs = nil
}
