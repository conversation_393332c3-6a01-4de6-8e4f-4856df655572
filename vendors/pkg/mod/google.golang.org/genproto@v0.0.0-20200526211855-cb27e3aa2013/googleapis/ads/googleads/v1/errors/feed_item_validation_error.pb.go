// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.22.0
// 	protoc        v3.11.2
// source: google/ads/googleads/v1/errors/feed_item_validation_error.proto

package errors

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// The possible validation errors of a feed item.
type FeedItemValidationErrorEnum_FeedItemValidationError int32

const (
	// No value has been specified.
	FeedItemValidationErrorEnum_UNSPECIFIED FeedItemValidationErrorEnum_FeedItemValidationError = 0
	// Used for return value only. Represents value unknown in this version.
	FeedItemValidationErrorEnum_UNKNOWN FeedItemValidationErrorEnum_FeedItemValidationError = 1
	// String is too short.
	FeedItemValidationErrorEnum_STRING_TOO_SHORT FeedItemValidationErrorEnum_FeedItemValidationError = 2
	// String is too long.
	FeedItemValidationErrorEnum_STRING_TOO_LONG FeedItemValidationErrorEnum_FeedItemValidationError = 3
	// Value is not provided.
	FeedItemValidationErrorEnum_VALUE_NOT_SPECIFIED FeedItemValidationErrorEnum_FeedItemValidationError = 4
	// Phone number format is invalid for region.
	FeedItemValidationErrorEnum_INVALID_DOMESTIC_PHONE_NUMBER_FORMAT FeedItemValidationErrorEnum_FeedItemValidationError = 5
	// String does not represent a phone number.
	FeedItemValidationErrorEnum_INVALID_PHONE_NUMBER FeedItemValidationErrorEnum_FeedItemValidationError = 6
	// Phone number format is not compatible with country code.
	FeedItemValidationErrorEnum_PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRY FeedItemValidationErrorEnum_FeedItemValidationError = 7
	// Premium rate number is not allowed.
	FeedItemValidationErrorEnum_PREMIUM_RATE_NUMBER_NOT_ALLOWED FeedItemValidationErrorEnum_FeedItemValidationError = 8
	// Phone number type is not allowed.
	FeedItemValidationErrorEnum_DISALLOWED_NUMBER_TYPE FeedItemValidationErrorEnum_FeedItemValidationError = 9
	// Specified value is outside of the valid range.
	FeedItemValidationErrorEnum_VALUE_OUT_OF_RANGE FeedItemValidationErrorEnum_FeedItemValidationError = 10
	// Call tracking is not supported in the selected country.
	FeedItemValidationErrorEnum_CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY FeedItemValidationErrorEnum_FeedItemValidationError = 11
	// Customer is not whitelisted for call tracking.
	FeedItemValidationErrorEnum_CUSTOMER_NOT_WHITELISTED_FOR_CALLTRACKING FeedItemValidationErrorEnum_FeedItemValidationError = 12
	// Country code is invalid.
	FeedItemValidationErrorEnum_INVALID_COUNTRY_CODE FeedItemValidationErrorEnum_FeedItemValidationError = 13
	// The specified mobile app id is invalid.
	FeedItemValidationErrorEnum_INVALID_APP_ID FeedItemValidationErrorEnum_FeedItemValidationError = 14
	// Some required field attributes are missing.
	FeedItemValidationErrorEnum_MISSING_ATTRIBUTES_FOR_FIELDS FeedItemValidationErrorEnum_FeedItemValidationError = 15
	// Invalid email button type for email extension.
	FeedItemValidationErrorEnum_INVALID_TYPE_ID FeedItemValidationErrorEnum_FeedItemValidationError = 16
	// Email address is invalid.
	FeedItemValidationErrorEnum_INVALID_EMAIL_ADDRESS FeedItemValidationErrorEnum_FeedItemValidationError = 17
	// The HTTPS URL in email extension is invalid.
	FeedItemValidationErrorEnum_INVALID_HTTPS_URL FeedItemValidationErrorEnum_FeedItemValidationError = 18
	// Delivery address is missing from email extension.
	FeedItemValidationErrorEnum_MISSING_DELIVERY_ADDRESS FeedItemValidationErrorEnum_FeedItemValidationError = 19
	// FeedItem scheduling start date comes after end date.
	FeedItemValidationErrorEnum_START_DATE_AFTER_END_DATE FeedItemValidationErrorEnum_FeedItemValidationError = 20
	// FeedItem scheduling start time is missing.
	FeedItemValidationErrorEnum_MISSING_FEED_ITEM_START_TIME FeedItemValidationErrorEnum_FeedItemValidationError = 21
	// FeedItem scheduling end time is missing.
	FeedItemValidationErrorEnum_MISSING_FEED_ITEM_END_TIME FeedItemValidationErrorEnum_FeedItemValidationError = 22
	// Cannot compute system attributes on a FeedItem that has no FeedItemId.
	FeedItemValidationErrorEnum_MISSING_FEED_ITEM_ID FeedItemValidationErrorEnum_FeedItemValidationError = 23
	// Call extension vanity phone numbers are not supported.
	FeedItemValidationErrorEnum_VANITY_PHONE_NUMBER_NOT_ALLOWED FeedItemValidationErrorEnum_FeedItemValidationError = 24
	// Invalid review text.
	FeedItemValidationErrorEnum_INVALID_REVIEW_EXTENSION_SNIPPET FeedItemValidationErrorEnum_FeedItemValidationError = 25
	// Invalid format for numeric value in ad parameter.
	FeedItemValidationErrorEnum_INVALID_NUMBER_FORMAT FeedItemValidationErrorEnum_FeedItemValidationError = 26
	// Invalid format for date value in ad parameter.
	FeedItemValidationErrorEnum_INVALID_DATE_FORMAT FeedItemValidationErrorEnum_FeedItemValidationError = 27
	// Invalid format for price value in ad parameter.
	FeedItemValidationErrorEnum_INVALID_PRICE_FORMAT FeedItemValidationErrorEnum_FeedItemValidationError = 28
	// Unrecognized type given for value in ad parameter.
	FeedItemValidationErrorEnum_UNKNOWN_PLACEHOLDER_FIELD FeedItemValidationErrorEnum_FeedItemValidationError = 29
	// Enhanced sitelinks must have both description lines specified.
	FeedItemValidationErrorEnum_MISSING_ENHANCED_SITELINK_DESCRIPTION_LINE FeedItemValidationErrorEnum_FeedItemValidationError = 30
	// Review source is ineligible.
	FeedItemValidationErrorEnum_REVIEW_EXTENSION_SOURCE_INELIGIBLE FeedItemValidationErrorEnum_FeedItemValidationError = 31
	// Review text cannot contain hyphens or dashes.
	FeedItemValidationErrorEnum_HYPHENS_IN_REVIEW_EXTENSION_SNIPPET FeedItemValidationErrorEnum_FeedItemValidationError = 32
	// Review text cannot contain double quote characters.
	FeedItemValidationErrorEnum_DOUBLE_QUOTES_IN_REVIEW_EXTENSION_SNIPPET FeedItemValidationErrorEnum_FeedItemValidationError = 33
	// Review text cannot contain quote characters.
	FeedItemValidationErrorEnum_QUOTES_IN_REVIEW_EXTENSION_SNIPPET FeedItemValidationErrorEnum_FeedItemValidationError = 34
	// Parameters are encoded in the wrong format.
	FeedItemValidationErrorEnum_INVALID_FORM_ENCODED_PARAMS FeedItemValidationErrorEnum_FeedItemValidationError = 35
	// URL parameter name must contain only letters, numbers, underscores, and
	// dashes.
	FeedItemValidationErrorEnum_INVALID_URL_PARAMETER_NAME FeedItemValidationErrorEnum_FeedItemValidationError = 36
	// Cannot find address location.
	FeedItemValidationErrorEnum_NO_GEOCODING_RESULT FeedItemValidationErrorEnum_FeedItemValidationError = 37
	// Review extension text has source name.
	FeedItemValidationErrorEnum_SOURCE_NAME_IN_REVIEW_EXTENSION_TEXT FeedItemValidationErrorEnum_FeedItemValidationError = 38
	// Some phone numbers can be shorter than usual. Some of these short numbers
	// are carrier-specific, and we disallow those in ad extensions because they
	// will not be available to all users.
	FeedItemValidationErrorEnum_CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWED FeedItemValidationErrorEnum_FeedItemValidationError = 39
	// Triggered when a request references a placeholder field id that does not
	// exist.
	FeedItemValidationErrorEnum_INVALID_PLACEHOLDER_FIELD_ID FeedItemValidationErrorEnum_FeedItemValidationError = 40
	// URL contains invalid ValueTrack tags or format.
	FeedItemValidationErrorEnum_INVALID_URL_TAG FeedItemValidationErrorEnum_FeedItemValidationError = 41
	// Provided list exceeds acceptable size.
	FeedItemValidationErrorEnum_LIST_TOO_LONG FeedItemValidationErrorEnum_FeedItemValidationError = 42
	// Certain combinations of attributes aren't allowed to be specified in the
	// same feed item.
	FeedItemValidationErrorEnum_INVALID_ATTRIBUTES_COMBINATION FeedItemValidationErrorEnum_FeedItemValidationError = 43
	// An attribute has the same value repeatedly.
	FeedItemValidationErrorEnum_DUPLICATE_VALUES FeedItemValidationErrorEnum_FeedItemValidationError = 44
	// Advertisers can link a conversion action with a phone number to indicate
	// that sufficiently long calls forwarded to that phone number should be
	// counted as conversions of the specified type.  This is an error message
	// indicating that the conversion action specified is invalid (e.g., the
	// conversion action does not exist within the appropriate Google Ads
	// account, or it is a type of conversion not appropriate to phone call
	// conversions).
	FeedItemValidationErrorEnum_INVALID_CALL_CONVERSION_ACTION_ID FeedItemValidationErrorEnum_FeedItemValidationError = 45
	// Tracking template requires final url to be set.
	FeedItemValidationErrorEnum_CANNOT_SET_WITHOUT_FINAL_URLS FeedItemValidationErrorEnum_FeedItemValidationError = 46
	// An app id was provided that doesn't exist in the given app store.
	FeedItemValidationErrorEnum_APP_ID_DOESNT_EXIST_IN_APP_STORE FeedItemValidationErrorEnum_FeedItemValidationError = 47
	// Invalid U2 final url.
	FeedItemValidationErrorEnum_INVALID_FINAL_URL FeedItemValidationErrorEnum_FeedItemValidationError = 48
	// Invalid U2 tracking url.
	FeedItemValidationErrorEnum_INVALID_TRACKING_URL FeedItemValidationErrorEnum_FeedItemValidationError = 49
	// Final URL should start from App download URL.
	FeedItemValidationErrorEnum_INVALID_FINAL_URL_FOR_APP_DOWNLOAD_URL FeedItemValidationErrorEnum_FeedItemValidationError = 50
	// List provided is too short.
	FeedItemValidationErrorEnum_LIST_TOO_SHORT FeedItemValidationErrorEnum_FeedItemValidationError = 51
	// User Action field has invalid value.
	FeedItemValidationErrorEnum_INVALID_USER_ACTION FeedItemValidationErrorEnum_FeedItemValidationError = 52
	// Type field has invalid value.
	FeedItemValidationErrorEnum_INVALID_TYPE_NAME FeedItemValidationErrorEnum_FeedItemValidationError = 53
	// Change status for event is invalid.
	FeedItemValidationErrorEnum_INVALID_EVENT_CHANGE_STATUS FeedItemValidationErrorEnum_FeedItemValidationError = 54
	// The header of a structured snippets extension is not one of the valid
	// headers.
	FeedItemValidationErrorEnum_INVALID_SNIPPETS_HEADER FeedItemValidationErrorEnum_FeedItemValidationError = 55
	// Android app link is not formatted correctly
	FeedItemValidationErrorEnum_INVALID_ANDROID_APP_LINK FeedItemValidationErrorEnum_FeedItemValidationError = 56
	// Phone number incompatible with call tracking for country.
	FeedItemValidationErrorEnum_NUMBER_TYPE_WITH_CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY FeedItemValidationErrorEnum_FeedItemValidationError = 57
	// The input is identical to a reserved keyword
	FeedItemValidationErrorEnum_RESERVED_KEYWORD_OTHER FeedItemValidationErrorEnum_FeedItemValidationError = 58
	// Each option label in the message extension must be unique.
	FeedItemValidationErrorEnum_DUPLICATE_OPTION_LABELS FeedItemValidationErrorEnum_FeedItemValidationError = 59
	// Each option prefill in the message extension must be unique.
	FeedItemValidationErrorEnum_DUPLICATE_OPTION_PREFILLS FeedItemValidationErrorEnum_FeedItemValidationError = 60
	// In message extensions, the number of optional labels and optional
	// prefills must be the same.
	FeedItemValidationErrorEnum_UNEQUAL_LIST_LENGTHS FeedItemValidationErrorEnum_FeedItemValidationError = 61
	// All currency codes in an ad extension must be the same.
	FeedItemValidationErrorEnum_INCONSISTENT_CURRENCY_CODES FeedItemValidationErrorEnum_FeedItemValidationError = 62
	// Headers in price extension are not unique.
	FeedItemValidationErrorEnum_PRICE_EXTENSION_HAS_DUPLICATED_HEADERS FeedItemValidationErrorEnum_FeedItemValidationError = 63
	// Header and description in an item are the same.
	FeedItemValidationErrorEnum_ITEM_HAS_DUPLICATED_HEADER_AND_DESCRIPTION FeedItemValidationErrorEnum_FeedItemValidationError = 64
	// Price extension has too few items.
	FeedItemValidationErrorEnum_PRICE_EXTENSION_HAS_TOO_FEW_ITEMS FeedItemValidationErrorEnum_FeedItemValidationError = 65
	// The given value is not supported.
	FeedItemValidationErrorEnum_UNSUPPORTED_VALUE FeedItemValidationErrorEnum_FeedItemValidationError = 66
	// Invalid final mobile url.
	FeedItemValidationErrorEnum_INVALID_FINAL_MOBILE_URL FeedItemValidationErrorEnum_FeedItemValidationError = 67
	// The given string value of Label contains invalid characters
	FeedItemValidationErrorEnum_INVALID_KEYWORDLESS_AD_RULE_LABEL FeedItemValidationErrorEnum_FeedItemValidationError = 68
	// The given URL contains value track parameters.
	FeedItemValidationErrorEnum_VALUE_TRACK_PARAMETER_NOT_SUPPORTED FeedItemValidationErrorEnum_FeedItemValidationError = 69
	// The given value is not supported in the selected language of an
	// extension.
	FeedItemValidationErrorEnum_UNSUPPORTED_VALUE_IN_SELECTED_LANGUAGE FeedItemValidationErrorEnum_FeedItemValidationError = 70
	// The iOS app link is not formatted correctly.
	FeedItemValidationErrorEnum_INVALID_IOS_APP_LINK FeedItemValidationErrorEnum_FeedItemValidationError = 71
	// iOS app link or iOS app store id is missing.
	FeedItemValidationErrorEnum_MISSING_IOS_APP_LINK_OR_IOS_APP_STORE_ID FeedItemValidationErrorEnum_FeedItemValidationError = 72
	// Promotion time is invalid.
	FeedItemValidationErrorEnum_PROMOTION_INVALID_TIME FeedItemValidationErrorEnum_FeedItemValidationError = 73
	// Both the percent off and money amount off fields are set.
	FeedItemValidationErrorEnum_PROMOTION_CANNOT_SET_PERCENT_OFF_AND_MONEY_AMOUNT_OFF FeedItemValidationErrorEnum_FeedItemValidationError = 74
	// Both the promotion code and orders over amount fields are set.
	FeedItemValidationErrorEnum_PROMOTION_CANNOT_SET_PROMOTION_CODE_AND_ORDERS_OVER_AMOUNT FeedItemValidationErrorEnum_FeedItemValidationError = 75
	// Too many decimal places are specified.
	FeedItemValidationErrorEnum_TOO_MANY_DECIMAL_PLACES_SPECIFIED FeedItemValidationErrorEnum_FeedItemValidationError = 76
	// Ad Customizers are present and not allowed.
	FeedItemValidationErrorEnum_AD_CUSTOMIZERS_NOT_ALLOWED FeedItemValidationErrorEnum_FeedItemValidationError = 77
	// Language code is not valid.
	FeedItemValidationErrorEnum_INVALID_LANGUAGE_CODE FeedItemValidationErrorEnum_FeedItemValidationError = 78
	// Language is not supported.
	FeedItemValidationErrorEnum_UNSUPPORTED_LANGUAGE FeedItemValidationErrorEnum_FeedItemValidationError = 79
	// IF Function is present and not allowed.
	FeedItemValidationErrorEnum_IF_FUNCTION_NOT_ALLOWED FeedItemValidationErrorEnum_FeedItemValidationError = 80
	// Final url suffix is not valid.
	FeedItemValidationErrorEnum_INVALID_FINAL_URL_SUFFIX FeedItemValidationErrorEnum_FeedItemValidationError = 81
	// Final url suffix contains an invalid tag.
	FeedItemValidationErrorEnum_INVALID_TAG_IN_FINAL_URL_SUFFIX FeedItemValidationErrorEnum_FeedItemValidationError = 82
	// Final url suffix is formatted incorrectly.
	FeedItemValidationErrorEnum_INVALID_FINAL_URL_SUFFIX_FORMAT FeedItemValidationErrorEnum_FeedItemValidationError = 83
	// Consent for call recording, which is required for the use of call
	// extensions, was not provided by the advertiser. Please see
	// https://support.google.com/google-ads/answer/7412639.
	FeedItemValidationErrorEnum_CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIRED FeedItemValidationErrorEnum_FeedItemValidationError = 84
	// Multiple message delivery options are set.
	FeedItemValidationErrorEnum_ONLY_ONE_DELIVERY_OPTION_IS_ALLOWED FeedItemValidationErrorEnum_FeedItemValidationError = 85
	// No message delivery option is set.
	FeedItemValidationErrorEnum_NO_DELIVERY_OPTION_IS_SET FeedItemValidationErrorEnum_FeedItemValidationError = 86
	// String value of conversion reporting state field is not valid.
	FeedItemValidationErrorEnum_INVALID_CONVERSION_REPORTING_STATE FeedItemValidationErrorEnum_FeedItemValidationError = 87
	// Image size is not right.
	FeedItemValidationErrorEnum_IMAGE_SIZE_WRONG FeedItemValidationErrorEnum_FeedItemValidationError = 88
	// Email delivery is not supported in the country specified in the country
	// code field.
	FeedItemValidationErrorEnum_EMAIL_DELIVERY_NOT_AVAILABLE_IN_COUNTRY FeedItemValidationErrorEnum_FeedItemValidationError = 89
	// Auto reply is not supported in the country specified in the country code
	// field.
	FeedItemValidationErrorEnum_AUTO_REPLY_NOT_AVAILABLE_IN_COUNTRY FeedItemValidationErrorEnum_FeedItemValidationError = 90
	// Invalid value specified for latitude.
	FeedItemValidationErrorEnum_INVALID_LATITUDE_VALUE FeedItemValidationErrorEnum_FeedItemValidationError = 91
	// Invalid value specified for longitude.
	FeedItemValidationErrorEnum_INVALID_LONGITUDE_VALUE FeedItemValidationErrorEnum_FeedItemValidationError = 92
	// Too many label fields provided.
	FeedItemValidationErrorEnum_TOO_MANY_LABELS FeedItemValidationErrorEnum_FeedItemValidationError = 93
	// Invalid image url.
	FeedItemValidationErrorEnum_INVALID_IMAGE_URL FeedItemValidationErrorEnum_FeedItemValidationError = 94
	// Latitude value is missing.
	FeedItemValidationErrorEnum_MISSING_LATITUDE_VALUE FeedItemValidationErrorEnum_FeedItemValidationError = 95
	// Longitude value is missing.
	FeedItemValidationErrorEnum_MISSING_LONGITUDE_VALUE FeedItemValidationErrorEnum_FeedItemValidationError = 96
)

// Enum value maps for FeedItemValidationErrorEnum_FeedItemValidationError.
var (
	FeedItemValidationErrorEnum_FeedItemValidationError_name = map[int32]string{
		0:  "UNSPECIFIED",
		1:  "UNKNOWN",
		2:  "STRING_TOO_SHORT",
		3:  "STRING_TOO_LONG",
		4:  "VALUE_NOT_SPECIFIED",
		5:  "INVALID_DOMESTIC_PHONE_NUMBER_FORMAT",
		6:  "INVALID_PHONE_NUMBER",
		7:  "PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRY",
		8:  "PREMIUM_RATE_NUMBER_NOT_ALLOWED",
		9:  "DISALLOWED_NUMBER_TYPE",
		10: "VALUE_OUT_OF_RANGE",
		11: "CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY",
		12: "CUSTOMER_NOT_WHITELISTED_FOR_CALLTRACKING",
		13: "INVALID_COUNTRY_CODE",
		14: "INVALID_APP_ID",
		15: "MISSING_ATTRIBUTES_FOR_FIELDS",
		16: "INVALID_TYPE_ID",
		17: "INVALID_EMAIL_ADDRESS",
		18: "INVALID_HTTPS_URL",
		19: "MISSING_DELIVERY_ADDRESS",
		20: "START_DATE_AFTER_END_DATE",
		21: "MISSING_FEED_ITEM_START_TIME",
		22: "MISSING_FEED_ITEM_END_TIME",
		23: "MISSING_FEED_ITEM_ID",
		24: "VANITY_PHONE_NUMBER_NOT_ALLOWED",
		25: "INVALID_REVIEW_EXTENSION_SNIPPET",
		26: "INVALID_NUMBER_FORMAT",
		27: "INVALID_DATE_FORMAT",
		28: "INVALID_PRICE_FORMAT",
		29: "UNKNOWN_PLACEHOLDER_FIELD",
		30: "MISSING_ENHANCED_SITELINK_DESCRIPTION_LINE",
		31: "REVIEW_EXTENSION_SOURCE_INELIGIBLE",
		32: "HYPHENS_IN_REVIEW_EXTENSION_SNIPPET",
		33: "DOUBLE_QUOTES_IN_REVIEW_EXTENSION_SNIPPET",
		34: "QUOTES_IN_REVIEW_EXTENSION_SNIPPET",
		35: "INVALID_FORM_ENCODED_PARAMS",
		36: "INVALID_URL_PARAMETER_NAME",
		37: "NO_GEOCODING_RESULT",
		38: "SOURCE_NAME_IN_REVIEW_EXTENSION_TEXT",
		39: "CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWED",
		40: "INVALID_PLACEHOLDER_FIELD_ID",
		41: "INVALID_URL_TAG",
		42: "LIST_TOO_LONG",
		43: "INVALID_ATTRIBUTES_COMBINATION",
		44: "DUPLICATE_VALUES",
		45: "INVALID_CALL_CONVERSION_ACTION_ID",
		46: "CANNOT_SET_WITHOUT_FINAL_URLS",
		47: "APP_ID_DOESNT_EXIST_IN_APP_STORE",
		48: "INVALID_FINAL_URL",
		49: "INVALID_TRACKING_URL",
		50: "INVALID_FINAL_URL_FOR_APP_DOWNLOAD_URL",
		51: "LIST_TOO_SHORT",
		52: "INVALID_USER_ACTION",
		53: "INVALID_TYPE_NAME",
		54: "INVALID_EVENT_CHANGE_STATUS",
		55: "INVALID_SNIPPETS_HEADER",
		56: "INVALID_ANDROID_APP_LINK",
		57: "NUMBER_TYPE_WITH_CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY",
		58: "RESERVED_KEYWORD_OTHER",
		59: "DUPLICATE_OPTION_LABELS",
		60: "DUPLICATE_OPTION_PREFILLS",
		61: "UNEQUAL_LIST_LENGTHS",
		62: "INCONSISTENT_CURRENCY_CODES",
		63: "PRICE_EXTENSION_HAS_DUPLICATED_HEADERS",
		64: "ITEM_HAS_DUPLICATED_HEADER_AND_DESCRIPTION",
		65: "PRICE_EXTENSION_HAS_TOO_FEW_ITEMS",
		66: "UNSUPPORTED_VALUE",
		67: "INVALID_FINAL_MOBILE_URL",
		68: "INVALID_KEYWORDLESS_AD_RULE_LABEL",
		69: "VALUE_TRACK_PARAMETER_NOT_SUPPORTED",
		70: "UNSUPPORTED_VALUE_IN_SELECTED_LANGUAGE",
		71: "INVALID_IOS_APP_LINK",
		72: "MISSING_IOS_APP_LINK_OR_IOS_APP_STORE_ID",
		73: "PROMOTION_INVALID_TIME",
		74: "PROMOTION_CANNOT_SET_PERCENT_OFF_AND_MONEY_AMOUNT_OFF",
		75: "PROMOTION_CANNOT_SET_PROMOTION_CODE_AND_ORDERS_OVER_AMOUNT",
		76: "TOO_MANY_DECIMAL_PLACES_SPECIFIED",
		77: "AD_CUSTOMIZERS_NOT_ALLOWED",
		78: "INVALID_LANGUAGE_CODE",
		79: "UNSUPPORTED_LANGUAGE",
		80: "IF_FUNCTION_NOT_ALLOWED",
		81: "INVALID_FINAL_URL_SUFFIX",
		82: "INVALID_TAG_IN_FINAL_URL_SUFFIX",
		83: "INVALID_FINAL_URL_SUFFIX_FORMAT",
		84: "CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIRED",
		85: "ONLY_ONE_DELIVERY_OPTION_IS_ALLOWED",
		86: "NO_DELIVERY_OPTION_IS_SET",
		87: "INVALID_CONVERSION_REPORTING_STATE",
		88: "IMAGE_SIZE_WRONG",
		89: "EMAIL_DELIVERY_NOT_AVAILABLE_IN_COUNTRY",
		90: "AUTO_REPLY_NOT_AVAILABLE_IN_COUNTRY",
		91: "INVALID_LATITUDE_VALUE",
		92: "INVALID_LONGITUDE_VALUE",
		93: "TOO_MANY_LABELS",
		94: "INVALID_IMAGE_URL",
		95: "MISSING_LATITUDE_VALUE",
		96: "MISSING_LONGITUDE_VALUE",
	}
	FeedItemValidationErrorEnum_FeedItemValidationError_value = map[string]int32{
		"UNSPECIFIED":                                                0,
		"UNKNOWN":                                                    1,
		"STRING_TOO_SHORT":                                           2,
		"STRING_TOO_LONG":                                            3,
		"VALUE_NOT_SPECIFIED":                                        4,
		"INVALID_DOMESTIC_PHONE_NUMBER_FORMAT":                       5,
		"INVALID_PHONE_NUMBER":                                       6,
		"PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRY":                     7,
		"PREMIUM_RATE_NUMBER_NOT_ALLOWED":                            8,
		"DISALLOWED_NUMBER_TYPE":                                     9,
		"VALUE_OUT_OF_RANGE":                                         10,
		"CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY":                     11,
		"CUSTOMER_NOT_WHITELISTED_FOR_CALLTRACKING":                  12,
		"INVALID_COUNTRY_CODE":                                       13,
		"INVALID_APP_ID":                                             14,
		"MISSING_ATTRIBUTES_FOR_FIELDS":                              15,
		"INVALID_TYPE_ID":                                            16,
		"INVALID_EMAIL_ADDRESS":                                      17,
		"INVALID_HTTPS_URL":                                          18,
		"MISSING_DELIVERY_ADDRESS":                                   19,
		"START_DATE_AFTER_END_DATE":                                  20,
		"MISSING_FEED_ITEM_START_TIME":                               21,
		"MISSING_FEED_ITEM_END_TIME":                                 22,
		"MISSING_FEED_ITEM_ID":                                       23,
		"VANITY_PHONE_NUMBER_NOT_ALLOWED":                            24,
		"INVALID_REVIEW_EXTENSION_SNIPPET":                           25,
		"INVALID_NUMBER_FORMAT":                                      26,
		"INVALID_DATE_FORMAT":                                        27,
		"INVALID_PRICE_FORMAT":                                       28,
		"UNKNOWN_PLACEHOLDER_FIELD":                                  29,
		"MISSING_ENHANCED_SITELINK_DESCRIPTION_LINE":                 30,
		"REVIEW_EXTENSION_SOURCE_INELIGIBLE":                         31,
		"HYPHENS_IN_REVIEW_EXTENSION_SNIPPET":                        32,
		"DOUBLE_QUOTES_IN_REVIEW_EXTENSION_SNIPPET":                  33,
		"QUOTES_IN_REVIEW_EXTENSION_SNIPPET":                         34,
		"INVALID_FORM_ENCODED_PARAMS":                                35,
		"INVALID_URL_PARAMETER_NAME":                                 36,
		"NO_GEOCODING_RESULT":                                        37,
		"SOURCE_NAME_IN_REVIEW_EXTENSION_TEXT":                       38,
		"CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWED":                  39,
		"INVALID_PLACEHOLDER_FIELD_ID":                               40,
		"INVALID_URL_TAG":                                            41,
		"LIST_TOO_LONG":                                              42,
		"INVALID_ATTRIBUTES_COMBINATION":                             43,
		"DUPLICATE_VALUES":                                           44,
		"INVALID_CALL_CONVERSION_ACTION_ID":                          45,
		"CANNOT_SET_WITHOUT_FINAL_URLS":                              46,
		"APP_ID_DOESNT_EXIST_IN_APP_STORE":                           47,
		"INVALID_FINAL_URL":                                          48,
		"INVALID_TRACKING_URL":                                       49,
		"INVALID_FINAL_URL_FOR_APP_DOWNLOAD_URL":                     50,
		"LIST_TOO_SHORT":                                             51,
		"INVALID_USER_ACTION":                                        52,
		"INVALID_TYPE_NAME":                                          53,
		"INVALID_EVENT_CHANGE_STATUS":                                54,
		"INVALID_SNIPPETS_HEADER":                                    55,
		"INVALID_ANDROID_APP_LINK":                                   56,
		"NUMBER_TYPE_WITH_CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY":    57,
		"RESERVED_KEYWORD_OTHER":                                     58,
		"DUPLICATE_OPTION_LABELS":                                    59,
		"DUPLICATE_OPTION_PREFILLS":                                  60,
		"UNEQUAL_LIST_LENGTHS":                                       61,
		"INCONSISTENT_CURRENCY_CODES":                                62,
		"PRICE_EXTENSION_HAS_DUPLICATED_HEADERS":                     63,
		"ITEM_HAS_DUPLICATED_HEADER_AND_DESCRIPTION":                 64,
		"PRICE_EXTENSION_HAS_TOO_FEW_ITEMS":                          65,
		"UNSUPPORTED_VALUE":                                          66,
		"INVALID_FINAL_MOBILE_URL":                                   67,
		"INVALID_KEYWORDLESS_AD_RULE_LABEL":                          68,
		"VALUE_TRACK_PARAMETER_NOT_SUPPORTED":                        69,
		"UNSUPPORTED_VALUE_IN_SELECTED_LANGUAGE":                     70,
		"INVALID_IOS_APP_LINK":                                       71,
		"MISSING_IOS_APP_LINK_OR_IOS_APP_STORE_ID":                   72,
		"PROMOTION_INVALID_TIME":                                     73,
		"PROMOTION_CANNOT_SET_PERCENT_OFF_AND_MONEY_AMOUNT_OFF":      74,
		"PROMOTION_CANNOT_SET_PROMOTION_CODE_AND_ORDERS_OVER_AMOUNT": 75,
		"TOO_MANY_DECIMAL_PLACES_SPECIFIED":                          76,
		"AD_CUSTOMIZERS_NOT_ALLOWED":                                 77,
		"INVALID_LANGUAGE_CODE":                                      78,
		"UNSUPPORTED_LANGUAGE":                                       79,
		"IF_FUNCTION_NOT_ALLOWED":                                    80,
		"INVALID_FINAL_URL_SUFFIX":                                   81,
		"INVALID_TAG_IN_FINAL_URL_SUFFIX":                            82,
		"INVALID_FINAL_URL_SUFFIX_FORMAT":                            83,
		"CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIRED":               84,
		"ONLY_ONE_DELIVERY_OPTION_IS_ALLOWED":                        85,
		"NO_DELIVERY_OPTION_IS_SET":                                  86,
		"INVALID_CONVERSION_REPORTING_STATE":                         87,
		"IMAGE_SIZE_WRONG":                                           88,
		"EMAIL_DELIVERY_NOT_AVAILABLE_IN_COUNTRY":                    89,
		"AUTO_REPLY_NOT_AVAILABLE_IN_COUNTRY":                        90,
		"INVALID_LATITUDE_VALUE":                                     91,
		"INVALID_LONGITUDE_VALUE":                                    92,
		"TOO_MANY_LABELS":                                            93,
		"INVALID_IMAGE_URL":                                          94,
		"MISSING_LATITUDE_VALUE":                                     95,
		"MISSING_LONGITUDE_VALUE":                                    96,
	}
)

func (x FeedItemValidationErrorEnum_FeedItemValidationError) Enum() *FeedItemValidationErrorEnum_FeedItemValidationError {
	p := new(FeedItemValidationErrorEnum_FeedItemValidationError)
	*p = x
	return p
}

func (x FeedItemValidationErrorEnum_FeedItemValidationError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeedItemValidationErrorEnum_FeedItemValidationError) Descriptor() protoreflect.EnumDescriptor {
	return file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_enumTypes[0].Descriptor()
}

func (FeedItemValidationErrorEnum_FeedItemValidationError) Type() protoreflect.EnumType {
	return &file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_enumTypes[0]
}

func (x FeedItemValidationErrorEnum_FeedItemValidationError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeedItemValidationErrorEnum_FeedItemValidationError.Descriptor instead.
func (FeedItemValidationErrorEnum_FeedItemValidationError) EnumDescriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_rawDescGZIP(), []int{0, 0}
}

// Container for enum describing possible validation errors of a feed item.
type FeedItemValidationErrorEnum struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *FeedItemValidationErrorEnum) Reset() {
	*x = FeedItemValidationErrorEnum{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedItemValidationErrorEnum) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedItemValidationErrorEnum) ProtoMessage() {}

func (x *FeedItemValidationErrorEnum) ProtoReflect() protoreflect.Message {
	mi := &file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedItemValidationErrorEnum.ProtoReflect.Descriptor instead.
func (*FeedItemValidationErrorEnum) Descriptor() ([]byte, []int) {
	return file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_rawDescGZIP(), []int{0}
}

var File_google_ads_googleads_v1_errors_feed_item_validation_error_proto protoreflect.FileDescriptor

var file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xb4, 0x19, 0x0a, 0x1b, 0x46, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x22,
	0x94, 0x19, 0x0a, 0x17, 0x46, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x0f, 0x0a, 0x0b, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x54, 0x52,
	0x49, 0x4e, 0x47, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x10, 0x02, 0x12,
	0x13, 0x0a, 0x0f, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4c, 0x4f,
	0x4e, 0x47, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x04, 0x12, 0x28, 0x0a,
	0x24, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x44, 0x4f, 0x4d, 0x45, 0x53, 0x54, 0x49,
	0x43, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x46,
	0x4f, 0x52, 0x4d, 0x41, 0x54, 0x10, 0x05, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10,
	0x06, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45,
	0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x10, 0x07, 0x12, 0x23, 0x0a,
	0x1f, 0x50, 0x52, 0x45, 0x4d, 0x49, 0x55, 0x4d, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x55,
	0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44,
	0x10, 0x08, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x49, 0x53, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44,
	0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x09, 0x12, 0x16,
	0x0a, 0x12, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x4f, 0x55, 0x54, 0x5f, 0x4f, 0x46, 0x5f, 0x52,
	0x41, 0x4e, 0x47, 0x45, 0x10, 0x0a, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x41, 0x4c, 0x4c, 0x54, 0x52,
	0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f,
	0x52, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59,
	0x10, 0x0b, 0x12, 0x2d, 0x0a, 0x29, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x46,
	0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x10,
	0x0c, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x0d, 0x12, 0x12, 0x0a, 0x0e, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x49, 0x44, 0x10, 0x0e, 0x12,
	0x21, 0x0a, 0x1d, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49,
	0x42, 0x55, 0x54, 0x45, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x53,
	0x10, 0x0f, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x49, 0x44, 0x10, 0x10, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53,
	0x10, 0x11, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x48, 0x54,
	0x54, 0x50, 0x53, 0x5f, 0x55, 0x52, 0x4c, 0x10, 0x12, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x49, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x41, 0x44,
	0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0x13, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x54, 0x41, 0x52, 0x54,
	0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x46, 0x54, 0x45, 0x52, 0x5f, 0x45, 0x4e, 0x44, 0x5f,
	0x44, 0x41, 0x54, 0x45, 0x10, 0x14, 0x12, 0x20, 0x0a, 0x1c, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e,
	0x47, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x53, 0x54, 0x41, 0x52,
	0x54, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x15, 0x12, 0x1e, 0x0a, 0x1a, 0x4d, 0x49, 0x53, 0x53,
	0x49, 0x4e, 0x47, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x45, 0x4e,
	0x44, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x16, 0x12, 0x18, 0x0a, 0x14, 0x4d, 0x49, 0x53, 0x53,
	0x49, 0x4e, 0x47, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x49, 0x44,
	0x10, 0x17, 0x12, 0x23, 0x0a, 0x1f, 0x56, 0x41, 0x4e, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x48, 0x4f,
	0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c,
	0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x18, 0x12, 0x24, 0x0a, 0x20, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4e, 0x49, 0x50, 0x50, 0x45, 0x54, 0x10, 0x19, 0x12, 0x19, 0x0a,
	0x15, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f,
	0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x10, 0x1a, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x10,
	0x1b, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x52, 0x49,
	0x43, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x10, 0x1c, 0x12, 0x1d, 0x0a, 0x19, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x48, 0x4f, 0x4c, 0x44,
	0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x10, 0x1d, 0x12, 0x2e, 0x0a, 0x2a, 0x4d, 0x49,
	0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4e, 0x48, 0x41, 0x4e, 0x43, 0x45, 0x44, 0x5f, 0x53,
	0x49, 0x54, 0x45, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x1e, 0x12, 0x26, 0x0a, 0x22, 0x52, 0x45,
	0x56, 0x49, 0x45, 0x57, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45,
	0x10, 0x1f, 0x12, 0x27, 0x0a, 0x23, 0x48, 0x59, 0x50, 0x48, 0x45, 0x4e, 0x53, 0x5f, 0x49, 0x4e,
	0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x4e, 0x49, 0x50, 0x50, 0x45, 0x54, 0x10, 0x20, 0x12, 0x2d, 0x0a, 0x29, 0x44,
	0x4f, 0x55, 0x42, 0x4c, 0x45, 0x5f, 0x51, 0x55, 0x4f, 0x54, 0x45, 0x53, 0x5f, 0x49, 0x4e, 0x5f,
	0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x4e, 0x49, 0x50, 0x50, 0x45, 0x54, 0x10, 0x21, 0x12, 0x26, 0x0a, 0x22, 0x51, 0x55,
	0x4f, 0x54, 0x45, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45,
	0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4e, 0x49, 0x50, 0x50, 0x45, 0x54,
	0x10, 0x22, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x46, 0x4f,
	0x52, 0x4d, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d,
	0x53, 0x10, 0x23, 0x12, 0x1e, 0x0a, 0x1a, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x55,
	0x52, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x10, 0x24, 0x12, 0x17, 0x0a, 0x13, 0x4e, 0x4f, 0x5f, 0x47, 0x45, 0x4f, 0x43, 0x4f, 0x44,
	0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x10, 0x25, 0x12, 0x28, 0x0a, 0x24,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x52,
	0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x45, 0x58, 0x54, 0x10, 0x26, 0x12, 0x2d, 0x0a, 0x29, 0x43, 0x41, 0x52, 0x52, 0x49, 0x45,
	0x52, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x43, 0x5f, 0x53, 0x48, 0x4f, 0x52, 0x54,
	0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f,
	0x57, 0x45, 0x44, 0x10, 0x27, 0x12, 0x20, 0x0a, 0x1c, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x48, 0x4f, 0x4c, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x49, 0x44, 0x10, 0x28, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x55, 0x52, 0x4c, 0x5f, 0x54, 0x41, 0x47, 0x10, 0x29, 0x12, 0x11, 0x0a, 0x0d,
	0x4c, 0x49, 0x53, 0x54, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4c, 0x4f, 0x4e, 0x47, 0x10, 0x2a, 0x12,
	0x22, 0x0a, 0x1e, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49,
	0x42, 0x55, 0x54, 0x45, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x42, 0x49, 0x4e, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x2b, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45,
	0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x53, 0x10, 0x2c, 0x12, 0x25, 0x0a, 0x21, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52,
	0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x2d,
	0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x57,
	0x49, 0x54, 0x48, 0x4f, 0x55, 0x54, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x52, 0x4c,
	0x53, 0x10, 0x2e, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x50, 0x50, 0x5f, 0x49, 0x44, 0x5f, 0x44, 0x4f,
	0x45, 0x53, 0x4e, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x50,
	0x50, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x10, 0x2f, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x52, 0x4c, 0x10, 0x30,
	0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43,
	0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x52, 0x4c, 0x10, 0x31, 0x12, 0x2a, 0x0a, 0x26, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x52, 0x4c, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44,
	0x5f, 0x55, 0x52, 0x4c, 0x10, 0x32, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x54,
	0x4f, 0x4f, 0x5f, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x10, 0x33, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x34, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x35, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x4e,
	0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x36, 0x12, 0x1b, 0x0a, 0x17, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x4e, 0x49, 0x50, 0x50, 0x45, 0x54, 0x53, 0x5f,
	0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x37, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x5f, 0x41, 0x50, 0x50, 0x5f,
	0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x38, 0x12, 0x3b, 0x0a, 0x37, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x54,
	0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x55, 0x50, 0x50,
	0x4f, 0x52, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52,
	0x59, 0x10, 0x39, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x53, 0x45, 0x52, 0x56, 0x45, 0x44, 0x5f,
	0x4b, 0x45, 0x59, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x3a, 0x12,
	0x1b, 0x0a, 0x17, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x53, 0x10, 0x3b, 0x12, 0x1d, 0x0a, 0x19,
	0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x50, 0x52, 0x45, 0x46, 0x49, 0x4c, 0x4c, 0x53, 0x10, 0x3c, 0x12, 0x18, 0x0a, 0x14, 0x55,
	0x4e, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x4c, 0x45, 0x4e, 0x47,
	0x54, 0x48, 0x53, 0x10, 0x3d, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x4e, 0x43, 0x4f, 0x4e, 0x53, 0x49,
	0x53, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x53, 0x10, 0x3e, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x52, 0x49, 0x43, 0x45, 0x5f,
	0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x48, 0x41, 0x53, 0x5f, 0x44, 0x55,
	0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x53,
	0x10, 0x3f, 0x12, 0x2e, 0x0a, 0x2a, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x48, 0x41, 0x53, 0x5f, 0x44,
	0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x48, 0x45, 0x41, 0x44, 0x45, 0x52,
	0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x40, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x52, 0x49, 0x43, 0x45, 0x5f, 0x45, 0x58, 0x54, 0x45,
	0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x48, 0x41, 0x53, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x46, 0x45,
	0x57, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x53, 0x10, 0x41, 0x12, 0x15, 0x0a, 0x11, 0x55, 0x4e, 0x53,
	0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x42,
	0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x46, 0x49, 0x4e, 0x41,
	0x4c, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x55, 0x52, 0x4c, 0x10, 0x43, 0x12, 0x25,
	0x0a, 0x21, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4b, 0x45, 0x59, 0x57, 0x4f, 0x52,
	0x44, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x44, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x5f, 0x4c, 0x41,
	0x42, 0x45, 0x4c, 0x10, 0x44, 0x12, 0x27, 0x0a, 0x23, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x54,
	0x52, 0x41, 0x43, 0x4b, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x10, 0x45, 0x12, 0x2a,
	0x0a, 0x26, 0x55, 0x4e, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x5f, 0x56, 0x41,
	0x4c, 0x55, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f,
	0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45, 0x10, 0x46, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x49, 0x4f, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x4c, 0x49,
	0x4e, 0x4b, 0x10, 0x47, 0x12, 0x2c, 0x0a, 0x28, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f,
	0x49, 0x4f, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x4f, 0x52, 0x5f,
	0x49, 0x4f, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x49, 0x44,
	0x10, 0x48, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f, 0x4d, 0x4f, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x49, 0x12, 0x39,
	0x0a, 0x35, 0x50, 0x52, 0x4f, 0x4d, 0x4f, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x4e, 0x4e,
	0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x4f,
	0x46, 0x46, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x41, 0x4d, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x4a, 0x12, 0x3e, 0x0a, 0x3a, 0x50, 0x52, 0x4f,
	0x4d, 0x4f, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45,
	0x54, 0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x4f, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x53, 0x5f, 0x4f, 0x56, 0x45, 0x52,
	0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x4b, 0x12, 0x25, 0x0a, 0x21, 0x54, 0x4f, 0x4f,
	0x5f, 0x4d, 0x41, 0x4e, 0x59, 0x5f, 0x44, 0x45, 0x43, 0x49, 0x4d, 0x41, 0x4c, 0x5f, 0x50, 0x4c,
	0x41, 0x43, 0x45, 0x53, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x4c,
	0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x44, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x49, 0x5a, 0x45,
	0x52, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x4d,
	0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4c, 0x41, 0x4e, 0x47,
	0x55, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x4e, 0x12, 0x18, 0x0a, 0x14, 0x55,
	0x4e, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x5f, 0x4c, 0x41, 0x4e, 0x47, 0x55,
	0x41, 0x47, 0x45, 0x10, 0x4f, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x46, 0x5f, 0x46, 0x55, 0x4e, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44,
	0x10, 0x50, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x46, 0x49,
	0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x52, 0x4c, 0x5f, 0x53, 0x55, 0x46, 0x46, 0x49, 0x58, 0x10, 0x51,
	0x12, 0x23, 0x0a, 0x1f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x41, 0x47, 0x5f,
	0x49, 0x4e, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x52, 0x4c, 0x5f, 0x53, 0x55, 0x46,
	0x46, 0x49, 0x58, 0x10, 0x52, 0x12, 0x23, 0x0a, 0x1f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x52, 0x4c, 0x5f, 0x53, 0x55, 0x46, 0x46, 0x49,
	0x58, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x10, 0x53, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x46,
	0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x54, 0x12, 0x27, 0x0a, 0x23,
	0x4f, 0x4e, 0x4c, 0x59, 0x5f, 0x4f, 0x4e, 0x45, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52,
	0x59, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x53, 0x5f, 0x41, 0x4c, 0x4c, 0x4f,
	0x57, 0x45, 0x44, 0x10, 0x55, 0x12, 0x1d, 0x0a, 0x19, 0x4e, 0x4f, 0x5f, 0x44, 0x45, 0x4c, 0x49,
	0x56, 0x45, 0x52, 0x59, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x53, 0x5f, 0x53,
	0x45, 0x54, 0x10, 0x56, 0x12, 0x26, 0x0a, 0x22, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52,
	0x54, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x57, 0x12, 0x14, 0x0a, 0x10,
	0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x5f, 0x57, 0x52, 0x4f, 0x4e, 0x47,
	0x10, 0x58, 0x12, 0x2b, 0x0a, 0x27, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x44, 0x45, 0x4c, 0x49,
	0x56, 0x45, 0x52, 0x59, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42,
	0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x10, 0x59, 0x12,
	0x27, 0x0a, 0x23, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x52, 0x45, 0x50, 0x4c, 0x59, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x10, 0x5a, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x4c, 0x41, 0x54, 0x49, 0x54, 0x55, 0x44, 0x45, 0x5f, 0x56, 0x41, 0x4c,
	0x55, 0x45, 0x10, 0x5b, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x4c, 0x4f, 0x4e, 0x47, 0x49, 0x54, 0x55, 0x44, 0x45, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10,
	0x5c, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x4f, 0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x59, 0x5f, 0x4c, 0x41,
	0x42, 0x45, 0x4c, 0x53, 0x10, 0x5d, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x52, 0x4c, 0x10, 0x5e, 0x12, 0x1a, 0x0a,
	0x16, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x4c, 0x41, 0x54, 0x49, 0x54, 0x55, 0x44,
	0x45, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x5f, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x49, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x5f, 0x4c, 0x4f, 0x4e, 0x47, 0x49, 0x54, 0x55, 0x44, 0x45, 0x5f, 0x56,
	0x41, 0x4c, 0x55, 0x45, 0x10, 0x60, 0x42, 0xf7, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x42, 0x1c, 0x46,
	0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x44, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67,
	0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x64, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x3b, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0xa2, 0x02, 0x03, 0x47, 0x41, 0x41, 0xaa, 0x02, 0x1e, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x41, 0x64, 0x73, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64, 0x73,
	0x2e, 0x56, 0x31, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0xca, 0x02, 0x1e, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x5c, 0x41, 0x64, 0x73, 0x5c, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64,
	0x73, 0x5c, 0x56, 0x31, 0x5c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0xea, 0x02, 0x22, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x41, 0x64, 0x73, 0x3a, 0x3a, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x41, 0x64, 0x73, 0x3a, 0x3a, 0x56, 0x31, 0x3a, 0x3a, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_rawDescOnce sync.Once
	file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_rawDescData = file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_rawDesc
)

func file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_rawDescGZIP() []byte {
	file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_rawDescOnce.Do(func() {
		file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_rawDescData)
	})
	return file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_rawDescData
}

var file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_goTypes = []interface{}{
	(FeedItemValidationErrorEnum_FeedItemValidationError)(0), // 0: google.ads.googleads.v1.errors.FeedItemValidationErrorEnum.FeedItemValidationError
	(*FeedItemValidationErrorEnum)(nil),                      // 1: google.ads.googleads.v1.errors.FeedItemValidationErrorEnum
}
var file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_init() }
func file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_init() {
	if File_google_ads_googleads_v1_errors_feed_item_validation_error_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedItemValidationErrorEnum); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_goTypes,
		DependencyIndexes: file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_depIdxs,
		EnumInfos:         file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_enumTypes,
		MessageInfos:      file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_msgTypes,
	}.Build()
	File_google_ads_googleads_v1_errors_feed_item_validation_error_proto = out.File
	file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_rawDesc = nil
	file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_goTypes = nil
	file_google_ads_googleads_v1_errors_feed_item_validation_error_proto_depIdxs = nil
}
