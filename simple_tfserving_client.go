package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// gRPC TensorFlow Serving 客户端
// 测试连通性和显示请求返回

type SimpleTFClient struct {
	conn      *grpc.ClientConn
	serverAddr string
}

func NewSimpleTFClient(serverAddr string) (*SimpleTFClient, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	log.Printf("正在连接 TensorFlow Serving: %s", serverAddr)
	conn, err := grpc.DialContext(ctx, serverAddr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithBlock(),
	)
	if err != nil {
		return nil, fmt.Errorf("连接失败: %v", err)
	}

	return &SimpleTFClient{conn: conn}, nil
}

func (c *SimpleTFClient) Close() {
	if c.conn != nil {
		c.conn.Close()
	}
}

// 获取模型元数据
func (c *SimpleTFClient) GetModelMetadata(modelName string) error {
	log.Printf("获取模型 %s 的元数据...", modelName)

	// 这里可以添加获取模型签名的代码
	// 目前先返回连接成功的信息
	log.Println("✅ 模型连接正常")
	return nil
}

// 预测请求的简化版本
func (c *SimpleTFClient) PredictSimple(modelName string, inputData interface{}) (interface{}, error) {
	log.Printf("发送预测请求到模型: %s", modelName)

	// 将输入数据序列化为 JSON
	inputJSON, err := json.Marshal(inputData)
	if err != nil {
		return nil, fmt.Errorf("序列化输入数据失败: %v", err)
	}

	log.Printf("输入数据: %s", string(inputJSON))

	// 模拟预测结果 (实际实现需要调用 gRPC 接口)
	// 这里返回一个示例结果
	result := map[string]interface{}{
		"predictions": []float64{0.85, 0.92, 0.78},
		"model_name":  modelName,
		"timestamp":   time.Now().Unix(),
	}

	log.Println("✅ 预测完成")
	return result, nil
}

func RunSimpleClient() {
	var serverAddr = flag.String("server_addr", "localhost:8500", "TensorFlow Serving 地址")
	var modelName = flag.String("model_name", "dnn_winr_v1", "模型名称")
	flag.Parse()

	log.Println("=== 简化版 TensorFlow Serving 客户端 ===")
	log.Printf("服务器: %s", *serverAddr)
	log.Printf("模型: %s", *modelName)

	// 创建客户端
	client, err := NewSimpleTFClient(*serverAddr)
	if err != nil {
		log.Fatalf("创建客户端失败: %v", err)
		log.Println("")
		log.Println("💡 请确保 TensorFlow Serving 正在运行:")
		log.Println("   docker run -p 8500:8500 -p 8501:8501 \\")
		log.Println("     --mount type=bind,source=/path/to/model,target=/models/model_name \\")
		log.Println("     -e MODEL_NAME=model_name -t tensorflow/serving")
		return
	}
	defer client.Close()

	log.Println("✅ 连接成功!")

	// 获取模型信息
	if err := client.GetModelMetadata(*modelName); err != nil {
		log.Printf("获取模型信息失败: %v", err)
	}

	// 准备示例输入数据
	inputData := map[string]interface{}{
		"instances": [][]float64{
			{1.0, 2.0, 3.0, 4.0, 5.0},
			{2.0, 3.0, 4.0, 5.0, 6.0},
		},
	}

	log.Println("📊 发送预测请求...")

	// 发送预测请求
	result, err := client.PredictSimple(*modelName, inputData)
	if err != nil {
		log.Fatalf("预测失败: %v", err)
	}

	// 显示结果
	log.Println("🎯 预测结果:")
	resultJSON, _ := json.MarshalIndent(result, "", "  ")
	fmt.Println(string(resultJSON))

	log.Println("")
	log.Println("📝 下一步完善:")
	log.Println("1. 添加真实的 gRPC 预测调用")
	log.Println("2. 实现正确的张量数据格式")
	log.Println("3. 添加错误处理和重试机制")
	log.Println("4. 支持批量预测")
}
